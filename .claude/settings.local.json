{"permissions": {"allow": ["mcp__ide__executeCode", "Bash(git add:*)", "Bash(git commit:*)", "Bash(rm:*)", "WebFetch(domain:treasuresofmaimi.com)", "Bash(npm run build:*)", "Bash(npm run types:generate:*)", "<PERSON>sh(vercel deploy:*)", "Bash(grep:*)", "Bash(npm run lint:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npx supabase:*)", "Bash(find:*)", "Bash(node:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(python3:*)", "Bash(ls:*)", "<PERSON><PERSON>(echo:*)", "WebFetch(domain:supabase.com)", "Bash(git push:*)", "Bash(git pull:*)", "Bash(psql:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(curl:*)", "Bash(kill:*)", "Bash(rg:*)", "Bash(npm run dev:*)", "Bash(npm install:*)"], "deny": []}}