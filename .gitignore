# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Sentry Config File
.env.sentry-build-plugin
.sentryclirc
sentry-wizard-installation-error-*.log

# Supabase
supabase/.temp/
supabase/.branches/

# IDE
.vscode/
.idea/
.windsurfrules

# Temporary files
*.tmp
*.temp
temp_file.txt
tmp_file.txt
page_end.txt
page_end_clean.txt
env-local-temp.txt
shop-maimi@0.1.0

# Database dumps
*.sql.gz
*.dump

# Backup files
*.backup
*.bak

# Package manager lock files (keep package-lock.json)
yarn.lock
pnpm-lock.yaml
