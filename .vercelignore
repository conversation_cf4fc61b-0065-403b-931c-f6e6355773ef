# Ignore development and system files
.git
.github
.vscode
.idea
**/.DS_Store
**/node_modules

# Ignore build artifacts
.next
out
dist
build
coverage

# Ignore debug files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Ignore local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ignore other unnecessary files
README.md
LICENSE
**/*.spec.js
**/*.test.js
**/*.spec.ts
**/*.test.ts
scripts 