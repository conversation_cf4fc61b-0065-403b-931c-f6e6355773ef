# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands
- `npm run dev` - Start development server on localhost:3000
- `npm run dev-fast` - Start development with Turbo mode enabled
- `npm run dev-debug` - Start development with Node.js debugger
- `npm run build` - Build production application
- `npm run start` - Start production server
- `npm run lint` - Run ESLint for code quality checks
- `npm run types:generate` - Generate TypeScript types from Supabase schema

### Database Commands
- `npx supabase gen types typescript --project-id vfnihmcppowqkjytozwv > src/lib/database.types.ts` - Regenerate database types
- Apply migrations located in `supabase/migrations/` directory to Supabase project

## Architecture Overview

### Stack
- **Frontend**: Next.js 13.4.19 with App Router, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase (PostgreSQL + Auth + Storage)
- **Payments**: PayPal SDK, Stripe
- **Deployment**: Vercel with Sentry monitoring
- **Image Management**: Cloudinary integration

### Key Architectural Patterns

#### Authentication & Authorization (Updated July 2025)
- **Primary**: Email-based registration and authentication via Supabase Auth
- **Security**: Row Level Security (RLS) policies enforced at database level
- **Admin Access**: Controlled via `profiles.is_admin` boolean flag
- **Profile Management**: Automatic profile creation on user registration via `ensureUserProfile()`
- **Middleware**: `src/middleware.ts` handles route protection with session caching to prevent rate limiting
- **Rate Limiting**: Supabase auth rate limit of 30 requests per 5 minutes per IP
- **Phone Auth**: Removed due to SMS provider complexity - now phone numbers are optional for notifications only
- **International**: Full global support for email authentication

#### Database Schema
- **Core Tables**: `profiles`, `products`, `categories`, `orders`, `order_items`, `cart_items`, `shipping_addresses`
- **Security**: All tables have RLS enabled with granular policies
- **Order Management**: Order statuses: `pending` (awaiting payment), `processing` (payment confirmed), `shipped`, `delivered`, `cancelled`
- **Analytics**: `product_views` table tracks user engagement
- **Notifications**: Comprehensive system with `notifications`, `push_subscriptions`, `admin_notification_preferences`
- **Media**: `product_media` table for product images and galleries with Cloudinary URLs
- **Discounts**: `discount_codes` table with usage tracking and validation

#### API Architecture
- RESTful API routes in `src/app/api/`
- Admin routes protected with `isAdmin()` checks
- Unified notification system across email, SMS, WhatsApp, and push
- Webhook handlers for Stripe and PayPal payments
- Product analytics tracking with debounced view recording

#### File Organization
```
src/
├── app/                 # Next.js App Router pages and API routes
│   ├── admin/          # Admin dashboard pages (protected)
│   ├── api/            # API endpoints
│   └── [pages]/        # Public pages
├── components/         # Reusable React components
│   ├── admin/         # Admin-specific components
│   ├── analytics/     # Analytics visualization components
│   └── ui/            # Base UI components
├── lib/               # Utility functions and configurations
└── types/             # TypeScript type definitions
```

### Critical Security Features
- Database-level security with mandatory RLS policies
- Admin token management with forced RLS
- Input validation on all API endpoints
- Secure environment variable handling
- CSRF protection via Next.js built-ins

## Development Guidelines

### Database Operations
- Always use typed Supabase client: `supabase: TypedSupabaseClient`
- Use utility functions in `src/lib/supabase.ts` for common operations
- Check user permissions with `isAdmin()` before admin operations
- Follow RLS policies - don't attempt to bypass them
- **Critical**: When updating order status, always include condition checks (e.g., `.eq('status', 'pending')`) to prevent overwriting confirmed orders

### Admin Panel Development
- Admin routes require authentication check in layout
- Use consistent UI patterns from existing admin components
- Implement real-time updates where appropriate (orders, notifications)
- Follow notification system patterns for admin alerts

### Product Management
- Product images managed through Cloudinary with fallback URLs
- Product analytics automatically tracked via `product-analytics.ts`
- Use `fetchProductBySlug()` for single product retrieval
- Product media handled separately in `product_media` table

### Payment Integration (Updated July 2025)
- **Payment Providers**: PayPal and Stripe with full webhook verification
- **Critical Payment Flow**: 
  - Order created → `'pending'` status with `'awaiting_payment'` payment_status
  - Payment completed → `'processing'` status with `'completed'` payment_status  
  - Payment failed/abandoned → `'cancelled'` status with `'failed'` payment_status
- **Security**: Orders only show as "processing" after confirmed payment to prevent shipping unpaid orders
- **Webhooks**: Stripe webhooks handle `checkout.session.completed`, `checkout.session.expired`, `payment_intent.payment_failed`
- **PayPal Integration**: `/api/paypal/create-order` and `/api/paypal/capture-order` endpoints
- **Stripe Integration**: `/api/stripe/create-checkout` and `/api/stripe/webhook` endpoints
- **Retry Payments**: Support for retry payment on cancelled/pending orders
- **Shipping**: Location-based calculations (Valencia, EU, US, International)
- **Discounts**: Coupon system with Stripe integration and validation

### Notification System (Updated July 2025)
- **Status**: Email notifications fully working globally, SMS ready but needs Twilio upgrade
- **Architecture**: Unified notification service supports email, SMS, WhatsApp, and push channels
- **Current Implementation**: 
  - ✅ Email via Resend API (production ready)
  - ⚠️ SMS via Twilio (blocked by trial account - needs $20 upgrade)
  - ✅ Phone number collection in user profiles (optional)
  - ✅ International support (email working, SMS ready)
- **User Experience**: Email-first authentication, phone numbers optional for notifications
- **Files**: `email-notifications.ts`, `sms-notifications.ts`, `unified-notifications.ts`
- **Test Scripts**: `test-email-notification.js`, `test-order-sms.js` 
- **Upgrade Plan**: See `NOTIFICATION_SYSTEM_STATUS.md` for SMS activation steps

## Common Patterns

### Error Handling
- Use Sentry for production error tracking
- Implement try-catch in all async operations
- Return consistent error responses from API routes

### State Management
- React Context for wishlist and auth state
- SWR for data fetching and caching
- Local state for UI components

### Type Safety
- Generate types from Supabase schema regularly
- Use Database['public']['Tables'] types for table operations
- Maintain type definitions in `src/types/`

## Environment Configuration

Required environment variables:
- `NEXT_PUBLIC_SUPABASE_URL` and `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY` for admin operations
- PayPal: `NEXT_PUBLIC_PAYPAL_CLIENT_ID`, `PAYPAL_CLIENT_SECRET`
- Stripe: `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`, `STRIPE_SECRET_KEY`, `STRIPE_WEBHOOK_SECRET`
- Cloudinary: `NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME`, `CLOUDINARY_API_KEY`, `CLOUDINARY_API_SECRET`
- Sentry: `SENTRY_DSN`, `NEXT_PUBLIC_SENTRY_DSN`
- **Notifications (July 2025)**:
  - `RESEND_API_KEY` - Email notifications (✅ working)
  - `TWILIO_ACCOUNT_SID`, `TWILIO_AUTH_TOKEN`, `TWILIO_PHONE_NUMBER` - SMS (⚠️ needs upgrade)

## Testing & Quality Assurance

### Comprehensive Test Suite
- **Primary Test**: `test-e-commerce-comprehensive.js` - Full end-to-end e-commerce flow testing
- **Payment-Specific Tests**: `test-payment-flow-fix.js`, `test-webhook-endpoints-live.js`, `test-real-user-flow.js`
- **Usage**: `node test-e-commerce-comprehensive.js` (requires dev environment)

### Test Coverage Areas
The comprehensive test suite covers:

#### ✅ **Core Functionality (19/28 tests passing)**
- **User Management**: Registration, authentication, session handling, profile management
- **Product Operations**: Category setup, product search, admin product queries  
- **Shipping**: Address creation, multiple addresses, default address logic
- **Admin Dashboard**: Privilege checks, order queries, customer management
- **Security**: Row Level Security (RLS), SQL injection prevention
- **Performance**: Query optimization, response times under 100ms
- **Error Handling**: Non-existent resource handling, proper error responses

#### ⚠️ **Known Issues Identified by Tests**
- **Product RLS Policy**: Regular users cannot create products (admin-only operation) ✓ Expected
- **Data Validation**: Some constraints may not be enforced at database level
- **Foreign Key Enforcement**: System allows some invalid references
- **Duplicate Prevention**: Slug uniqueness not enforced in all cases

#### 🔧 **Test Categories**
1. **Authentication & Authorization**: User registration, login, session management
2. **Product Management**: CRUD operations, search, categorization
3. **Shopping Cart**: Add/remove items, quantity updates, calculations
4. **Order Processing**: Creation, status transitions, payment integration
5. **Payment Webhooks**: Stripe integration, status updates, error handling
6. **Admin Operations**: Dashboard queries, user management, product oversight
7. **Security Features**: RLS policies, input validation, injection prevention
8. **Performance**: Query speed, bulk operations, indexing effectiveness

### Running Tests
```bash
# Full comprehensive test suite
node test-e-commerce-comprehensive.js

# Payment-specific tests
node test-payment-flow-fix.js        # Database operations
node test-webhook-endpoints-live.js  # Live webhook testing
node test-real-user-flow.js         # End-to-end user scenarios
```

### Testing Payment Flows
- Always test both successful payment and cancellation scenarios
- Verify order status transitions: pending → processing (success) or pending → cancelled (failure)
- Test webhook endpoints with running development server
- Validate metadata passing between Stripe sessions and database

### Test Results Interpretation

#### Expected Test Outcomes
- **67.9% Success Rate** (19/28 tests) is acceptable for current system state
- **Product Creation Failures**: Expected for non-admin users (security feature working correctly)
- **RLS Policy Blocks**: Indicates security policies are active and working
- **Performance Tests**: Sub-100ms query responses indicate good database optimization

#### Critical Success Indicators
- ✅ **Authentication Flow**: All user registration and login tests must pass
- ✅ **Payment Security**: Order status transitions working correctly
- ✅ **RLS Security**: Unauthorized data access properly blocked
- ✅ **Admin Operations**: Dashboard queries functional
- ✅ **Data Integrity**: Shipping addresses and user profiles created correctly

#### Test Maintenance
- Update test database schema references when migrations are applied
- Modify RLS expectations when security policies change
- Adjust performance thresholds based on infrastructure changes
- Add new test cases when implementing features like discount codes or inventory management

#### Continuous Integration
- Tests designed to be self-contained with automatic cleanup
- Exit code 0 for success, 1 for failure (CI/CD compatible)
- Detailed logging for debugging failed scenarios
- No external dependencies beyond environment variables

### Security Considerations
- All database migrations include comprehensive RLS policies
- Critical security audit completed June 2025 (see security audit files)
- Admin access strictly controlled through database-level permissions
- Input validation implemented on all user-facing endpoints
- **Payment Security**: Orders cannot be marked as "processing" without payment confirmation to prevent shipping unpaid orders

### Performance Monitoring
- Sentry integration for error tracking and performance monitoring
- Product view analytics for business insights
- Cloudinary for optimized image delivery
- Bundle splitting configured for optimal loading
- Rate limit monitoring for Supabase authentication (30 requests/5min/IP)

## Troubleshooting Common Issues

### Authentication Rate Limiting
- **Problem**: "Authentication rate limit exceeded" errors in development
- **Cause**: Too many rapid auth requests (MCP servers, middleware, frequent logins)
- **Solution**: 
  - Kill any running MCP Supabase processes: `pkill -f "mcp-server-supabase"`
  - Wait 5-15 minutes for rate limit to reset
  - Use incognito browser or clear browser storage
  - Temporarily disable middleware auth checks if needed

### Payment Flow Issues
- **Problem**: Orders showing as "pending" but payment not completed
- **Root Cause**: Payment webhook failed or order status not properly updated
- **Solution**: Check webhook logs, verify order status transitions, ensure webhooks are receiving session metadata
- **Prevention**: Always include order_id in Stripe/PayPal session metadata

### Middleware Performance  
- **Problem**: Slow page loads or repeated auth requests
- **Cause**: Middleware making auth checks on every request
- **Solution**: Implemented session caching (30s) and admin status caching (60s) to reduce Supabase requests
- **Current Status**: ✅ **ENABLED** with rate limiting protection - all protected routes are now secured

## Critical Security Restoration (July 2025)

### ⚠️ **IMPORTANT**: Middleware Security Status
- **Status**: ✅ **ACTIVE** and protecting all routes
- **Issue Resolved**: Middleware was temporarily disabled due to rate limiting - now re-enabled with proper caching
- **Protection Level**: Full authentication and authorization enforcement

### Route Protection Details
- **Admin Routes** (`/admin/*`): Requires authentication + admin privileges
- **Checkout Routes** (`/checkout/*`): Requires user authentication  
- **Account Routes** (`/account/*`): Requires user authentication
- **Public Routes** (`/`, `/product/*`): Open access
- **Auth Routes** (`/auth/*`): Accessible to unauthenticated users

### Caching Implementation
- **Session Cache**: 30 seconds to reduce auth API calls
- **Admin Status Cache**: 60 seconds to reduce database queries
- **Cache Cleanup**: Automatic cleanup of expired entries
- **Timeout Protection**: 3-second timeout with graceful fallback

### Security Verification
```bash
# Test protected routes return 307 redirects
curl -I http://localhost:3000/admin      # → /auth/admin/login
curl -I http://localhost:3000/checkout   # → /auth/login
curl -I http://localhost:3000/           # → 200 OK
```