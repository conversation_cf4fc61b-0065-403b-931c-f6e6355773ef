# Mai Mi Deployment Checklist

## ✅ Product Media Table

The `product_media` table has been successfully created in your database. This enables multiple image uploads for products in your admin interface.

## 🔄 Environment Variables

Ensure your deployment environment has these essential environment variables:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
```

## 💻 Optional RPC Function

For improved SQL execution in the admin interface, consider adding this helper function to your Supabase SQL editor:

```sql
-- Create the execute_sql function for direct SQL execution
CREATE OR REPLACE FUNCTION execute_sql(query text)
RETURNS json AS $$
DECLARE
  result json;
BEGIN
  EXECUTE query INTO result;
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'error', SQLERRM,
    'detail', SQLSTATE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 🧪 Testing

Before final deployment, test these critical features:

1. **Single Image Upload**: Add a product with a single image
2. **Multiple Image Upload**: Upload multiple images to an existing product
3. **Image Management**: Remove images and set a different image as the main product image
4. **Product Search & Filtering**: Verify category and search functionality

## 🚀 Deployment

1. Commit all changes to your repository
2. Deploy your application to your hosting platform
3. After deployment, verify that image uploads work correctly in production

## 🔧 Troubleshooting

If you encounter issues with image uploads in production:

1. Check browser console for errors
2. Verify Cloudinary credentials are correct
3. Confirm the product_media table exists and has the correct permissions
4. Test admin authentication to ensure proper access
