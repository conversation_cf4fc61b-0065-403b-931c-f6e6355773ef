# Mai Mi Pre-Deployment Checklist

## Database
- [x] `product_media` table exists and is properly configured
- [ ] RLS policies are correctly set up for all tables
- [ ] Appropriate indexes are created for performance
- [ ] All required functions like `execute_sql` are created if needed

## Authentication
- [ ] Admin users are properly set up in the profiles table
- [ ] Authentication middleware is working correctly
- [ ] Sessions persist appropriately
- [ ] Protected routes are properly secured

## Environment Variables
Ensure these variables are properly set in Vercel:
- [ ] `NEXT_PUBLIC_SUPABASE_URL`
- [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- [ ] `SUPABASE_SERVICE_ROLE_KEY`
- [ ] `NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME`
- [ ] `CLOUDINARY_API_KEY`
- [ ] `CLOUDINARY_API_SECRET`

## Testing Functionality
- [ ] Product creation works
- [ ] Multiple image uploads work
- [ ] Categories can be managed
- [ ] User authentication works
- [ ] Admin privileges work correctly
- [ ] Image rendering works correctly

## Performance & SEO
- [ ] Images are optimized
- [ ] Next.js image optimization is working
- [ ] Metadata is set up for SEO
- [ ] Caching headers are configured

## Domain & HTTPS
- [ ] Custom domain is configured in Vercel
- [ ] DNS records are set up correctly
- [ ] HTTPS is enabled and working

## Post-Deployment Tasks
- [ ] Verify all functionality works in production
- [ ] Check for any console errors
- [ ] Test on mobile devices
- [ ] Verify image uploads in production
- [ ] Check admin interface functionality
