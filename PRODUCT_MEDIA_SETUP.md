# Setting Up Product Media in Supabase

This guide explains how to set up multiple image support for products in your Mai Mi application.

## Creating the Product Media Table

1. **Open your Supabase project dashboard**

2. **Navigate to the SQL Editor**
   - In the left sidebar, click on "SQL Editor"
   - Click "New Query" to create a new SQL query

3. **Copy and paste the contents of `product_media_setup.sql`**
   - The file contains all necessary SQL commands to create:
     - The `product_media` table
     - Proper relationships to the products table
     - Row-level security policies
     - Indexes for performance
     - Triggers for automatic timestamp updating

4. **Run the SQL query**
   - Click the "Run" button to execute the SQL commands
   - You should see confirmation that the queries executed successfully

5. **Verify the table was created**
   - Go to "Table Editor" in the sidebar
   - You should see the `product_media` table listed
   - Click on it to verify the structure matches what you expect

## Usage in the Application

The application is already configured to use this table for storing multiple images per product. Once the table is created:

1. Product administrators can upload multiple images for each product
2. The first image will automatically be set as the main product image
3. All images will be displayed in the product gallery
4. 3D models and AR experiences can also be generated and stored in this table

## Testing

After setup, test the functionality by:

1. Going to the product edit page in the admin interface
2. Uploading multiple images for a product
3. Verifying all images appear in the gallery
4. Testing the "Set as main image" functionality

## Troubleshooting

If you encounter issues:

- Check the Supabase logs for any SQL errors
- Verify that RLS policies are properly set up
- Ensure your admin user has the proper permissions
- Check for any browser console errors when uploading images
