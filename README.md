# 🛍️ Shop-Maimi

A luxury vintage fashion e-commerce platform built with Next.js and Supabase, specializing in authentic designer handbags and accessories.

![Shop-Maimi](public/images/logo.png)

## ✨ Features

### 🛒 E-commerce Core
- **Product Catalog**: Browse luxury vintage items with detailed galleries
- **Shopping Cart**: Add, remove, and manage items with real-time updates
- **Secure Checkout**: Multi-step checkout with address management
- **Payment Processing**: PayPal and Stripe integration
- **Order Management**: Complete order tracking and history

### 👤 User Experience
- **Authentication**: Secure user registration and login
- **User Profiles**: Manage personal information and addresses
- **Wishlist**: Save favorite items for later
- **Order History**: Track past purchases and order status

### 🔧 Admin Dashboard
- **Product Management**: Add, edit, and manage inventory
- **Order Processing**: View and update order statuses
- **Customer Management**: User account oversight
- **Analytics**: Sales and product view tracking
- **Discount Codes**: Create and manage promotional codes

### 🌍 Advanced Features
- **Shipping Calculator**: Location-based shipping rates (Valencia, Spain, EU, US, International)
- **Responsive Design**: Optimized for all devices
- **SEO Optimized**: Meta tags, structured data, and sitemaps
- **Performance Monitoring**: Error tracking with Sentry

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Custom components with Framer Motion animations
- **State Management**: React Context API

### Backend
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage for images
- **API**: Next.js API Routes

### Payments & Services
- **Payment Processing**: PayPal SDK, Stripe
- **Image Management**: Cloudinary integration
- **Error Tracking**: Sentry
- **Email**: Supabase Edge Functions

### Deployment
- **Hosting**: Vercel
- **Database**: Supabase Cloud
- **CDN**: Vercel Edge Network

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account
- PayPal Developer account (optional)
- Stripe account (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Ola-Yeenca/shop-maimi.git
   cd shop-maimi
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Fill in your environment variables (see [Environment Variables](#environment-variables) section)

4. **Set up Supabase**
   - Create a new Supabase project
   - Run the migrations in the `supabase/migrations` folder
   - Set up Row Level Security policies

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔐 Environment Variables

Create a `.env.local` file in the root directory:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# PayPal (Optional)
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret

# Stripe (Optional)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Cloudinary (Optional)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Sentry (Optional)
SENTRY_DSN=your_sentry_dsn
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 📁 Project Structure

```
shop-maimi/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── admin/             # Admin dashboard
│   │   ├── api/               # API routes
│   │   ├── auth/              # Authentication pages
│   │   ├── cart/              # Shopping cart
│   │   ├── checkout/          # Checkout process
│   │   └── products/          # Product pages
│   ├── components/            # Reusable components
│   │   ├── admin/             # Admin-specific components
│   │   ├── paypal/            # PayPal integration
│   │   ├── stripe/            # Stripe integration
│   │   └── ui/                # UI components
│   ├── lib/                   # Utility functions
│   └── types/                 # TypeScript type definitions
├── public/                    # Static assets
│   └── images/                # Product images
├── supabase/                  # Database migrations
└── docs/                      # Documentation
```

## 🗄️ Database Schema

The application uses Supabase with the following main tables:

- **products**: Product catalog
- **categories**: Product categories
- **cart_items**: Shopping cart items
- **orders**: Order information
- **order_items**: Individual order items
- **shipping_addresses**: User shipping addresses
- **profiles**: User profiles
- **discount_codes**: Promotional codes

## 🔒 Security Features

- **Row Level Security (RLS)**: Database-level security policies
- **Authentication**: Secure user authentication with Supabase
- **Input Validation**: Server-side validation for all inputs
- **CSRF Protection**: Built-in Next.js CSRF protection
- **Environment Variables**: Sensitive data stored securely

## 🚀 Deployment

### Vercel Deployment

1. **Connect to Vercel**
   ```bash
   npm install -g vercel
   vercel
   ```

2. **Set Environment Variables**
   Add all environment variables in the Vercel dashboard

3. **Deploy**
   ```bash
   vercel --prod
   ```

### Manual Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start the production server**
   ```bash
   npm start
   ```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js** - The React framework for production
- **Supabase** - The open source Firebase alternative
- **Tailwind CSS** - A utility-first CSS framework
- **Vercel** - Platform for frontend frameworks and static sites

## 📞 Support

For support, email <EMAIL> or create an issue in this repository.

---

**Shop-Maimi** - Curating luxury vintage fashion with modern technology 💎
