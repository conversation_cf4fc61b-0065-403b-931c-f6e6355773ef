# Security Testing Checklist
**Date:** June 17, 2025  
**Project:** Shop-Maimi Database Security Fixes

## Pre-Migration Testing

### 1. Backup Verification ✅
- [ ] Full database backup created
- [ ] Backup restoration tested
- [ ] Rollback plan documented

### 2. Current Access Audit
- [ ] List all admin users and verify legitimacy
- [ ] Check for any existing admin tokens
- [ ] Document current user permissions

## Migration Testing (Staging Environment)

### 3. Apply Migrations
- [ ] Apply `20250617_security_audit_fix_critical_rls.sql`
- [ ] Apply `20250617_security_audit_fix_permissions.sql`
- [ ] Apply `20250617_security_audit_add_indexes.sql`
- [ ] Verify no migration errors

### 4. RLS Policy Testing

#### Critical Tables (Must Pass)
- [ ] **admin_tokens**: Non-admin users cannot access
- [ ] **admin_tokens**: Admin users can access all tokens
- [ ] **profiles**: Users can only see their own profile
- [ ] **profiles**: Admins can see all profiles
- [ ] **store_settings**: Public can read, only admins can write
- [ ] **shipping_addresses**: Users can only see their own addresses

#### User-Specific Tables
- [ ] **cart_items**: Users can only access their own items
- [ ] **wishlists**: Users can only access their own wishlists
- [ ] **wardrobe_items**: Users can only access their own items
- [ ] **orders**: Users can only view their own orders
- [ ] **order_items**: Users can only view items from their orders

#### Public Tables
- [ ] **products**: Anyone can view, only admins can modify
- [ ] **categories**: Anyone can view, only admins can modify
- [ ] **collections**: Anyone can view, only admins can modify
- [ ] **product_media**: Anyone can view, only admins can modify

#### Analytics Tables
- [ ] **product_views**: Users can view their own, admins can view all
- [ ] **contact_messages**: Only admins can view all messages

## Security Test Cases

### 5. Unauthorized Access Tests
```sql
-- Test 1: Non-admin trying to access admin_tokens (should fail)
SELECT * FROM admin_tokens;

-- Test 2: User trying to access other user's profile (should fail)
SELECT * FROM profiles WHERE id != auth.uid();

-- Test 3: User trying to access other user's orders (should fail)
SELECT * FROM orders WHERE user_id != auth.uid();

-- Test 4: User trying to update store settings (should fail)
UPDATE store_settings SET store_name = 'Hacked Store';

-- Test 5: User trying to modify other user's cart (should fail)
UPDATE cart_items SET quantity = 999 WHERE user_id != auth.uid();
```

### 6. Order Security Tests
```sql
-- Test 1: User trying to update order total (should fail)
UPDATE orders SET total_amount = 0.01 WHERE user_id = auth.uid();

-- Test 2: User trying to insert order items directly (should fail)
INSERT INTO order_items (order_id, product_id, quantity, price) 
VALUES ('...', '...', 1, 0.01);

-- Test 3: Secure order creation function works
SELECT create_order_with_items(
  100.00,
  'shipping-address-id',
  'payment-intent-id',
  'stripe',
  '[{"product_id": "product-id", "quantity": 1, "price": 100.00}]'::jsonb
);
```

### 7. Admin Function Tests
```sql
-- Test 1: Admin can view all data
SELECT COUNT(*) FROM profiles; -- Should return all profiles
SELECT COUNT(*) FROM orders;   -- Should return all orders
SELECT COUNT(*) FROM admin_tokens; -- Should return all tokens

-- Test 2: Admin can update orders
UPDATE orders SET status = 'processing' WHERE id = 'some-order-id';

-- Test 3: Admin can manage products
INSERT INTO products (name, price, slug) VALUES ('Test Product', 100.00, 'test');
```

### 8. Performance Tests
```sql
-- Test 1: Profile admin check performance
EXPLAIN ANALYZE SELECT * FROM profiles WHERE is_admin = true;

-- Test 2: User order lookup performance
EXPLAIN ANALYZE SELECT * FROM orders WHERE user_id = auth.uid();

-- Test 3: Product catalog performance
EXPLAIN ANALYZE SELECT * FROM products WHERE status = 'active';
```

## Application Testing

### 9. Frontend Integration Tests
- [ ] User registration/login still works
- [ ] User profile updates work
- [ ] Shopping cart functionality works
- [ ] Order placement works (using secure function)
- [ ] Wishlist functionality works
- [ ] Admin panel shows all data for admins
- [ ] Admin panel restricted for non-admins

### 10. API Endpoint Tests
- [ ] `/api/products` - Public access works
- [ ] `/api/cart` - User-specific access works
- [ ] `/api/orders` - User-specific access works
- [ ] `/api/admin/*` - Admin-only access enforced
- [ ] `/api/profiles` - User-specific access works

## Post-Migration Monitoring

### 11. Error Monitoring (First 24 Hours)
- [ ] Monitor application error logs
- [ ] Check for RLS policy violations
- [ ] Verify no legitimate user access is blocked
- [ ] Monitor database performance metrics

### 12. Security Monitoring (Ongoing)
- [ ] Set up alerts for admin token access
- [ ] Monitor failed RLS policy checks
- [ ] Track unusual query patterns
- [ ] Set up audit log monitoring

## Rollback Procedures

### 13. If Issues Found
- [ ] Document the specific issue
- [ ] Determine if it's a security risk
- [ ] If high risk: Immediate rollback
- [ ] If low risk: Schedule fix for next deployment
- [ ] Update testing procedures based on findings

## Sign-Off

### 14. Approval Required
- [ ] Security team approval
- [ ] Database administrator approval
- [ ] Application team approval
- [ ] Product owner approval

---

## Test Results Template

### Migration Applied Successfully: ✅/❌
**Date:** ___________  
**Applied by:** ___________  
**Issues found:** ___________

### Critical Security Tests: ✅/❌
**admin_tokens protection:** ✅/❌  
**profiles protection:** ✅/❌  
**order manipulation prevention:** ✅/❌  
**user data isolation:** ✅/❌  

### Performance Impact: ✅/❌
**Query performance acceptable:** ✅/❌  
**Index utilization confirmed:** ✅/❌  
**No significant slowdown:** ✅/❌  

### Application Functionality: ✅/❌
**User workflows working:** ✅/❌  
**Admin workflows working:** ✅/❌  
**API endpoints working:** ✅/❌  

**Overall Result:** ✅/❌  
**Approved for Production:** ✅/❌  

**Approved by:**  
Security Team: ___________ Date: ___________  
DBA: ___________ Date: ___________  
Dev Team: ___________ Date: ___________  
Product Owner: ___________ Date: ___________