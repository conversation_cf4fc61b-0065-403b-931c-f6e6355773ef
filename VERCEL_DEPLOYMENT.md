# Deploying Mai Mi to Vercel with Custom Domain

## Step 1: Create a Vercel Account
If you don't already have one, sign up at [vercel.com](https://vercel.com)

## Step 2: Install Vercel CLI
```bash
npm install -g vercel
```

## Step 3: Prepare Your Repository
Git commit your latest changes:
```bash
git add .
git commit -m "Prepare for deployment"
git push
```

## Step 4: Deploy with Vercel CLI
From your project directory:
```bash
vercel login
vercel
```

During the setup, Vercel will ask you several questions:
- Set up and deploy: Yes
- Link to existing project: No
- Project name: treasures-of-maimi
- Framework preset: Next.js
- Root directory: ./
- Want to override settings: Yes
- Build command: npm run build
- Output directory: .next
- Development command: npm run dev
- Environment variables: Add all from your .env.local file

## Step 5: Set Up Your Custom Domain
After deployment:
1. Go to your Vercel dashboard
2. Select your project
3. Click "Domains"
4. Add your domain: treasuresofmaimi.com
5. Vercel will give you DNS records to add in your Squarespace domain settings

## Step 6: Configure DNS at Squarespace
1. Log in to your Squarespace account
2. Go to Home → Settings → Domains → treasuresofmaimi.com → Advanced settings
3. Add the DNS records Vercel provided:
   - Type: A
   - Name: @
   - Value: 76.76.21.21
   - Add another record
   - Type: CNAME
   - Name: www
   - Value: cname.vercel-dns.com.

## Step 7: Verify Deployment
Wait for DNS propagation (can take up to 48 hours), then visit your site at treasuresofmaimi.com

## Environment Variables
Make sure these are set in your Vercel project settings:
- NEXT_PUBLIC_SUPABASE_URL
- NEXT_PUBLIC_SUPABASE_ANON_KEY
- SUPABASE_SERVICE_ROLE_KEY
- CLOUDINARY_CLOUD_NAME
- CLOUDINARY_API_KEY
- CLOUDINARY_API_SECRET

## Continuous Deployment
Any future commits to your main branch will automatically trigger a new deployment.

## Deployment Preview
While waiting for DNS propagation, you can access your site using the Vercel preview URL (e.g., https://treasures-of-maimi.vercel.app)
