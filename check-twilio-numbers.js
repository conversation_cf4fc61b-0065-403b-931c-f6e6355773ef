// Check available Twilio phone numbers
// Run with: node check-twilio-numbers.js

// Load environment variables
require('dotenv').config();

const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN;

// Validate required environment variables
if (!TWILIO_ACCOUNT_SID) {
  console.error('❌ TWILIO_ACCOUNT_SID environment variable is required');
  console.error('💡 Add TWILIO_ACCOUNT_SID=your_account_sid to your .env.local file');
  process.exit(1);
}

if (!TWILIO_AUTH_TOKEN) {
  console.error('❌ TWILIO_AUTH_TOKEN environment variable is required');
  console.error('💡 Add TWILIO_AUTH_TOKEN=your_auth_token to your .env.local file');
  process.exit(1);
}

const checkTwilioNumbers = async () => {
  try {
    console.log('🔍 Checking your Twilio phone numbers...\n');
    
    const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/IncomingPhoneNumbers.json`, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`).toString('base64')}`,
      },
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Successfully retrieved phone numbers!');
      console.log(`📊 Total numbers: ${result.incoming_phone_numbers.length}\n`);
      
      if (result.incoming_phone_numbers.length > 0) {
        result.incoming_phone_numbers.forEach((number, index) => {
          console.log(`📱 Number ${index + 1}:`);
          console.log(`   Phone: ${number.phone_number}`);
          console.log(`   Friendly Name: ${number.friendly_name || 'N/A'}`);
          console.log(`   SMS Capable: ${number.capabilities.sms ? '✅' : '❌'}`);
          console.log(`   Voice Capable: ${number.capabilities.voice ? '✅' : '❌'}`);
          console.log(`   MMS Capable: ${number.capabilities.mms ? '✅' : '❌'}`);
          console.log('');
        });
        
        // Suggest the first SMS-capable number for the .env file
        const smsCapableNumber = result.incoming_phone_numbers.find(num => num.capabilities.sms);
        if (smsCapableNumber) {
          console.log('💡 Suggested for .env file:');
          console.log(`TWILIO_PHONE_NUMBER=${smsCapableNumber.phone_number}`);
        }
      } else {
        console.log('⚠️  No phone numbers found in your Twilio account.');
        console.log('📝 You need to purchase a phone number from Twilio Console:');
        console.log('   https://console.twilio.com/us1/develop/phone-numbers/manage/incoming');
      }
    } else {
      console.log('❌ Failed to retrieve phone numbers:', result);
      if (result.code) {
        console.log(`🚨 Error Code: ${result.code}`);
        console.log(`📝 Error Message: ${result.message}`);
      }
    }
  } catch (error) {
    console.error('❌ Error checking Twilio numbers:', error.message);
  }
};

const checkAccountInfo = async () => {
  try {
    console.log('🔍 Checking Twilio account info...\n');
    
    const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}.json`, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`).toString('base64')}`,
      },
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Account information:');
      console.log(`📧 Account SID: ${result.sid}`);
      console.log(`👤 Friendly Name: ${result.friendly_name || 'N/A'}`);
      console.log(`📊 Status: ${result.status}`);
      console.log(`🏷️  Type: ${result.type}`);
      console.log(`📅 Created: ${new Date(result.date_created).toLocaleDateString()}`);
      console.log('');
    } else {
      console.log('❌ Failed to retrieve account info:', result);
    }
  } catch (error) {
    console.error('❌ Error checking account info:', error.message);
  }
};

const runChecks = async () => {
  console.log('🚀 Checking Twilio configuration...\n');
  
  await checkAccountInfo();
  console.log('='.repeat(50) + '\n');
  await checkTwilioNumbers();
  
  console.log('\n🏁 Twilio configuration check completed!');
};

// Run the checks
runChecks();
