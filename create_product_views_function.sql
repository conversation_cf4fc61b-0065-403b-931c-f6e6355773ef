-- Function to get top products viewed in the last week
CREATE OR REPLACE FUNCTION get_top_products_last_week()
RETURNS TABLE (
  name text,
  views bigint,
  day text
) AS $$
BEGIN
  RETURN QUERY
  WITH daily_views AS (
    SELECT
      p.name,
      TO_CHAR(pv.viewed_at, 'Dy') AS day,
      COUNT(*) AS views
    FROM
      product_views pv
      JOIN products p ON pv.product_id = p.id
    WHERE
      pv.viewed_at >= NOW() - INTERVAL '7 days'
    GROUP BY
      p.name,
      TO_CHAR(pv.viewed_at, 'Dy')
    ORDER BY
      views DESC
  )
  SELECT * FROM daily_views;
END;
$$ LANGUAGE plpgsql;
