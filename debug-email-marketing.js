#!/usr/bin/env node

/**
 * Debug Email Marketing Service
 * Detailed debugging to identify issues
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🔧 Debug Information:');
console.log('SUPABASE_URL:', SUPABASE_URL ? 'Set' : 'Missing');
console.log('SUPABASE_SERVICE_KEY:', SUPABASE_SERVICE_KEY ? 'Set (length: ' + SUPABASE_SERVICE_KEY.length + ')' : 'Missing');

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function debugEmailMarketingTables() {
  console.log('\n🔍 Debugging Email Marketing Tables...\n');

  try {
    // Test 1: Basic connection test
    console.log('1️⃣ Testing basic Supabase connection...');
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (testError) {
      console.log('❌ Basic connection failed:', testError.message);
      return false;
    } else {
      console.log('✅ Basic Supabase connection working');
    }

    // Test 2: Check email_campaigns table structure
    console.log('\n2️⃣ Checking email_campaigns table...');
    
    // First, try to select from the table to see if it exists
    const { data: campaignsData, error: campaignsError } = await supabase
      .from('email_campaigns')
      .select('*')
      .limit(1);

    if (campaignsError) {
      console.log('❌ email_campaigns table error:', campaignsError);
      console.log('   Error code:', campaignsError.code);
      console.log('   Error message:', campaignsError.message);
      console.log('   Error details:', campaignsError.details);
      console.log('   Error hint:', campaignsError.hint);
      return false;
    } else {
      console.log('✅ email_campaigns table accessible');
      console.log('   Found', campaignsData?.length || 0, 'existing campaigns');
    }

    // Test 3: Check campaign_recipients table structure
    console.log('\n3️⃣ Checking campaign_recipients table...');
    
    const { data: recipientsData, error: recipientsError } = await supabase
      .from('campaign_recipients')
      .select('*')
      .limit(1);

    if (recipientsError) {
      console.log('❌ campaign_recipients table error:', recipientsError);
      console.log('   Error code:', recipientsError.code);
      console.log('   Error message:', recipientsError.message);
      return false;
    } else {
      console.log('✅ campaign_recipients table accessible');
      console.log('   Found', recipientsData?.length || 0, 'existing recipients');
    }

    // Test 4: Try a simple insert with detailed error handling
    console.log('\n4️⃣ Testing simple insert operation...');
    
    const testCampaign = {
      name: 'Debug Test Campaign',
      subject: 'Debug Test Subject',
      content: 'Debug test content',
      template_type: 'newsletter',
      target_audience: 'all_users',
      status: 'draft'
    };

    console.log('   Attempting to insert:', JSON.stringify(testCampaign, null, 2));

    const { data: insertData, error: insertError } = await supabase
      .from('email_campaigns')
      .insert(testCampaign)
      .select();

    if (insertError) {
      console.log('❌ Insert failed with detailed error:');
      console.log('   Error object:', JSON.stringify(insertError, null, 2));
      console.log('   Error code:', insertError.code);
      console.log('   Error message:', insertError.message);
      console.log('   Error details:', insertError.details);
      console.log('   Error hint:', insertError.hint);
      
      // Check if it's a permission issue
      if (insertError.code === '42501') {
        console.log('💡 This appears to be a permission issue');
        console.log('   The service role key may not have INSERT permissions on email_campaigns');
      }
      
      return false;
    } else {
      console.log('✅ Insert successful!');
      console.log('   Inserted data:', JSON.stringify(insertData, null, 2));
      
      // Clean up the test data
      if (insertData && insertData.length > 0) {
        const { error: deleteError } = await supabase
          .from('email_campaigns')
          .delete()
          .eq('id', insertData[0].id);
        
        if (deleteError) {
          console.log('⚠️  Failed to clean up test data:', deleteError.message);
        } else {
          console.log('✅ Test data cleaned up');
        }
      }
    }

    return true;

  } catch (error) {
    console.error('❌ Debug test failed with exception:', error);
    console.error('   Error stack:', error.stack);
    return false;
  }
}

async function checkTablePermissions() {
  console.log('\n🔐 Checking Table Permissions...\n');

  try {
    // Check what tables we can access
    const tables = ['profiles', 'products', 'orders', 'email_campaigns', 'campaign_recipients'];
    
    for (const table of tables) {
      console.log(`Checking ${table}...`);
      
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);

      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
      } else {
        console.log(`✅ ${table}: accessible (${data?.length || 0} rows)`);
      }
    }

  } catch (error) {
    console.error('❌ Permission check failed:', error);
  }
}

async function runDebug() {
  console.log('🐛 Email Marketing Service Debug Session\n');
  console.log('=' .repeat(60));

  // Run debugging tests
  await checkTablePermissions();
  const success = await debugEmailMarketingTables();

  console.log('\n' + '=' .repeat(60));
  console.log('🔍 DEBUG SUMMARY');
  console.log('=' .repeat(60));

  if (success) {
    console.log('✅ Email marketing tables are working correctly');
    console.log('✅ Database operations successful');
    console.log('✅ TypeScript fixes are effective');
  } else {
    console.log('❌ Issues found with email marketing setup');
    console.log('💡 Check the detailed error messages above');
  }

  console.log('\n🏁 Debug session completed');
  return success;
}

// Run the debug session
runDebug()
  .then(success => process.exit(success ? 0 : 1))
  .catch(error => {
    console.error('❌ Debug session failed:', error);
    process.exit(1);
  });
