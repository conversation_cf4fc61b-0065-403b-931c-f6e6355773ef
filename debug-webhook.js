/**
 * Debug webhook processing
 * Create an order and manually check webhook processing
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment
require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function debugWebhookProcessing() {
  console.log('🔍 Debugging Webhook Processing');
  console.log('===============================\n');
  
  try {
    // Find or create a test user
    let testUser;
    const { data: existingUser } = await supabase
      .from('profiles')
      .select('id, email')
      .limit(1)
      .single();
    
    if (existingUser) {
      testUser = existingUser;
      console.log('✅ Using existing user:', testUser.email);
    } else {
      // Create new user if none exists
      const { data: newUser, error: userError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'debug-password-123',
        email_confirm: true
      });
      
      if (userError) throw userError;
      
      await supabase.from('profiles').upsert({
        id: newUser.user.id,
        email: newUser.user.email,
        full_name: 'Debug User'
      });
      
      testUser = { id: newUser.user.id, email: newUser.user.email };
      console.log('✅ Created test user:', testUser.email);
    }
    
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert([
        {
          user_id: testUser.id,
          status: 'processing',
          total_amount: 50.00,
          payment_provider: 'stripe',
          payment_status: 'processing',
          customer_email: testUser.email,
          session_id: 'cs_debug_test_' + Date.now()
        }
      ])
      .select()
      .single();

    if (orderError) {
      console.error('❌ Error creating test order:', orderError);
      return;
    }

    console.log('✅ Created test order:', order.id);
    console.log('   Status:', order.status);
    console.log('   Payment Status:', order.payment_status);
    console.log('   Session ID:', order.session_id, '\n');

    // Try to update the order like the webhook would
    console.log('🔄 Testing webhook-style update...');
    
    const { data: updateResult, error: updateError } = await supabase
      .from('orders')
      .update({
        status: 'pending',
        payment_status: 'completed',
        payment_intent: 'pi_debug_test',
        total_amount: 50.00,
        updated_at: new Date().toISOString(),
        status_updated_at: new Date().toISOString(),
        customer_email: testUser.email
      })
      .eq('id', order.id)
      .eq('status', 'processing')
      .select();

    if (updateError) {
      console.error('❌ Update error:', updateError);
    } else {
      console.log('✅ Update successful:', updateResult);
    }

    // Check the final order status
    const { data: finalOrder, error: fetchError } = await supabase
      .from('orders')
      .select('*')
      .eq('id', order.id)
      .single();

    if (fetchError) {
      console.error('❌ Fetch error:', fetchError);
    } else {
      console.log('\n📊 Final order status:');
      console.log('   ID:', finalOrder.id);
      console.log('   Status:', finalOrder.status);
      console.log('   Payment Status:', finalOrder.payment_status);
      console.log('   Payment Intent:', finalOrder.payment_intent);
      console.log('   Updated At:', finalOrder.updated_at);
    }

    // Test the status condition separately
    console.log('\n🔍 Testing status condition...');
    const { data: processingOrders, error: conditionError } = await supabase
      .from('orders')
      .select('id, status, payment_status')
      .eq('id', order.id)
      .eq('status', 'processing');

    if (conditionError) {
      console.error('❌ Condition error:', conditionError);
    } else {
      console.log('✅ Orders with status=processing:', processingOrders?.length || 0);
      if (processingOrders && processingOrders.length > 0) {
        console.log('   Found:', processingOrders[0]);
      }
    }

    // Cleanup
    await supabase.from('orders').delete().eq('id', order.id);
    console.log('\n🧹 Test order cleaned up');

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugWebhookProcessing();