#!/bin/bash

# Exit on error
set -e

echo "🚀 Starting deployment process for Treasures of Maimi..."

# Check if vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI not found. Installing..."
    npm install -g vercel
fi

# Load environment variables from .env.local
if [ -f .env.local ]; then
    echo "✅ Loading environment variables from .env.local"
    export $(grep -v '^#' .env.local | xargs)
fi

# Commit any pending changes
echo "📝 Committing changes..."
if [ -z "$(git status --porcelain)" ]; then 
    echo "✅ No changes to commit"
else
    git add .
    git commit -m "Prepare for deployment: $(date)"
fi

# Run build locally first to catch any errors
echo "🔨 Building project locally..."
npm run build

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
vercel --prod

echo "🔗 Setting up custom domain: treasuresofmaimi.com"
vercel domains add treasuresofmaimi.com --yes

echo "✅ Deployment complete!"
echo "📝 Note: DNS propagation for your custom domain might take 24-48 hours."
echo "🌐 In the meantime, you can access your site using the Vercel URL provided above."
