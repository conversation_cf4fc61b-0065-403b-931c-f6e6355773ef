
> shop-maimi@0.1.0 dev
> next dev

- info Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env.local
- info Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env
- warn You have enabled experimental features (serverActions, serverComponentsExternalPackages) in next.config.js.
- warn Experimental features are not covered by semver, and may cause unexpected or broken application behavior. Use at your own risk.

- [33mwarn[39m Port 3000 is in use, trying 3001 instead.
- [32mready[39m started server on [::]:3001, url: http://localhost:3001
- [35mevent[39m compiled client and server successfully in 91 ms (20 modules)
- [36mwait[39m compiling...
- [35mevent[39m compiled client and server successfully in 51 ms (20 modules)
- [36minfo[39m Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env.local
- [36minfo[39m Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env
- [36minfo[39m Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env.local
- [36minfo[39m Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env
- [36mwait[39m compiling...
- [35mevent[39m compiled client and server successfully in 21 ms (20 modules)
- [36mwait[39m compiling...
- [35mevent[39m compiled client and server successfully in 19 ms (20 modules)
- [36mwait[39m compiling...
- [35mevent[39m compiled client and server successfully in 26 ms (20 modules)
