#!/usr/bin/env node

/**
 * Execute Email Marketing Migration
 * Manually executes the SQL migration using Supabase SQL editor approach
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function executeMigration() {
  console.log('🚀 Executing Email Marketing Migration...\n');

  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, 'supabase/migrations/20250720_create_email_marketing.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📄 Migration loaded, executing SQL...\n');

    // Since Supabase doesn't allow direct SQL execution via the client library for DDL,
    // we'll provide instructions for manual execution
    console.log('📋 MANUAL MIGRATION INSTRUCTIONS:');
    console.log('=' .repeat(60));
    console.log('1. Go to your Supabase Dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Create a new query');
    console.log('4. Copy and paste the following SQL:');
    console.log('=' .repeat(60));
    console.log('\n' + migrationSQL);
    console.log('\n' + '=' .repeat(60));
    console.log('5. Click "Run" to execute the migration');
    console.log('6. Come back and run this test again');
    console.log('=' .repeat(60));

    // For now, let's try to check if we can create the tables using individual statements
    console.log('\n🔧 Attempting automated execution...\n');

    // Try to create the ENUM types first
    const enumStatements = [
      "CREATE TYPE campaign_template_type AS ENUM ('promotional', 'newsletter', 'announcement', 'welcome');",
      "CREATE TYPE campaign_target_audience AS ENUM ('all_users', 'customers', 'subscribers', 'custom');",
      "CREATE TYPE campaign_status AS ENUM ('draft', 'scheduled', 'sending', 'sent', 'cancelled');"
    ];

    for (const statement of enumStatements) {
      try {
        console.log('Executing:', statement.substring(0, 50) + '...');
        // We can't execute DDL directly, so we'll skip this for now
        console.log('⚠️  DDL execution not supported via client library');
        break;
      } catch (error) {
        console.log('❌ Error:', error.message);
      }
    }

    console.log('\n💡 ALTERNATIVE APPROACH:');
    console.log('Since automated execution is not possible, please:');
    console.log('1. Copy the SQL above');
    console.log('2. Go to Supabase Dashboard > SQL Editor');
    console.log('3. Paste and execute the SQL');
    console.log('4. Then run: node test-email-marketing-simple.js');

    return false; // Indicates manual intervention needed

  } catch (error) {
    console.error('❌ Migration execution failed:', error.message);
    return false;
  }
}

async function checkIfMigrationNeeded() {
  console.log('🔍 Checking if migration is needed...\n');

  try {
    // Try to access the email_campaigns table
    const { data, error } = await supabase
      .from('email_campaigns')
      .select('id')
      .limit(1);

    if (error && error.code === '42P01') {
      console.log('❌ email_campaigns table does not exist');
      console.log('✅ Migration is needed');
      return true;
    } else if (error) {
      console.log('❌ Unexpected error:', error.message);
      return true;
    } else {
      console.log('✅ email_campaigns table already exists');
      console.log('⚠️  Migration may have already been run');
      return false;
    }

  } catch (error) {
    console.error('❌ Check failed:', error.message);
    return true;
  }
}

async function main() {
  console.log('📧 Email Marketing Migration Tool\n');

  const needsMigration = await checkIfMigrationNeeded();

  if (needsMigration) {
    await executeMigration();
  } else {
    console.log('🎉 Migration not needed - tables already exist!');
    console.log('✅ You can now test the email marketing service');
    console.log('💡 Run: node test-email-marketing-simple.js');
  }
}

main().catch(console.error);
