/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: [
      'res.cloudinary.com',
      'images.unsplash.com',
      'source.unsplash.com',
      'eu.louisvuitton.com',
      'media.gucci.com',
      'vfnihmcppowqkjytozwv.supabase.co',
      'avatars.githubusercontent.com',
      'lh3.googleusercontent.com',
      'www.paypalobjects.com'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.cloudinary.com'
      }
    ]
  },
  experimental: {
    serverActions: true,
    serverComponentsExternalPackages: ['@supabase/auth-helpers-nextjs'],
  },
  env: {
    NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: 'dlfizdedi',
  },
  webpack: (config, { isServer, dev }) => {
    config.ignoreWarnings = [
      { module: /node_modules\/punycode/ },
      { module: /node_modules\/@supabase\/realtime-js/ },
      // Suppress Stripe-related warnings
      { message: /preload.*unsupported.*as.*value/ },
      { message: /Cannot find module.*en/ },
      { message: /Module not found.*en/ }
    ];

    // Fix React Server Components bundling issues
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    // Optimize bundle splitting for better performance
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,
          supabase: {
            name: 'supabase',
            test: /[\\/]node_modules[\\/]@supabase[\\/]/,
            chunks: 'all',
            priority: 10,
          },
        },
      };
    }

    return config;
  },
  // Add headers configuration
  async headers() {
    return [
      {
        source: "/manifest.webmanifest",
        headers: [
          {
            key: "Content-Type",
            value: "application/manifest+json",
          },
        ],
      },
    ];
  },
};

// Only enable Sentry in production
if (process.env.NODE_ENV === 'production' && process.env.SENTRY_DSN) {
  const { withSentryConfig } = require("@sentry/nextjs");

  module.exports = withSentryConfig(
    nextConfig,
    {
      org: "ola-yeenca",
      project: "javascript-nextjs",
      silent: !process.env.CI,
      widenClientFileUpload: true,
      tunnelRoute: "/monitoring",
      disableLogger: true,
      automaticVercelMonitors: true,
    }
  );
} else {
  module.exports = nextConfig;
}
