{"name": "shop-maimi", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev-debug": "NODE_OPTIONS='--inspect' next dev --turbo", "dev-fast": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "fix-admin": "node ./scripts/apply-admin-fix.js", "debug": "node ./scripts/debug-node.js", "types:generate": "npx supabase gen types typescript --project-id vfnihmcppowqkjytozwv > src/lib/database.types.ts"}, "dependencies": {"@google/model-viewer": "^4.0.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@paypal/react-paypal-js": "^8.8.2", "@react-three/drei": "^9.0.0", "@react-three/fiber": "^8.0.0", "@sentry/nextjs": "^9.10.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "@types/pdfkit": "^0.13.9", "@types/three": "^0.174.0", "@types/uuid": "^10.0.0", "@vercel/og": "^0.6.8", "antd": "^5.14.0", "autoprefixer": "10.4.14", "buffer": "^6.0.3", "bufferutil": "^4.0.9", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "fetch-cookie": "^3.1.0", "framer-motion": "^10.16.4", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.477.0", "micromatch": "^4.0.8", "next": "13.4.19", "node-fetch": "^2.7.0", "pdfkit": "^0.16.0", "pg": "^8.13.3", "postcss": "8.4.24", "qrcode.react": "^4.2.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hot-toast": "^2.5.2", "react-qr-code": "^2.0.15", "recharts": "^2.15.1", "sonner": "^2.0.3", "stripe": "^17.7.0", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "three": "^0.174.0", "tough-cookie": "^5.1.2", "twilio": "^5.7.3", "utf-8-validate": "^6.0.5", "util": "^0.12.5", "uuid": "^11.1.0", "web-push": "^3.6.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.9", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "supabase": "^1.200.3", "tailwindcss": "3.3.2", "typescript": "^5"}}