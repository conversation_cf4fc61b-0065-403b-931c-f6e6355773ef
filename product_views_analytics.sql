-- Daily Product Views (for charting)
CREATE OR REPLACE FUNCTION get_daily_product_views()
RETURNS TABLE (
  view_day TIMESTAMP,
  product_id UUID,
  product_name TEXT,
  views BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    date_trunc('day', pv.viewed_at) AS view_day,
    pv.product_id,
    p.name AS product_name,
    COUNT(*) AS views
  FROM
    product_views pv
  JOIN
    products p ON pv.product_id = p.id
  WHERE
    pv.viewed_at >= NOW() - INTERVAL '7 days'
  GROUP BY
    view_day, pv.product_id, p.name
  ORDER BY
    view_day ASC, views DESC;
END;
$$ LANGUAGE plpgsql;

-- Total Views Per Product (Last 7 Days)
CREATE OR REPLACE FUNCTION get_top_viewed_products(days INTEGER DEFAULT 7)
RETURNS TABLE (
  id UUID,
  name TEXT,
  views BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.id,
    p.name,
    COUNT(v.id) AS views
  FROM
    products p
  LEFT JOIN
    product_views v ON p.id = v.product_id
  WHERE
    v.viewed_at >= NOW() - (days * INTERVAL '1 day')
  GROUP BY
    p.id
  ORDER BY
    views DESC;
END;
$$ LANGUAGE plpgsql;

-- Views Per Day (All Products Combined)
CREATE OR REPLACE FUNCTION get_views_per_day(days INTEGER DEFAULT 14)
RETURNS TABLE (
  view_day TIMESTAMP,
  total_views BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    date_trunc('day', viewed_at) AS view_day,
    COUNT(*) AS total_views
  FROM
    product_views
  WHERE
    viewed_at >= NOW() - (days * INTERVAL '1 day')
  GROUP BY
    view_day
  ORDER BY
    view_day ASC;
END;
$$ LANGUAGE plpgsql;
