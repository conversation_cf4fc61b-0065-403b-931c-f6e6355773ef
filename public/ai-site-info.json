{"site_name": "Treasures of Maimi", "site_description": "AI-curated luxury vintage fashion boutique specializing in authenticated pre-loved designer pieces", "primary_purpose": "E-commerce platform for authenticated vintage luxury fashion", "target_audience": ["Luxury fashion enthusiasts", "Sustainable fashion advocates", "Vintage luxury collectors", "Investment-focused buyers"], "unique_value_propositions": ["Certificate of authenticity for every item", "AI-powered personalized curation", "Sustainable luxury through circular fashion", "Expert authentication process", "Global shipping from Miami"], "product_categories": [{"name": "Designer Handbags", "brands": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "description": "Authenticated vintage luxury handbags with certificates"}, {"name": "Luxury Accessories", "items": ["<PERSON><PERSON><PERSON>", "Jewelry", "Belts", "Sunglasses"], "description": "Curated vintage luxury accessories from top designers"}, {"name": "Designer C<PERSON>hing", "description": "Vintage clothing pieces from prestigious fashion houses"}], "authentication_process": {"description": "Professional multi-point authentication process", "includes": ["Material analysis", "Craftsmanship examination", "Hardware verification", "Serial number validation", "Date code verification", "Brand-specific details check"], "certificate": "Official certificate of authenticity provided with each item"}, "ai_features": {"curation": "Machine learning algorithms for personalized recommendations", "matching": "Style preference analysis and trend-based suggestions", "discovery": "AI-powered product discovery based on user behavior"}, "sustainability_focus": {"circular_economy": "Promoting reuse and extending lifecycle of luxury goods", "environmental_impact": "Reducing fashion industry waste through pre-loved luxury", "conscious_consumption": "Encouraging thoughtful luxury purchases"}, "business_model": {"type": "B2C E-commerce", "inventory": "Curated vintage luxury items", "authentication": "In-house expert authentication team", "shipping": "Worldwide shipping with secure packaging", "payment": "Stripe and PayPal integration"}, "technical_stack": {"frontend": "Next.js 13+ with TypeScript", "backend": "Supabase PostgreSQL", "payments": "Stripe & PayPal", "images": "Cloudinary", "auth": "Supabase Auth", "notifications": "Multi-channel (Email, SMS, WhatsApp, Push)"}, "content_areas": {"product_catalog": "Detailed product descriptions with provenance", "education": "Vintage luxury fashion knowledge and history", "sustainability": "Circular fashion and environmental impact", "authentication_guides": "How to identify authentic luxury items", "care_instructions": "Maintaining vintage luxury pieces"}, "geographic_info": {"base_location": "Miami, Florida, USA", "shipping_areas": "Worldwide", "primary_markets": ["North America", "Europe", "Asia-Pacific"], "languages": ["English", "Spanish"]}, "quality_assurance": {"condition_grades": ["New", "Like New", "Excellent", "Good", "Fair"], "quality_control": "Multi-point inspection process", "return_policy": "14-day return policy for approved items"}, "ai_crawler_notes": {"focus_keywords": ["authenticated vintage luxury", "certificate of authenticity", "sustainable luxury fashion", "AI curated vintage", "circular fashion economy"], "content_priorities": ["Authentication and certificates", "Sustainability and circular fashion", "AI curation and personalization", "Luxury brand expertise", "Global shipping and service"], "crawl_friendly_urls": ["/products", "/collection", "/about", "/faq", "/llm.txt"]}, "last_updated": "2025-01-20", "schema_version": "1.0"}