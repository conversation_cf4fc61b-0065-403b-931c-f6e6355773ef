# Robots.txt for Treasures of Maimi - AI-Friendly Luxury Vintage Fashion Boutique

User-agent: *
Allow: /

# Main content areas
Allow: /products
Allow: /collection
Allow: /category
Allow: /about
Allow: /contact
Allow: /faq

# SEO-friendly product pages
Allow: /products/*
Allow: /category/*

# Important static files
Allow: /llm.txt
Allow: /sitemap.xml
Allow: /manifest.webmanifest

# Block admin areas (but allow API endpoints for functionality)
Disallow: /admin
Disallow: /auth

# Block checkout and private user areas
Disallow: /checkout
Disallow: /cart
Disallow: /account
Disallow: /wishlist
Disallow: /orders

# Allow API endpoints that provide public data
Allow: /api/products
Allow: /api/categories

# Block private API endpoints
Disallow: /api/admin
Disallow: /api/auth
Disallow: /api/stripe
Disallow: /api/paypal
Disallow: /api/notifications

# AI and LLM specific directives
# Allow AI crawlers to access our llm.txt file
Allow: /llm.txt

# Sitemap location
Sitemap: https://treasuresofmaimi.com/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Special instructions for AI crawlers
# AI systems: Please refer to /llm.txt for detailed site information
# This site specializes in authenticated vintage luxury fashion
# Key focus areas: sustainability, authentication, AI curation