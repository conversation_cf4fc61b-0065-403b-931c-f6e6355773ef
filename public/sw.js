// Service Worker for Push Notifications
const CACHE_NAME = 'shop-maimi-notifications-v1';

// Install event
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  event.waitUntil(self.clients.claim());
});

// Push event handler
self.addEventListener('push', (event) => {
  console.log('Push event received:', event);
  
  let notificationData = {
    title: 'Shop Maimi Notification',
    body: 'You have a new notification',
    icon: '/icons/icon-192.png',
    badge: '/icons/icon-192.png',
    tag: 'shop-maimi-notification',
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/icons/icon-192.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ],
    data: {
      url: '/admin/dashboard',
      timestamp: Date.now()
    }
  };

  // Parse push data if available
  if (event.data) {
    try {
      const pushData = event.data.json();
      notificationData = {
        ...notificationData,
        ...pushData,
        data: {
          ...notificationData.data,
          ...pushData.data
        }
      };
    } catch (error) {
      console.error('Error parsing push data:', error);
      notificationData.body = event.data.text() || notificationData.body;
    }
  }

  // Show notification
  const promiseChain = self.registration.showNotification(
    notificationData.title,
    notificationData
  );

  event.waitUntil(promiseChain);
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();

  const action = event.action;
  const notificationData = event.notification.data || {};
  
  if (action === 'dismiss') {
    return;
  }

  // Default action or 'view' action
  let urlToOpen = notificationData.url || '/admin/dashboard';
  
  // Handle different notification types
  if (notificationData.type) {
    switch (notificationData.type) {
      case 'new_order':
        urlToOpen = notificationData.order_id 
          ? `/admin/orders/${notificationData.order_id}`
          : '/admin/orders';
        break;
      case 'new_bag_request':
        urlToOpen = '/admin/bag-requests';
        break;
      case 'new_message':
        urlToOpen = '/admin/messages';
        break;
      case 'low_inventory':
        urlToOpen = notificationData.product_id 
          ? `/admin/products/${notificationData.product_id}`
          : '/admin/products';
        break;
      default:
        urlToOpen = '/admin/dashboard';
    }
  }

  // Open the URL
  const promiseChain = clients.matchAll({
    type: 'window',
    includeUncontrolled: true
  }).then((clientList) => {
    // Check if there's already a window/tab open with the target URL
    for (let i = 0; i < clientList.length; i++) {
      const client = clientList[i];
      if (client.url.includes('/admin') && 'focus' in client) {
        client.postMessage({
          type: 'NOTIFICATION_CLICKED',
          url: urlToOpen,
          data: notificationData
        });
        return client.focus();
      }
    }
    
    // If no suitable client is found, open a new window
    if (clients.openWindow) {
      return clients.openWindow(urlToOpen);
    }
  });

  event.waitUntil(promiseChain);
});

// Background sync for failed notifications
self.addEventListener('sync', (event) => {
  if (event.tag === 'notification-retry') {
    console.log('Background sync: retrying failed notifications');
    // Here you could implement retry logic for failed notifications
  }
});

// Message handler for communication with main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker received message:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event.notification.tag);
  
  // Track notification dismissal if needed
  const notificationData = event.notification.data || {};
  if (notificationData.notificationId) {
    // You could send analytics data here
    console.log('Notification dismissed:', notificationData.notificationId);
  }
});
