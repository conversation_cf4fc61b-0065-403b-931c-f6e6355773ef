#!/usr/bin/env node

/**
 * Run Email Marketing Migration
 * Executes the email marketing migration against the production database
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function runEmailMarketingMigration() {
  console.log('🚀 Running Email Marketing Migration...\n');

  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, 'supabase/migrations/20250720_create_email_marketing.sql');
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Migration file not found:', migrationPath);
      return false;
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Migration file loaded:', migrationPath);
    console.log('📏 Migration size:', migrationSQL.length, 'characters\n');

    // Check if tables already exist
    console.log('🔍 Checking if email marketing tables already exist...');
    
    try {
      const { data: existingCampaigns } = await supabase
        .from('email_campaigns')
        .select('id')
        .limit(1);
      
      console.log('✅ email_campaigns table already exists');
      console.log('⚠️  Migration may have already been run');
      
      const proceed = process.argv.includes('--force');
      if (!proceed) {
        console.log('💡 Use --force flag to run migration anyway');
        return true;
      }
    } catch (error) {
      console.log('📝 email_campaigns table does not exist - proceeding with migration');
    }

    // Split the migration into individual statements
    console.log('\n🔧 Executing migration statements...');
    
    // Remove comments and split by semicolon
    const statements = migrationSQL
      .split('\n')
      .filter(line => !line.trim().startsWith('--') && line.trim().length > 0)
      .join('\n')
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    console.log(`📊 Found ${statements.length} SQL statements to execute\n`);

    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.length === 0) continue;
      
      console.log(`${i + 1}/${statements.length} Executing:`, statement.substring(0, 50) + '...');
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          // Try alternative method for DDL statements
          const { error: directError } = await supabase
            .from('_dummy_table_that_does_not_exist')
            .select('*');
          
          // If it's a "table does not exist" error, that's expected
          // For actual SQL execution, we need to use a different approach
          console.log('⚠️  Direct execution not supported, statement may need manual execution');
          console.log('   Statement:', statement);
        } else {
          console.log('✅ Success');
          successCount++;
        }
      } catch (error) {
        console.log('❌ Error:', error.message);
        errorCount++;
      }
    }

    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successful statements: ${successCount}`);
    console.log(`❌ Failed statements: ${errorCount}`);

    if (errorCount > 0) {
      console.log('\n⚠️  Some statements failed. This is expected for DDL operations.');
      console.log('💡 You may need to run the migration manually in Supabase Dashboard:');
      console.log('   1. Go to Supabase Dashboard -> SQL Editor');
      console.log('   2. Copy and paste the migration file content');
      console.log('   3. Execute the SQL');
    }

    // Test if the migration worked
    console.log('\n🧪 Testing migration results...');
    
    try {
      const { data: campaigns, error: campaignsError } = await supabase
        .from('email_campaigns')
        .select('id')
        .limit(1);

      if (campaignsError) {
        console.log('❌ email_campaigns table still not accessible');
        return false;
      } else {
        console.log('✅ email_campaigns table is now accessible');
      }

      const { data: recipients, error: recipientsError } = await supabase
        .from('campaign_recipients')
        .select('id')
        .limit(1);

      if (recipientsError) {
        console.log('❌ campaign_recipients table still not accessible');
        return false;
      } else {
        console.log('✅ campaign_recipients table is now accessible');
      }

      console.log('\n🎉 Email marketing migration completed successfully!');
      return true;

    } catch (error) {
      console.log('❌ Migration verification failed:', error.message);
      return false;
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    return false;
  }
}

// Run the migration
runEmailMarketingMigration()
  .then(success => {
    if (success) {
      console.log('\n✅ Migration completed successfully');
      process.exit(0);
    } else {
      console.log('\n❌ Migration failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
