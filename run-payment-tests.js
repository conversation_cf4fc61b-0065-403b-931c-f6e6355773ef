/**
 * Master test runner for payment flow bug fix
 * Runs both database tests and webhook integration tests
 * 
 * Run with: node run-payment-tests.js
 */

const { spawn } = require('child_process');
const path = require('path');

function runTest(testFile, testName) {
  return new Promise((resolve) => {
    console.log(`\n🚀 Running ${testName}...`);
    console.log('='.repeat(50));
    
    const testProcess = spawn('node', [testFile], {
      stdio: 'inherit',
      cwd: __dirname
    });
    
    testProcess.on('close', (code) => {
      console.log(`\n${testName} completed with code: ${code}`);
      resolve(code === 0);
    });
    
    testProcess.on('error', (error) => {
      console.error(`❌ Failed to run ${testName}:`, error);
      resolve(false);
    });
  });
}

async function runAllTests() {
  console.log('🧪 PAYMENT FLOW BUG FIX - COMPREHENSIVE TESTING');
  console.log('================================================');
  console.log('This test suite validates the critical payment bug fix');
  console.log('that prevents orders from showing as "pending" when payments fail.\n');
  
  const testResults = [];
  
  // Test 1: Database Logic Tests
  const dbTestResult = await runTest(
    path.join(__dirname, 'test-payment-flow-fix.js'),
    'Database Logic Tests'
  );
  testResults.push({ name: 'Database Logic', passed: dbTestResult });
  
  // Test 2: Webhook Integration Tests  
  const webhookTestResult = await runTest(
    path.join(__dirname, 'test-stripe-webhook.js'),
    'Webhook Integration Tests'
  );
  testResults.push({ name: 'Webhook Integration', passed: webhookTestResult });
  
  // Final Summary
  console.log('\n' + '='.repeat(60));
  console.log('🏁 FINAL TEST SUMMARY');
  console.log('='.repeat(60));
  
  let allPassed = true;
  testResults.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (!result.passed) allPassed = false;
  });
  
  console.log('\n' + '-'.repeat(60));
  
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! The payment flow bug fix is working correctly.');
    console.log('');
    console.log('✅ Orders are created with "awaiting_payment" status');
    console.log('✅ Webhooks properly update order status after payment');
    console.log('✅ Failed payments are marked as "failed", not "pending"');
    console.log('✅ Expired sessions are handled correctly');
    console.log('✅ Safety mechanisms prevent accidental status changes');
    console.log('✅ Abandoned orders can be cleaned up automatically');
    console.log('');
    console.log('🛡️  The critical bug that could cause financial losses has been fixed!');
  } else {
    console.log('⚠️  SOME TESTS FAILED!');
    console.log('');
    console.log('The payment flow bug fix may not be working correctly.');
    console.log('Please review the failed tests and fix any issues before deploying.');
  }
  
  console.log('\n' + '='.repeat(60));
  
  process.exit(allPassed ? 0 : 1);
}

// Run all tests
runAllTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});