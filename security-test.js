#!/usr/bin/env node

/**
 * Comprehensive Security Test Suite
 * Tests all security fixes applied to the Shop Maimi application
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 Starting Comprehensive Security Audit...\n');

// Test 1: Check for hardcoded credentials
function testHardcodedCredentials() {
  console.log('1. 🔍 Testing for hardcoded credentials...');
  
  const patterns = [
    /key.*=.*['"][a-zA-Z0-9_-]{20,}['"]/g,
    /password.*=.*['"][^'"]+['"]/g,
    /token.*=.*['"][^'"]+['"]/g,
    /secret.*=.*['"][^'"]+['"]/g,
    /\|\|\s*['"][^'"]+['"]/g, // Fallback patterns
  ];
  
  let foundIssues = 0;
  
  function scanFile(filePath) {
    if (!filePath.endsWith('.ts') && !filePath.endsWith('.js')) return;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      patterns.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches) {
          matches.forEach(match => {
            // Filter out acceptable patterns
            if (match.includes('process.env') || 
                match.includes('console.log') || 
                match.includes('error:') ||
                match.includes('mailto:') ||
                match.includes('https://') ||
                match.includes('http://') ||
                match.includes('localhost')) {
              return;
            }
            
            console.log(`   ❌ SECURITY ISSUE: ${filePath}`);
            console.log(`      Found: ${match}`);
            foundIssues++;
          });
        }
      });
    } catch (error) {
      // Skip files that can't be read
    }
  }
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile()) {
          scanFile(fullPath);
        }
      });
    } catch (error) {
      // Skip directories that can't be read
    }
  }
  
  scanDirectory('./src');
  
  if (foundIssues === 0) {
    console.log('   ✅ No hardcoded credentials found');
  } else {
    console.log(`   ❌ Found ${foundIssues} potential security issues`);
  }
  
  return foundIssues === 0;
}

// Test 2: Check environment variable usage
function testEnvironmentVariables() {
  console.log('\n2. 🌍 Testing environment variable security...');
  
  const criticalEnvVars = [
    'STRIPE_SECRET_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'CLOUDINARY_API_SECRET',
    'VAPID_PRIVATE_KEY'
  ];
  
  let foundIssues = 0;
  
  function scanFile(filePath) {
    if (!filePath.endsWith('.ts') && !filePath.endsWith('.js')) return;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      criticalEnvVars.forEach(envVar => {
        // Check for fallback patterns like: process.env.STRIPE_SECRET_KEY || 'fallback'
        const fallbackPattern = new RegExp(`process\\.env\\.${envVar}\\s*\\|\\|\\s*['"][^'"]*['"]`, 'g');
        const matches = content.match(fallbackPattern);
        
        if (matches) {
          console.log(`   ❌ SECURITY ISSUE: ${filePath}`);
          console.log(`      Found fallback for ${envVar}: ${matches[0]}`);
          foundIssues++;
        }
      });
    } catch (error) {
      // Skip files that can't be read
    }
  }
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile()) {
          scanFile(fullPath);
        }
      });
    } catch (error) {
      // Skip directories that can't be read
    }
  }
  
  scanDirectory('./src');
  
  if (foundIssues === 0) {
    console.log('   ✅ All critical environment variables properly secured');
  } else {
    console.log(`   ❌ Found ${foundIssues} environment variable security issues`);
  }
  
  return foundIssues === 0;
}

// Test 3: Check for dangerous code patterns
function testDangerousPatterns() {
  console.log('\n3. ⚠️  Testing for dangerous code patterns...');
  
  const patterns = [
    { pattern: /eval\s*\(/g, description: 'eval() usage' },
    { pattern: /Function\s*\(/g, description: 'Function constructor usage' },
    { pattern: /\.innerHTML\s*=/g, description: 'innerHTML assignment' },
    { pattern: /dangerouslySetInnerHTML/g, description: 'dangerouslySetInnerHTML usage' },
    { pattern: /setTimeout\s*\(\s*['"][^'"]*['"]/g, description: 'setTimeout with string' },
    { pattern: /setInterval\s*\(\s*['"][^'"]*['"]/g, description: 'setInterval with string' }
  ];
  
  let foundIssues = 0;
  
  function scanFile(filePath) {
    if (!filePath.endsWith('.ts') && !filePath.endsWith('.js') && !filePath.endsWith('.tsx') && !filePath.endsWith('.jsx')) return;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      patterns.forEach(({ pattern, description }) => {
        const matches = content.match(pattern);
        if (matches) {
          console.log(`   ❌ SECURITY ISSUE: ${filePath}`);
          console.log(`      Found ${description}: ${matches[0]}`);
          foundIssues++;
        }
      });
    } catch (error) {
      // Skip files that can't be read
    }
  }
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile()) {
          scanFile(fullPath);
        }
      });
    } catch (error) {
      // Skip directories that can't be read
    }
  }
  
  scanDirectory('./src');
  
  if (foundIssues === 0) {
    console.log('   ✅ No dangerous code patterns found');
  } else {
    console.log(`   ❌ Found ${foundIssues} dangerous code patterns`);
  }
  
  return foundIssues === 0;
}

// Test 4: Check authentication and authorization patterns
function testAuthPatterns() {
  console.log('\n4. 🔐 Testing authentication and authorization patterns...');
  
  let foundIssues = 0;
  const authFiles = [];
  
  function scanFile(filePath) {
    if (!filePath.includes('api/') || !filePath.endsWith('.ts')) return;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check if API route has authentication
      if (content.includes('export async function') && 
          (content.includes('POST') || content.includes('PUT') || content.includes('DELETE') || content.includes('PATCH'))) {
        
        authFiles.push(filePath);
        
        // Check for authentication patterns
        const hasAuth = content.includes('auth.getSession()') || 
                       content.includes('isAdmin(') ||
                       content.includes('adminStatus') ||
                       content.includes('session');
        
        if (!hasAuth) {
          console.log(`   ❌ SECURITY ISSUE: ${filePath}`);
          console.log(`      API route missing authentication checks`);
          foundIssues++;
        }
      }
    } catch (error) {
      // Skip files that can't be read
    }
  }
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile()) {
          scanFile(fullPath);
        }
      });
    } catch (error) {
      // Skip directories that can't be read
    }
  }
  
  scanDirectory('./src');
  
  console.log(`   📊 Scanned ${authFiles.length} API route files`);
  
  if (foundIssues === 0) {
    console.log('   ✅ All API routes have authentication checks');
  } else {
    console.log(`   ❌ Found ${foundIssues} API routes without authentication`);
  }
  
  return foundIssues === 0;
}

// Test 5: Check for logging of sensitive information
function testSensitiveLogging() {
  console.log('\n5. 📝 Testing for sensitive information in logs...');
  
  const sensitivePatterns = [
    /console\.log.*password/gi,
    /console\.log.*key/gi,
    /console\.log.*token/gi,
    /console\.log.*secret/gi,
    /console\.log.*credential/gi
  ];
  
  let foundIssues = 0;
  
  function scanFile(filePath) {
    if (!filePath.endsWith('.ts') && !filePath.endsWith('.js')) return;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      sensitivePatterns.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches) {
          matches.forEach(match => {
            // Skip acceptable log patterns
            if (match.includes('session.user.id') || 
                match.includes('email') ||
                match.includes('Admin') ||
                match.includes('error')) {
              return;
            }
            
            console.log(`   ❌ SECURITY ISSUE: ${filePath}`);
            console.log(`      Potentially logging sensitive data: ${match}`);
            foundIssues++;
          });
        }
      });
    } catch (error) {
      // Skip files that can't be read
    }
  }
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        } else if (stat.isFile()) {
          scanFile(fullPath);
        }
      });
    } catch (error) {
      // Skip directories that can't be read
    }
  }
  
  scanDirectory('./src');
  
  if (foundIssues === 0) {
    console.log('   ✅ No sensitive information logging detected');
  } else {
    console.log(`   ❌ Found ${foundIssues} potential sensitive logging issues`);
  }
  
  return foundIssues === 0;
}

// Run all tests
async function runSecurityAudit() {
  const results = [
    testHardcodedCredentials(),
    testEnvironmentVariables(), 
    testDangerousPatterns(),
    testAuthPatterns(),
    testSensitiveLogging()
  ];
  
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log('\n' + '='.repeat(60));
  console.log('🔒 SECURITY AUDIT SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Tests Passed: ${passed}/${total}`);
  console.log(`❌ Tests Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 All security tests passed! Application is secure.');
    process.exit(0);
  } else {
    console.log('\n⚠️  Security issues found. Please review and fix the issues above.');
    process.exit(1);
  }
}

runSecurityAudit();