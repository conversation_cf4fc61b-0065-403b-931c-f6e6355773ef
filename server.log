
> shop-maimi@0.1.0 dev
> next dev

- info Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env.local
- info Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env
- warn You have enabled experimental features (serverActions, serverComponentsExternalPackages) in next.config.js.
- warn Experimental features are not covered by semver, and may cause unexpected or broken application behavior. Use at your own risk.

- [32mready[39m started server on [::]:3000, url: http://localhost:3000
- [35mevent[39m compiled client and server successfully in 102 ms (20 modules)
- [36mwait[39m compiling...
- [35mevent[39m compiled client and server successfully in 50 ms (20 modules)
- [36minfo[39m Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env.local
- [36minfo[39m Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env
- [36minfo[39m Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env.local
- [36minfo[39m Loaded env from /Users/<USER>/Desktop/Shop-Maimi/.env
- [36mwait[39m compiling /api/stripe/webhook/route (client and server)...
- [35mevent[39m compiled successfully in 360 ms (385 modules)
⚠️ No Stripe signature found in webhook request
💰 Payment successful for session: cs_direct_test_1753355623603
🔍 Debug: Session metadata: {
  order_id: [32m'0e98c528-4234-4a35-b5da-435ab1245dce'[39m,
  customer_email: [32m'<EMAIL>'[39m
}
🔍 Debug: Order ID: 0e98c528-4234-4a35-b5da-435ab1245dce
📦 Shipping details: [1mnull[22m
🔄 Attempting to update order: 0e98c528-4234-4a35-b5da-435ab1245dce
✅ Order update result: []
✅ Orders updated: [33m0[39m
✅ Order status updated successfully: 0e98c528-4234-4a35-b5da-435ab1245dce
Email Service: Starting sendAdminNotification with: {
  type: [32m'new_order'[39m,
  title: [32m'New Order Received'[39m,
  message: [32m'Order #0e98c528-4234-4a35-b5da-435ab1245dce for €99.99 from <EMAIL>'[39m,
  data: {
    order_id: [32m'0e98c528-4234-4a35-b5da-435ab1245dce'[39m,
    order_number: [32m'0e98c528-4234-4a35-b5da-435ab1245dce'[39m,
    total_amount: [33m99.99[39m,
    customer_email: [32m'<EMAIL>'[39m
  },
  priority: [32m'high'[39m
}
Fetching admin users with email addresses...
SMS Service: Starting sendAdminNotification with: {
  type: [32m'new_order'[39m,
  title: [32m'New Order Received'[39m,
  message: [32m'Order #0e98c528-4234-4a35-b5da-435ab1245dce for €99.99 from <EMAIL>'[39m,
  data: {
    order_id: [32m'0e98c528-4234-4a35-b5da-435ab1245dce'[39m,
    order_number: [32m'0e98c528-4234-4a35-b5da-435ab1245dce'[39m,
    total_amount: [33m99.99[39m,
    customer_email: [32m'<EMAIL>'[39m
  },
  priority: [32m'high'[39m
}
Twilio configured successfully
Fetching admin users with phone numbers...
Admin users query result: {
  adminUsers: [
    {
      id: [32m'4618d1c6-16ca-4a59-9210-0d3e84d77ab2'[39m,
      phone_number: [32m'+34 672 023 649'[39m,
      first_name: [32m'Mai'[39m,
      last_name: [32m'Hayashi'[39m
    }
  ],
  profileError: [1mnull[22m
}
Number of admin users found: [33m1[39m
Checking preferences for admin: 4618d1c6-16ca-4a59-9210-0d3e84d77ab2 notification type: new_order
Preferences result: { prefs: { push_enabled: [33mtrue[39m }, prefsError: [1mnull[22m }
Admin users query result: {
  adminUsers: [
    {
      id: [32m'4618d1c6-16ca-4a59-9210-0d3e84d77ab2'[39m,
      email: [32m'<EMAIL>'[39m,
      first_name: [32m'Mai'[39m,
      last_name: [32m'Hayashi'[39m
    }
  ],
  profileError: [1mnull[22m
}
Number of admin users found: [33m1[39m
Checking preferences for admin: 4618d1c6-16ca-4a59-9210-0d3e84d77ab2 notification type: new_order
No admin users found with WhatsApp notifications enabled for type: new_order
Preferences result: { prefs: { email_enabled: [33mtrue[39m }, prefsError: [1mnull[22m }
SMS sent successfully: SM090eb54de361ccfe64acc822e345ee80
Email sent successfully: 63cc4c1a-726c-4c60-a0b4-37202a564409
Unified notification results: { email: [33mtrue[39m, sms: [33mtrue[39m, whatsapp: [33mfalse[39m }
📧 New order notification sent to admin
- [36mwait[39m compiling /src/middleware (client and server)...
- [35mevent[39m compiled successfully in 157 ms (194 modules)
 [AuthApiError: Invalid Refresh Token: Refresh Token Not Found] {
  __isAuthError: true,
  name: 'AuthApiError',
  status: 400,
  code: 'refresh_token_not_found'
}
[Middleware] Admin route access: { sessionExists: false, pathname: '/admin' }
 [AuthApiError: Invalid Refresh Token: Refresh Token Not Found] {
  __isAuthError: true,
  name: 'AuthApiError',
  status: 400,
  code: 'refresh_token_not_found'
}
- [36mwait[39m compiling /auth/admin/login/page (client and server)...
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [36mwait[39m compiling...
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [36mwait[39m compiling /manifest.webmanifest/route (client and server)...
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [36mwait[39m compiling /icon (client and server)...
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [36mwait[39m compiling /icon/[[...__metadata_id__]]/route (client and server)...
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [36mwait[39m compiling /page (client and server)...
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/components/ErrorBoundary.tsx
- [36mwait[39m compiling /auth/login/page (client and server)...
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/app/global-error.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/app/global-error.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/app/global-error.tsx
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/app/global-error.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/app/global-error.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/app/global-error.tsx
- [36mwait[39m compiling /_error (client and server)...
- [33mwarn[39m ./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/app/global-error.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/app/global-error.tsx

./node_modules/require-in-the-middle/index.js
Critical dependency: require function is used in a way in which dependencies cannot be statically extracted

Import trace for requested module:
./node_modules/require-in-the-middle/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/src/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/src/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./src/app/global-error.tsx
[LoginPage] isAuthLoading: [33mtrue[39m
[LoginPage] session: [1mnull[22m
[LoginPage] forceRender: [33mfalse[39m
[LoginPage] hasRedirected: [33mfalse[39m
[LoginPage] Redirect target: /
[LoginPage] Showing loading spinner (isAuthLoading && !forceRender)
