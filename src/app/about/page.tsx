'use client';

import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';


const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.6 }
  }
};

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      <div className="relative h-[60vh] bg-[#f7f7f7]">
        <div className="absolute inset-0 overflow-hidden">
          <Image
            src="/images/about-hero.JPG"
            alt="The Treasure of Maimi About Page"
            fill
            priority
            className="object-cover object-center opacity-80"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-[rgba(0,0,0,0.4)] to-transparent" />
        </div>
        
        <div className="relative h-full max-w-7xl mx-auto px-6 flex flex-col justify-center items-center text-center">
          <h1 className="text-5xl md:text-6xl font-heading tracking-wider text-white mb-6">Our Story</h1>
          <p className="text-xl font-body italic text-white/90 max-w-2xl">
            Curating timeless luxury, one piece at a time.
          </p>
        </div>
      </div>
      
      <motion.section 
        className="py-20 px-6 max-w-7xl mx-auto"
        initial="hidden"
        whileInView="visible"
        variants={fadeIn}
        viewport={{ once: true }}
      >
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="font-heading text-3xl mb-6 tracking-wider text-[#171717]">Our Mission</h2>
            <p className="text-gray-700 mb-6 leading-relaxed">
              At The Treasure of Maimi, we believe that luxury should be accessible, sustainable, and meaningful. 
              Our mission is to curate high-quality, pre-loved luxury items that tell a story and give 
              them a second life in your wardrobe.
            </p>
            <p className="text-gray-700 mb-6 leading-relaxed">
              Founded in 2020, we've built a reputation for authentic, carefully selected pieces that 
              combine timeless design with exceptional craftsmanship. Each item in our collection is 
              meticulously authenticated and restored to ensure it meets our exacting standards.
            </p>
            <div className="flex gap-2">
              <Link
                href="/products"
                className="inline-block px-6 py-3 text-white font-body tracking-wide text-sm transition-all duration-300 bg-[#171717] hover:bg-[#333333] rounded-md"
              >
                Explore Collection
              </Link>
              <Link
                href="/contact"
                className="inline-block px-6 py-3 border border-[#171717] text-[#171717] font-body tracking-wide text-sm transition-all duration-300 hover:bg-gray-50 rounded-md"
              >
                Contact Us
              </Link>
            </div>
          </div>
          <div className="relative h-[400px] rounded-lg overflow-hidden">
            <Image 
              src="/images/logo.png" 
              alt="The Treasure of Maimi Logo" 
              fill
              sizes="(max-width: 768px) 100vw, 400px"
              className="object-contain object-center"
            />
          </div>
        </div>
      </motion.section>
      
      <motion.section 
        className="py-20 px-6 bg-[#f7f7f7]"
        initial="hidden"
        whileInView="visible"
        variants={fadeIn}
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto text-center mb-16">
          <h2 className="font-heading text-3xl mb-6 tracking-wider text-[#171717]">Our Values</h2>
          <p className="text-gray-700 max-w-3xl mx-auto">
            These core principles guide everything we do at The Treasure of Maimi, from the products we select 
            to the experience we create for our customers.
          </p>
        </div>
        
        <div className="max-w-7xl mx-auto grid md:grid-cols-3 gap-8">
          <div className="bg-white p-8 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-[#171717] text-white rounded-full flex items-center justify-center mb-6 mx-auto">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-xl font-heading mb-4 text-center">Authenticity</h3>
            <p className="text-gray-600 text-center">
              Every product in our collection undergoes rigorous authentication by our luxury experts. 
              We guarantee the authenticity of each item we sell.
            </p>
          </div>
          
          <div className="bg-white p-8 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-[#171717] text-white rounded-full flex items-center justify-center mb-6 mx-auto">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
            </div>
            <h3 className="text-xl font-heading mb-4 text-center">Sustainability</h3>
            <p className="text-gray-600 text-center">
              We're committed to sustainable luxury. By giving pre-loved items a second life, 
              we reduce waste and promote conscious consumption.
            </p>
          </div>
          
          <div className="bg-white p-8 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-[#171717] text-white rounded-full flex items-center justify-center mb-6 mx-auto">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="text-xl font-heading mb-4 text-center">Quality</h3>
            <p className="text-gray-600 text-center">
              We meticulously assess each item's condition and only select pieces that meet our high standards. 
              Every product comes with a detailed condition report.
            </p>
          </div>
        </div>
      </motion.section>
      
      <motion.section 
        className="py-20 px-6 max-w-7xl mx-auto"
        initial="hidden"
        whileInView="visible"
        variants={fadeIn}
        viewport={{ once: true }}
      >
        <div className="text-center mb-16">
          <h2 className="font-heading text-3xl mb-6 tracking-wider text-[#171717]">Meet the Founder</h2>
          <p className="text-gray-700 max-w-3xl mx-auto">
            At the heart of The Treasure of Maimi is a visionary whose passion for vintage fashion and commitment to sustainability drives every curated piece.
          </p>
        </div>
        
        <div className="grid md:grid-cols-1 gap-12 max-w-2xl mx-auto">
          <div className="text-center">
            <div className="relative w-64 h-64 mx-auto rounded-full overflow-hidden mb-8">
              <Image 
                src="/images/mai.jpeg" 
                alt="Mai - Founder & CEO" 
                fill
                className="object-cover object-center"
              />
            </div>
            <h3 className="text-2xl font-heading mb-2">Mai</h3>
            <p className="text-gray-600 italic mb-6 text-lg">Founder & CEO</p>
            <p className="text-gray-700 max-w-lg mx-auto leading-relaxed">
              With a deep passion for vintage fashion and sustainability, Mai founded The Treasure of Maimi 
              to bring curated luxury pieces to fashion enthusiasts. Her vision combines the elegance 
              of classic design with modern accessibility.
            </p>
          </div>
        </div>
      </motion.section>
      
      <motion.section 
        className="py-20 px-6 bg-[#171717] text-white"
        initial="hidden"
        whileInView="visible"
        variants={fadeIn}
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="font-heading text-3xl mb-6 tracking-wider">Our Sustainability Commitment</h2>
          <p className="max-w-3xl mx-auto mb-8 text-white/80">
            At The Treasure of Maimi, sustainability isn't just a trend—it's at the core of our business model. 
            By extending the lifecycle of luxury goods, we're reducing waste and promoting a more 
            circular economy in the fashion industry.
          </p>
          
          <div className="grid md:grid-cols-3 gap-8 mt-12">
            <div>
              <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">Circular Economy</h3>
              <p className="text-white/80">
                We extend the lifecycle of luxury items, keeping them in circulation 
                and out of landfills.
              </p>
            </div>
            
            <div>
              <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">Eco-Friendly Packaging</h3>
              <p className="text-white/80">
                All our packaging is made from recycled or recyclable materials, 
                minimizing our environmental footprint.
              </p>
            </div>
            
            <div>
              <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">Carbon-Neutral Shipping</h3>
              <p className="text-white/80">
                We offset the carbon emissions from all our shipments through 
                certified carbon offset programs.
              </p>
            </div>
          </div>
        </div>
      </motion.section>
      
      <section className="py-20 px-6 max-w-7xl mx-auto text-center">
        <h2 className="font-heading text-3xl mb-6 tracking-wider text-[#171717]">Join Our Community</h2>
        <p className="text-gray-700 max-w-3xl mx-auto mb-8">
          Be the first to know about new arrivals, exclusive offers, and sustainability initiatives.
        </p>
        <div className="max-w-md mx-auto flex gap-2">
          <input 
            type="email" 
            placeholder="Your email address" 
            className="flex-1 border border-gray-300 rounded-md px-4 py-3 focus:outline-none focus:ring-2 focus:ring-gray-200"
          />
          <button className="px-6 py-3 bg-[#171717] text-white font-body tracking-wide text-sm hover:bg-[#333333] transition-colors duration-300 rounded-md">
            Subscribe
          </button>
        </div>
      </section>
    </div>
  );
}
