'use client';

import { useState, useEffect, useRef } from 'react';
import { getUserProfile, Profile, updateProfile, signOut } from '@/lib/auth';
import { User } from '@supabase/supabase-js';
import { useAuth } from '@/components/AuthProvider';
import { getWishlistItems, getOrderHistory, getWardrobeItems, addWardrobeItem, deleteWardrobeItem, WardrobeItem, WishlistItem, removeFromWishlist } from '@/lib/wardrobe';
import Image from 'next/image';
import Link from 'next/link';
import toast from 'react-hot-toast';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/database.types';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    name: string;
    slug: string;
    image_url: string;
  } | null;
}

interface Order {
  id: string;
  order_number: string | null;
  status: string;
  total_amount: number;
  created_at: string | null;
  updated_at: string | null;
  payment_intent: string | null;
  payment_provider: string | null;
  payment_status: string | null;
  carrier: string | null;
  tracking_url: string | null;
  items: OrderItem[];
}

export default function AccountPage() {
  const { user, session, isLoading: authLoading } = useAuth();
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('profile');
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [orderHistory, setOrderHistory] = useState<Order[]>([]);
  const [orderError, setOrderError] = useState<string | null>(null);
  const [wardrobeItems, setWardrobeItems] = useState<WardrobeItem[]>([]);
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [nameForm, setNameForm] = useState({
    first_name: '',
    last_name: ''
  });
  const [addressForm, setAddressForm] = useState({
    street_address: '',
    city: '',
    state: '',
    postal_code: '',
    country: '',
    phone: ''
  });
  const [isAddingWardrobeItem, setIsAddingWardrobeItem] = useState(false);
  const [newWardrobeItem, setNewWardrobeItem] = useState({
    name: '',
    category: 'handbags',
    color: '#000000',
    brand: '',
    notes: '',
    priority: 'medium'
  });
  const [saving, setSaving] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [notificationPreferences, setNotificationPreferences] = useState({
    order_updates: true,
    bag_request_updates: true,
    promotional_emails: false,
    marketing_sms: false,
  });
  const supabase = createClientComponentClient<Database>();

  // Helper function to get order status label
  const getOrderStatusLabel = (status: string | null, paymentStatus: string | null): string => {
    if (status === 'cancelled' && paymentStatus === 'incomplete') {
      return 'Payment Not Completed';
    }
    if (status === 'cancelled') {
      return 'Cancelled';
    }
    if (status === 'processing') {
      return 'Processing';
    }
    if (status === 'pending') {
      return 'Payment Not Completed';
    }
    return status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Payment Not Completed';
  };

  // Helper function to get order status styling
  const getOrderStatusStyle = (status: string | null, paymentStatus: string | null): string => {
    if (status === 'cancelled' && paymentStatus === 'incomplete') {
      return 'bg-orange-100 text-orange-800';
    }
    if (status === 'cancelled') {
      return 'bg-red-100 text-red-800';
    }
    if (status === 'processing') {
      return 'bg-blue-100 text-blue-800';
    }
    if (status === 'pending') {
      return 'bg-orange-100 text-orange-800'; // Same as incomplete - both mean payment not completed
    }
    if (status === 'completed' || status === 'paid') {
      return 'bg-green-100 text-green-800';
    }
    return 'bg-orange-100 text-orange-800'; // Default to "not completed" styling
  };

  // Handle retry payment for incomplete orders
  const handleRetryPayment = (order: Order) => {
    // Redirect to checkout to complete the payment
    window.location.href = '/checkout';
  };

  useEffect(() => {
    // Check for session_id in URL
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session_id');
    
    if (sessionId) {
      setShowSuccessMessage(true);
      setActiveTab('orders');
      // Remove session_id from URL without refreshing the page
      window.history.replaceState({}, '', '/account?tab=orders');
      
      // Refresh order history immediately
      if (user?.id) {
        loadOrderHistory(user.id);
      }
      
      // Hide success message after 5 seconds
      setTimeout(() => {
        setShowSuccessMessage(false);
      }, 5000);
    } else {
      // Check if tab is specified in URL
      const tab = urlParams.get('tab');
      if (tab) {
        setActiveTab(tab);
      }
    }
  }, [user?.id]);

  // Separate function to load order history
  const loadOrderHistory = async (userId: string) => {
    try {
      console.log('Loading order history for user:', userId);
      setOrderError(null);
      const orders = await getOrderHistory(userId);
      console.log('Orders loaded:', orders);
      orders.forEach((order: Order) => {
        if (!order.items || !Array.isArray(order.items)) {
          console.warn('Order items missing or invalid for order:', order.id);
        }
      });
      if (orders.length === 0) {
        console.warn('No orders returned for user:', userId);
      }
      setOrderHistory(orders);
    } catch (error) {
      console.error('Error loading order history:', error);
      setOrderError('Failed to load order history. Please try refreshing the page.');
    }
  };

  useEffect(() => {
    async function loadUserData() {
      if (authLoading) {
        console.log('Auth still loading, waiting...');
        return;
      }
      
      try {
        console.log('Loading user data...', { user, session });
        
        if (user?.id) {
          console.log('User found, loading profile and other data...');
          const profileData = await getUserProfile(user.id);
          console.log('Profile data loaded:', profileData);
          setProfile(profileData);
          
          // Initialize forms with profile data
          if (profileData) {
            console.log('Initializing forms with profile data');
            
            // Initialize name form
            setNameForm({
              first_name: profileData.first_name || '',
              last_name: profileData.last_name || ''
            });
            
            // Initialize address form
            setAddressForm({
              street_address: profileData.street_address || '',
              city: profileData.city || '',
              state: profileData.state || '',
              postal_code: profileData.postal_code || '',
              country: profileData.country || '',
              phone: profileData.phone || ''
            });
          }
          
          // Load wishlist items
          const wishlist = await getWishlistItems(user.id);
          setWishlistItems(wishlist as WishlistItem[]);
          
          // Load order history
          await loadOrderHistory(user.id);
          
          // Load wardrobe items
          const wardrobe = await getWardrobeItems(user.id);
          console.log('[Account] Loaded wardrobe items:', wardrobe);
          setWardrobeItems(wardrobe);
        } else {
          console.log('No user found');
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadUserData();
  }, [authLoading, user, session]);

  // Add realtime subscription for wardrobe items
  useEffect(() => {
    if (!user?.id) return;

    // Set up realtime subscription
    const channel = supabase
      .channel('wardrobe_items_changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'wardrobe_items',
          filter: `user_id=eq.${user.id}` // Only listen to changes for this user's items
        },
        async (payload: RealtimePostgresChangesPayload<Database['public']['Tables']['wardrobe_items']['Row']>) => {
          console.log('Realtime change received:', payload);
          
          // Reload wardrobe items to ensure we have the latest data
          const updatedWardrobe = await getWardrobeItems(user.id);
          setWardrobeItems(updatedWardrobe);
          
          // Show toast notification based on the event type
          if (payload.eventType === 'UPDATE') {
            const newStatus = payload.new.status;
            if (newStatus === 'approved') {
              toast.success('🎉 Your bag request has been approved!');
            } else if (newStatus === 'rejected') {
              toast.error('Your bag request has been rejected.');
            }
          }
        }
      )
      .subscribe();

    // Cleanup subscription on component unmount or when user changes
    return () => {
      supabase.removeChannel(channel);
    };
  }, [user?.id]);

  const handleAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;
    
    setSaving(true);
    try {
      console.log('Submitting address form:', addressForm);
      const updatedProfile = await updateProfile(user.id, {
        street_address: addressForm.street_address,
        city: addressForm.city,
        state: addressForm.state,
        postal_code: addressForm.postal_code,
        country: addressForm.country,
        phone: addressForm.phone
      });
      console.log('Updated profile:', updatedProfile);
      
      // Force a refresh of the profile data
      if (user.id) {
        console.log('Refreshing profile data after update...');
        const refreshedProfile = await getUserProfile(user.id);
        console.log('Refreshed profile data:', refreshedProfile);
        setProfile(refreshedProfile);
      } else {
        setProfile(updatedProfile);
      }
      
      setIsEditingAddress(false);
      toast.success('Address updated successfully');
    } catch (error) {
      console.error('Error updating address:', error);
      toast.error('Failed to update address');
    } finally {
      setSaving(false);
    }
  };

  const handleNameSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;
    
    setSaving(true);
    try {
      console.log('Submitting name form:', nameForm);
      const updatedProfile = await updateProfile(user.id, {
        first_name: nameForm.first_name,
        last_name: nameForm.last_name,
        full_name: `${nameForm.first_name} ${nameForm.last_name}`.trim()
      });
      setAddressForm((prev) => ({
        ...prev,
        full_name: `${nameForm.first_name} ${nameForm.last_name}`.trim()
      }));
      console.log('Updated profile with name:', updatedProfile);
      
      // Force a refresh of the profile data
      if (user.id) {
        console.log('Refreshing profile data after name update...');
        const refreshedProfile = await getUserProfile(user.id);
        console.log('Refreshed profile data:', refreshedProfile);
        setProfile(refreshedProfile);
      } else {
        setProfile(updatedProfile);
      }
      
      setIsEditingName(false);
      toast.success('Name updated successfully');
    } catch (error) {
      console.error('Error updating name:', error);
      toast.error('Failed to update name');
    } finally {
      setSaving(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddWardrobeItem = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;
    
    setSaving(true);
    try {
      // Use the preview URL if available, otherwise use an empty string
      // This will be replaced with the actual uploaded image URL in a production environment
      const imageUrl = previewUrl || '';
      
      console.log('[Account] Adding wardrobe item:', {
        userId: user.id,
        item: {
          ...newWardrobeItem,
          image_url: imageUrl,
          status: 'pending'
        }
      });
      
      await addWardrobeItem(user.id, {
        ...newWardrobeItem,
        image_url: imageUrl,
        status: 'pending'
      });
      
      // Refresh wardrobe items
      const wardrobe = await getWardrobeItems(user.id);
      setWardrobeItems(wardrobe);
      
      // Reset form
      setIsAddingWardrobeItem(false);
      setNewWardrobeItem({
        name: '',
        category: 'handbags',
        color: '#000000',
        brand: '',
        notes: '',
        priority: 'medium'
      });
      setSelectedFile(null);
      setPreviewUrl(null);
    } catch (error) {
      console.error('Error adding bag request:', error);
      toast.error('Failed to add bag request. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleRemoveFromWishlist = async (productId: string) => {
    if (!user) return;
    
    console.log('Removing product from wishlist:', productId);
    try {
      const result = await removeFromWishlist(user.id, productId);
      console.log('Remove from wishlist result:', result);
      
      // Update local state immediately
      setWishlistItems(prev => prev.filter(item => item.product_id !== productId));
      
      // Refresh wishlist items from the database
      console.log('Fetching updated wishlist items...');
      const wishlist = await getWishlistItems(user.id);
      console.log('Updated wishlist items:', wishlist);
      setWishlistItems(wishlist as WishlistItem[]);
    } catch (error) {
      console.error('Error removing from wishlist:', error);
    }
  };

  const handleDeleteWardrobeItem = async (itemId: string) => {
    if (!user) return;
    
    try {
      await deleteWardrobeItem(itemId);
      
      // Refresh wardrobe items
      const wardrobe = await getWardrobeItems(user.id);
      setWardrobeItems(wardrobe);
    } catch (error) {
      console.error('Error deleting wardrobe item:', error);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      window.location.href = '/';
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex justify-center items-center pt-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
      </div>
    );
  }

  // Don't redirect if we're still loading - let middleware handle it
  if (!user || !session) {
    console.log('Account page: No user or session, showing loading...');
    return (
      <div className="min-h-screen flex justify-center items-center pt-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
      </div>
    );
  }

  return (
    <div className="bg-white min-h-screen">
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-50 p-4">
          <div className="container mx-auto px-4 max-w-7xl flex items-center justify-between">
            <div className="flex items-center">
              <svg className="h-5 w-5 text-green-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <p className="text-green-700">Order placed successfully! Thank you for your purchase.</p>
            </div>
            <button 
              onClick={() => setShowSuccessMessage(false)}
              className="text-green-700 hover:text-green-900"
            >
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Page Header */}
      <div className="border-b border-gray-100 py-6">
        <div className="container mx-auto px-4 max-w-7xl">
          <h1 className="text-2xl uppercase tracking-widest font-light">My Account</h1>
        </div>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-gray-100">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('profile')}
              className={`py-4 text-xs uppercase tracking-wider ${
                activeTab === 'profile' 
                  ? 'border-b border-black font-medium' 
                  : 'text-gray-500 hover:text-black'
              }`}
            >
              Profile
            </button>
            <button
              onClick={() => setActiveTab('wishlist')}
              className={`py-4 text-xs uppercase tracking-wider ${
                activeTab === 'wishlist' 
                  ? 'border-b border-black font-medium' 
                  : 'text-gray-500 hover:text-black'
              }`}
            >
              Wishlist
            </button>
            <button
              onClick={() => setActiveTab('orders')}
              className={`py-4 text-xs uppercase tracking-wider ${
                activeTab === 'orders' 
                  ? 'border-b border-black font-medium' 
                  : 'text-gray-500 hover:text-black'
              }`}
            >
              Orders
            </button>
            <button
              onClick={() => setActiveTab('bag-requests')}
              className={`py-4 text-xs uppercase tracking-wider ${
                activeTab === 'bag-requests' 
                  ? 'border-b border-black font-medium' 
                  : 'text-gray-500 hover:text-black'
              }`}
            >
              Bag Requests
            </button>
            <button
              onClick={() => setActiveTab('notifications')}
              className={`py-4 text-xs uppercase tracking-wider ${
                activeTab === 'notifications' 
                  ? 'border-b border-black font-medium' 
                  : 'text-gray-500 hover:text-black'
              }`}
            >
              Notifications
            </button>
          </div>
        </div>
      </div>
      
      {/* Content Area */}
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
            {/* Personal Information */}
            <div>
              <h2 className="text-xl font-normal mb-8 uppercase tracking-wide">Personal Information</h2>
              
              {/* Name */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-3">
                  <p className="text-xs text-gray-500 uppercase tracking-wider">Name</p>
                  {!isEditingName && (
                    <button 
                      onClick={() => setIsEditingName(true)}
                      className="text-xs text-gray-500 hover:text-black"
                    >
                      Edit
                    </button>
                  )}
                </div>
                
                {isEditingName ? (
                  <form onSubmit={handleNameSubmit} className="space-y-4">
                    <div className="space-y-4">
                      <input
                        type="text"
                        id="first_name"
                        placeholder="First Name"
                        value={nameForm.first_name}
                        onChange={(e) => setNameForm({...nameForm, first_name: e.target.value})}
                        className="w-full py-2 border-b border-gray-200 focus:outline-none focus:border-gray-400 text-sm"
                      />
                      <input
                        type="text"
                        id="last_name"
                        placeholder="Last Name"
                        value={nameForm.last_name}
                        onChange={(e) => setNameForm({...nameForm, last_name: e.target.value})}
                        className="w-full py-2 border-b border-gray-200 focus:outline-none focus:border-gray-400 text-sm"
                      />
                    </div>
                    <div className="flex space-x-4 pt-2">
                      <button
                        type="button"
                        onClick={() => setIsEditingName(false)}
                        className="text-xs text-gray-500"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="text-xs font-medium"
                        disabled={saving}
                      >
                        {saving ? 'Saving...' : 'Save'}
                      </button>
                    </div>
                  </form>
                ) : (
                  <p className="text-sm">
                    {profile ? 
                     `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || profile.full_name || 'Not provided' 
                     : user?.user_metadata?.full_name || 'Not provided'}
                  </p>
                )}
              </div>
              
              {/* Email */}
              <div className="mb-8">
                <p className="text-xs text-gray-500 uppercase tracking-wider mb-3">Email</p>
                <p className="text-sm">{user?.email}</p>
              </div>
              
              {/* Admin Badge */}
              {profile?.is_admin && (
                <div className="inline-block border border-black px-3 py-1 text-xs uppercase tracking-wider">
                  Admin
                </div>
              )}
              
              {/* Sign Out */}
              <div className="mt-8">
                <button 
                  onClick={handleSignOut}
                  className="text-xs underline hover:no-underline"
                >
                  Sign Out
                </button>
              </div>
            </div>
            
            {/* Address Information */}
            <div>
              <h2 className="text-xl font-normal mb-8 uppercase tracking-wide">Address Information</h2>
              
              <div className="mb-8">
                <div className="flex justify-between items-center mb-3">
                  <p className="text-xs text-gray-500 uppercase tracking-wider">Address</p>
                  {!isEditingAddress && (
                    <button 
                      onClick={() => setIsEditingAddress(true)}
                      className="text-xs text-gray-500 hover:text-black"
                    >
                      Edit
                    </button>
                  )}
                </div>
                
                {isEditingAddress ? (
                  <form onSubmit={handleAddressSubmit} className="space-y-4">
                    <input
                      type="text"
                      id="street_address"
                      placeholder="Street Address"
                      value={addressForm.street_address}
                      onChange={(e) => setAddressForm({...addressForm, street_address: e.target.value})}
                      className="w-full py-2 border-b border-gray-200 focus:outline-none focus:border-gray-400 text-sm"
                    />
                    <input
                      type="text"
                      id="city"
                      placeholder="City"
                      value={addressForm.city}
                      onChange={(e) => setAddressForm({...addressForm, city: e.target.value})}
                      className="w-full py-2 border-b border-gray-200 focus:outline-none focus:border-gray-400 text-sm"
                    />
                    <div className="grid grid-cols-2 gap-4">
                      <input
                        type="text"
                        id="state"
                        placeholder="State/Province"
                        value={addressForm.state}
                        onChange={(e) => setAddressForm({...addressForm, state: e.target.value})}
                        className="w-full py-2 border-b border-gray-200 focus:outline-none focus:border-gray-400 text-sm"
                      />
                      <input
                        type="text"
                        id="postal_code"
                        placeholder="Postal Code"
                        value={addressForm.postal_code}
                        onChange={(e) => setAddressForm({...addressForm, postal_code: e.target.value})}
                        className="w-full py-2 border-b border-gray-200 focus:outline-none focus:border-gray-400 text-sm"
                      />
                    </div>
                    <input
                      type="text"
                      id="country"
                      placeholder="Country"
                      value={addressForm.country}
                      onChange={(e) => setAddressForm({...addressForm, country: e.target.value})}
                      className="w-full py-2 border-b border-gray-200 focus:outline-none focus:border-gray-400 text-sm"
                    />
                    <input
                      type="tel"
                      id="phone"
                      placeholder="Phone Number"
                      value={addressForm.phone}
                      onChange={(e) => setAddressForm({...addressForm, phone: e.target.value})}
                      className="w-full py-2 border-b border-gray-200 focus:outline-none focus:border-gray-400 text-sm"
                    />
                    <div className="flex space-x-4 pt-2">
                      <button
                        type="button"
                        onClick={() => setIsEditingAddress(false)}
                        className="text-xs text-gray-500"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="text-xs font-medium"
                        disabled={saving}
                      >
                        {saving ? 'Saving...' : 'Save'}
                      </button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-1">
                    <p className="text-sm">
                      {profile?.street_address || 'No address provided'}
                    </p>
                    {profile?.city && (
                      <p className="text-sm">
                        {profile.city}
                        {profile.state && `, ${profile.state}`} 
                        {profile.postal_code && ` ${profile.postal_code}`}
                      </p>
                    )}
                    {profile?.country && (
                      <p className="text-sm">{profile.country}</p>
                    )}
                    {profile?.phone && (
                      <p className="text-sm mt-2">{profile.phone}</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        
        {/* Wishlist Tab */}
        {activeTab === 'wishlist' && (
          <div>
            <h2 className="text-2xl font-light mb-8">My Wishlist</h2>
            
            {wishlistItems.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 mb-4">Your wishlist is empty</p>
                <Link 
                  href="/collection" 
                  className="inline-block bg-black text-white px-6 py-2 text-sm hover:bg-gray-800 transition-colors"
                >
                  Explore Collection
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {wishlistItems.map((item) => (
                  <div key={item.id} className="group">
                    <div className="relative aspect-square overflow-hidden bg-gray-100 mb-3">
                      {item.product?.image_url ? (
                        <Image
                          src={item.product.image_url}
                          alt={item.product.name}
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                          No image
                        </div>
                      )}
                      <button 
                        onClick={() => handleRemoveFromWishlist(item.product_id)}
                        className="absolute top-2 right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                        aria-label="Remove from wishlist"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </button>
                    </div>
                    <h3 className="font-medium">{item.product?.name || 'Product'}</h3>
                    <p className="text-gray-900">${item.product?.price ? item.product.price.toFixed(2) : '0.00'}</p>
                    {item.product?.slug && (
                      <Link 
                        href={`/product/${item.product.slug}`}
                        className="mt-2 inline-block text-sm underline hover:no-underline"
                      >
                        View Details
                      </Link>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        
        {/* Orders Tab */}
        {activeTab === 'orders' && (
          <div>
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl font-light">Order History</h2>
              {orderError && (
                <button
                  onClick={() => user?.id && loadOrderHistory(user.id)}
                  className="text-sm text-gray-600 hover:text-black"
                >
                  Refresh Orders
                </button>
              )}
            </div>
            
            {orderError ? (
              <div className="text-center py-8">
                <p className="text-red-600 mb-4">{orderError}</p>
                <button
                  onClick={() => user?.id && loadOrderHistory(user.id)}
                  className="inline-block bg-black text-white px-6 py-2 text-sm hover:bg-gray-800 transition-colors"
                >
                  Try Again
                </button>
              </div>
            ) : orderHistory.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 mb-4">You haven't placed any orders yet</p>
                <Link 
                  href="/collection" 
                  className="inline-block bg-black text-white px-6 py-2 text-sm hover:bg-gray-800 transition-colors"
                >
                  Start Shopping
                </Link>
              </div>
            ) : (
              <div className="space-y-8">
                {orderHistory.map((order: Order) => {
                  // Log the order data for debugging
                  console.log('Rendering order:', order);
                  
                  return (
                    <div key={order.id} className="border border-gray-200 rounded-md overflow-hidden">
                      <div className="bg-gray-50 p-4 flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500">Order #{order.order_number || order.id.substring(0, 8)}</p>
                          <p className="text-sm text-gray-500">
                            {new Date(order.created_at || '').toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">€{Number(order.total_amount).toFixed(2)}</p>
                          <p className={`inline-block px-2 py-1 text-xs rounded ${getOrderStatusStyle(order.status, order.payment_status)}`}>
                            {getOrderStatusLabel(order.status, order.payment_status)}
                          </p>
                          {(order.status === 'cancelled' && order.payment_status === 'incomplete') || order.status === 'pending' ? (
                            <button
                              onClick={() => handleRetryPayment(order)}
                              className="mt-2 text-xs text-blue-600 hover:text-blue-800 underline"
                            >
                              Complete Payment
                            </button>
                          ) : null}
                        </div>
                      </div>
                      <div className="p-4">
                        <h3 className="text-sm font-medium mb-3">Items</h3>
                        <div className="space-y-4">
                          {(order.items || []).map((item: OrderItem) => {
                            // Log the item data for debugging
                            console.log('Rendering item:', item);
                            
                            return (
                              <div key={item.id} className="flex items-center">
                                <div className="w-16 h-16 bg-gray-100 mr-4 relative flex-shrink-0">
                                  {item.product?.image_url ? (
                                    <Image
                                      src={item.product.image_url}
                                      alt={item.product.name || 'Product image'}
                                      fill
                                      sizes="64px"
                                      className="object-cover"
                                    />
                                  ) : (
                                    <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                                      No image
                                    </div>
                                  )}
                                </div>
                                <div className="flex-grow">
                                  <h4 className="font-medium">{item.product?.name || 'Product name not available'}</h4>
                                  <p className="text-sm text-gray-500">
                                    Qty: {item.quantity} × €{Number(item.price).toFixed(2)}
                                  </p>
                                  <p className="text-sm text-gray-500">
                                    Subtotal: €{(Number(item.quantity) * Number(item.price)).toFixed(2)}
                                  </p>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                      {order.tracking_url && (
                        <div className="border-t border-gray-200 p-4">
                          <a 
                            href={order.tracking_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800"
                          >
                            Track Order
                          </a>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}
        
        {/* Wardrobe Tab */}
        {activeTab === 'bag-requests' && (
          <div>
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl font-light">Bag Requests</h2>
              <button 
                onClick={() => setIsAddingWardrobeItem(true)}
                className="bg-black text-white px-4 py-2 text-sm hover:bg-gray-800 transition-colors"
              >
                Request New Bag
              </button>
            </div>
            
            {isAddingWardrobeItem ? (
              <div className="bg-gray-50 p-6 mb-8 rounded-md">
                <h3 className="text-xl font-light mb-4">Request a Bag</h3>
                <form onSubmit={handleAddWardrobeItem} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="name" className="block text-xs text-gray-500 uppercase mb-1">
                        Bag Name/Description
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={newWardrobeItem.name}
                        onChange={(e) => setNewWardrobeItem({...newWardrobeItem, name: e.target.value})}
                        className="w-full p-2 border border-gray-200 focus:border-black focus:ring-0"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="category" className="block text-xs text-gray-500 uppercase mb-1">
                        Category
                      </label>
                      <select
                        id="category"
                        value={newWardrobeItem.category}
                        onChange={(e) => setNewWardrobeItem({...newWardrobeItem, category: e.target.value})}
                        className="w-full p-2 border border-gray-200 focus:border-black focus:ring-0"
                        required
                      >
                        <option value="handbags">Handbags</option>
                        <option value="clutches">Clutches</option>
                        <option value="totes">Totes</option>
                        <option value="crossbody">Crossbody</option>
                        <option value="backpacks">Backpacks</option>
                        <option value="wallets">Wallets</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="color" className="block text-xs text-gray-500 uppercase mb-1">
                        Color
                      </label>
                      <div className="flex items-center">
                        <input
                          type="color"
                          id="color"
                          value={newWardrobeItem.color}
                          onChange={(e) => setNewWardrobeItem({...newWardrobeItem, color: e.target.value})}
                          className="w-10 h-10 p-0 border-0"
                        />
                        <span className="ml-2 text-sm">{newWardrobeItem.color}</span>
                      </div>
                    </div>
                    <div>
                      <label htmlFor="brand" className="block text-xs text-gray-500 uppercase mb-1">
                        Brand
                      </label>
                      <input
                        type="text"
                        id="brand"
                        value={newWardrobeItem.brand}
                        onChange={(e) => setNewWardrobeItem({...newWardrobeItem, brand: e.target.value})}
                        className="w-full p-2 border border-gray-200 focus:border-black focus:ring-0"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Priority
                      </label>
                      <select
                        value={newWardrobeItem.priority}
                        onChange={(e) => setNewWardrobeItem({...newWardrobeItem, priority: e.target.value})}
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      >
                        <option value="">Select Priority</option>
                        <option value="low">Low - No Rush</option>
                        <option value="medium">Medium - Within a Month</option>
                        <option value="high">High - As Soon as Possible</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <label htmlFor="notes" className="block text-xs text-gray-500 uppercase mb-1">
                      Additional Notes
                    </label>
                    <textarea
                      id="notes"
                      value={newWardrobeItem.notes}
                      onChange={(e) => setNewWardrobeItem({...newWardrobeItem, notes: e.target.value})}
                      className="w-full p-2 border border-gray-200 focus:border-black focus:ring-0"
                      rows={3}
                      placeholder="Include any specific details about the bag, where you saw it, links, etc."
                    ></textarea>
                  </div>
                  <div>
                    <label htmlFor="image" className="block text-xs text-gray-500 uppercase mb-1">
                      Image
                    </label>
                    <div className="flex items-center space-x-4">
                      <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="border border-gray-200 px-4 py-2 text-sm hover:bg-gray-50 transition-colors"
                      >
                        Choose File
                      </button>
                      <input
                        type="file"
                        id="image"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        className="hidden"
                        accept="image/*"
                      />
                      <span className="text-sm text-gray-500">
                        {selectedFile ? selectedFile.name : 'No file chosen'}
                      </span>
                    </div>
                    {previewUrl && (
                      <div className="mt-2 relative w-24 h-24 bg-gray-100">
                        <Image
                          src={previewUrl}
                          alt="Preview"
                          fill
                          sizes="96px"
                          className="object-cover"
                        />
                      </div>
                    )}
                  </div>
                  <div className="flex space-x-4 pt-2">
                    <button
                      type="submit"
                      disabled={saving}
                      className="bg-black text-white px-6 py-2 text-sm hover:bg-gray-800 transition-colors disabled:opacity-50"
                    >
                      {saving ? 'Saving...' : 'Add to Wardrobe'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsAddingWardrobeItem(false)}
                      className="border border-gray-200 px-6 py-2 text-sm hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            ) : null}
            
            {wardrobeItems.length === 0 && !isAddingWardrobeItem ? (
              <div className="text-center py-12">
                <p className="text-gray-500 mb-4">You haven't requested any bags yet</p>
                <button 
                  onClick={() => setIsAddingWardrobeItem(true)}
                  className="inline-block bg-black text-white px-6 py-2 text-sm hover:bg-gray-800 transition-colors"
                >
                  Request Your First Bag
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {wardrobeItems.map((item) => {
                  console.log('[Account] Rendering wardrobe item:', item, 'Status:', item.status);
                  return (
                  <div key={item.id} className="group border border-gray-200 rounded-md overflow-hidden">
                    <div 
                      className="relative aspect-square overflow-hidden"
                      style={{ backgroundColor: item.color || '#f3f4f6' }}
                    >
                      {item.image_url ? (
                        <Image
                          src={item.image_url}
                          alt={item.name}
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center text-white">
                          No image
                        </div>
                      )}
                      <button 
                        onClick={() => handleDeleteWardrobeItem(item.id)}
                        className="absolute top-2 right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                        aria-label="Delete item"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </button>
                    </div>
                    <div className="p-3">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium">{item.name}</h3>
                        <span className={`inline-block px-2 py-1 text-xs font-bold rounded-full capitalize whitespace-nowrap ${
                          item.status === 'approved' ? 'bg-green-100 text-green-800' :
                          item.status === 'rejected' ? 'bg-red-100 text-red-800' :
                          item.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.status === 'in_progress' ? 'In Progress' : (item.status || 'pending')}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 capitalize">{item.category}</p>
                      {item.brand && <p className="text-sm text-gray-700">{item.brand}</p>}
                      {item.priority && (
                        <p className="text-sm text-gray-700">
                          Priority: <span className="capitalize">{item.priority}</span>
                        </p>
                      )}
                      {item.notes && (
                        <p className="text-xs text-gray-600 mt-2">
                          {item.notes}
                        </p>
                      )}
                    </div>
                  </div>
                  );
                })}
              </div>
            )}
          </div>
        )}
        
        {/* Notifications Tab */}
        {activeTab === 'notifications' && (
          <div>
            <h2 className="text-2xl font-light mb-8">Notification Preferences</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Order & Status Updates */}
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Order & Status Updates</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Order Status Updates</label>
                      <p className="text-xs text-gray-500">Get notified when your order status changes</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={notificationPreferences.order_updates}
                      onChange={(e) => setNotificationPreferences(prev => ({
                        ...prev,
                        order_updates: e.target.checked
                      }))}
                      className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Bag Request Updates</label>
                      <p className="text-xs text-gray-500">Get notified about your bag request status</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={notificationPreferences.bag_request_updates}
                      onChange={(e) => setNotificationPreferences(prev => ({
                        ...prev,
                        bag_request_updates: e.target.checked
                      }))}
                      className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                    />
                  </div>
                </div>
              </div>
              
              {/* Marketing & Promotions */}
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Marketing & Promotions</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Promotional Emails</label>
                      <p className="text-xs text-gray-500">Receive emails about new arrivals and sales</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={notificationPreferences.promotional_emails}
                      onChange={(e) => setNotificationPreferences(prev => ({
                        ...prev,
                        promotional_emails: e.target.checked
                      }))}
                      className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">SMS Marketing</label>
                      <p className="text-xs text-gray-500">Receive text messages about exclusive offers</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={notificationPreferences.marketing_sms}
                      onChange={(e) => setNotificationPreferences(prev => ({
                        ...prev,
                        marketing_sms: e.target.checked
                      }))}
                      className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            {/* Save Button */}
            <div className="mt-8">
              <button
                onClick={() => {
                  // TODO: Save notification preferences to database
                  toast.success('Notification preferences updated');
                }}
                className="bg-black text-white px-6 py-2 text-sm hover:bg-gray-800 transition-colors"
              >
                Save Preferences
              </button>
            </div>
            
            {/* Recent Notifications */}
            <div className="mt-12">
              <h3 className="text-xl font-light mb-6">Recent Notifications</h3>
              <div className="bg-white border border-gray-200 rounded-lg">
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Order Status Update</p>
                        <p className="text-xs text-gray-600">Your order #12345 has been shipped</p>
                        <p className="text-xs text-gray-400 mt-1">2 hours ago</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Bag Request Approved</p>
                        <p className="text-xs text-gray-600">Your Louis Vuitton bag request has been approved</p>
                        <p className="text-xs text-gray-400 mt-1">1 day ago</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className="w-2 h-2 bg-gray-400 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Welcome to Mai Mi</p>
                        <p className="text-xs text-gray-600">Thank you for joining our exclusive community</p>
                        <p className="text-xs text-gray-400 mt-1">3 days ago</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}