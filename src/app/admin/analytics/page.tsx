'use client';

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import DailyProductViewsChart from '@/components/analytics/DailyProductViewsChart';
import TopViewedProductsChart from '@/components/analytics/TopViewedProductsChart';
import ViewsPerDayChart from '@/components/analytics/ViewsPerDayChart';
import RecentProductViewsTable from '@/components/analytics/RecentProductViewsTable';

interface ProductAnalytics {
  id: string;
  name: string;
  views: number;
  revenue: number;
}

interface CategoryAnalytics {
  name: string;
  count: number;
  percentage: number;
}

interface ViewEvent {
  product_name: string;
  viewed_at: string;
  user_email: string | null;
}

interface WeeklyTopProduct {
  name: string;
  views: number;
  day: string;
}

export default function AnalyticsPage() {
  const supabase = createClientComponentClient();
  const [analytics, setAnalytics] = useState({
    topProducts: [] as ProductAnalytics[],
    categoryBreakdown: [] as CategoryAnalytics[],
    recentViews: [] as ViewEvent[],
    weeklyTopProducts: [] as WeeklyTopProduct[],
    isLoading: true,
  });
  
  // Add tabs for different analytics views
  const [activeTab, setActiveTab] = useState<'overview' | 'products' | 'categories' | 'views'>('overview');

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      console.log('Fetching analytics data...');
      
      // Run queries in parallel for better performance
      const [productsResult, categoriesResult, viewsResult] = await Promise.all([
        supabase.from('products').select(`id, name, price, product_views(count)`),
        supabase.from('categories').select(`name, products(count)`),
        supabase.from('product_views').select(`
          products:product_id(name), 
          viewed_at, 
          profiles:user_id(id, email)
        `).order('viewed_at', { ascending: false }).limit(10)
      ]);

      // Process products data
      const topProducts = productsResult.data?.map(product => ({
        id: product.id,
        name: product.name,
        views: product.product_views?.length ? product.product_views[0].count : 0,
        revenue: product.price * (product.product_views?.length ? product.product_views[0].count : 0) * 0.1 // Assuming 10% conversion rate
      })).sort((a, b) => b.views - a.views).slice(0, 5) || [];

      // Process categories data
      const totalProducts = categoriesResult.data?.reduce((sum, category) => sum + (category.products?.length || 0), 0) || 0;
      const categoryBreakdown = categoriesResult.data?.map(category => ({
        name: category.name,
        count: category.products?.length || 0,
        percentage: totalProducts > 0 ? ((category.products?.length || 0) / totalProducts) * 100 : 0
      })).sort((a, b) => b.count - a.count) || [];

      // Process recent views
      const recentViews = viewsResult.data?.map(view => {
        // Safely extract product name
        let productName = 'Unknown Product';
        if (view.products) {
          if (Array.isArray(view.products) && view.products.length > 0) {
            productName = view.products[0]?.name || 'Unknown Product';
          } else if (typeof view.products === 'object' && view.products !== null) {
            productName = (view.products as any).name || 'Unknown Product';
          }
        }
        
        // Safely extract user email
        let userEmail = 'Anonymous';
        if (view.profiles) {
          if (Array.isArray(view.profiles) && view.profiles.length > 0) {
            userEmail = view.profiles[0]?.email || 'Anonymous';
          } else if (typeof view.profiles === 'object' && view.profiles !== null) {
            userEmail = (view.profiles as any).email || 'Anonymous';
          }
        }
        
        return {
          product_name: productName,
          viewed_at: view.viewed_at ? new Date(view.viewed_at).toLocaleString() : 'Unknown Date',
          user_email: userEmail
        };
      }) || [];

      setAnalytics({
        topProducts,
        categoryBreakdown,
        recentViews,
        weeklyTopProducts: [],
        isLoading: false,
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setAnalytics(prev => ({ ...prev, isLoading: false }));
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-4">Top Products by Views</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={analytics.topProducts}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="views" fill="#8884d8" name="Views" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-4">Category Breakdown</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={analytics.categoryBreakdown}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="count" fill="#82ca9d" name="Products" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 gap-6 mb-6">
              <ViewsPerDayChart days={14} />
            </div>
            
            <div className="grid grid-cols-1 gap-6">
              <RecentProductViewsTable limit={10} />
            </div>
          </>
        );
        
      case 'products':
        return (
          <>
            <div className="grid grid-cols-1 gap-6 mb-6">
              <TopViewedProductsChart days={7} />
            </div>
            <div className="grid grid-cols-1 gap-6">
              <DailyProductViewsChart />
            </div>
          </>
        );
        
      case 'categories':
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Category Breakdown</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Products
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Percentage
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {analytics.categoryBreakdown.map((category, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {category.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {category.count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {category.percentage.toFixed(1)}%
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );
        
      case 'views':
        return (
          <>
            <div className="grid grid-cols-1 gap-6 mb-6">
              <ViewsPerDayChart days={30} />
            </div>
            <div className="grid grid-cols-1 gap-6">
              <RecentProductViewsTable limit={20} />
            </div>
          </>
        );
        
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Analytics Dashboard</h1>
      
      {analytics.isLoading ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-gray-500">Loading analytics data...</p>
        </div>
      ) : (
        <>
          {/* Analytics Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-sm font-medium text-gray-500 mb-1">Total Products</h3>
              <p className="text-2xl font-bold">{analytics.topProducts.length}</p>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-sm font-medium text-gray-500 mb-1">Total Categories</h3>
              <p className="text-2xl font-bold">{analytics.categoryBreakdown.length}</p>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-sm font-medium text-gray-500 mb-1">Total Views</h3>
              <p className="text-2xl font-bold">
                {analytics.topProducts.reduce((sum, product) => sum + product.views, 0)}
              </p>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-sm font-medium text-gray-500 mb-1">Estimated Revenue</h3>
              <p className="text-2xl font-bold">
                ${analytics.topProducts.reduce((sum, product) => sum + product.revenue, 0).toFixed(2)}
              </p>
            </div>
          </div>
          
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('overview')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('products')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'products'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Product Analytics
              </button>
              <button
                onClick={() => setActiveTab('categories')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'categories'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Categories
              </button>
              <button
                onClick={() => setActiveTab('views')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'views'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                View Analytics
              </button>
            </nav>
          </div>
          
          {/* Tab Content */}
          {renderTabContent()}
        </>
      )}
    </div>
  );
}
