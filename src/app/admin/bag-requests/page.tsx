'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/database.types';
import Image from 'next/image';
import toast from 'react-hot-toast';
import { Eye, X, CheckCircle, XCircle } from 'lucide-react';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

type WardrobeItem = Database['public']['Tables']['wardrobe_items']['Row'] & {
  profiles?: {
    id: string;
    email: string;
    first_name: string | null;
    last_name: string | null;
  } | null;
};

// Add type safety for status values
type BagRequestStatus = 'pending' | 'approved' | 'rejected';

// Add status configuration
const STATUS_CONFIG = {
  pending: {
    label: 'Pending',
    color: 'bg-yellow-100 text-yellow-800',
    icon: null
  },
  approved: {
    label: 'Approved',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  rejected: {
    label: 'Rejected',
    color: 'bg-red-100 text-red-800',
    icon: XCircle
  }
} as const;

export default function BagRequestsPage() {
  const [items, setItems] = useState<WardrobeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState<WardrobeItem | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [saving, setSaving] = useState(false);
  const supabase = createClientComponentClient<Database>();

  const loadBagRequests = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('wardrobe_items')
        .select(`
          *,
          profiles (
            id,
            email,
            first_name,
            last_name
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setItems(data || []);
    } catch (error) {
      console.error('Error loading bag requests:', error);
      toast.error('Failed to load bag requests');
    } finally {
      setLoading(false);
    }
  };

  // Set up realtime subscription
  useEffect(() => {
    // Initial load
    loadBagRequests();

    // Set up realtime subscription
    const channel = supabase
      .channel('wardrobe_items_changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'wardrobe_items'
        },
        async (payload: RealtimePostgresChangesPayload<Database['public']['Tables']['wardrobe_items']['Row']>) => {
          console.log('Realtime change received:', payload);
          
          // Reload the entire list to ensure we have the latest data
          // including any related profiles data
          await loadBagRequests();
          
          // Type guard to ensure payload.new exists and has the correct shape
          const isValidPayload = (
            payload: RealtimePostgresChangesPayload<Database['public']['Tables']['wardrobe_items']['Row']>
          ): payload is RealtimePostgresChangesPayload<Database['public']['Tables']['wardrobe_items']['Row']> & { new: Database['public']['Tables']['wardrobe_items']['Row'] } => {
            return payload.new !== null && typeof payload.new === 'object' && 'id' in payload.new;
          };
          
          // If we have a selected item and it was updated, refresh its details
          if (selectedItem && isValidPayload(payload) && payload.new.id === selectedItem.id) {
            const { data } = await supabase
              .from('wardrobe_items')
              .select(`
                *,
                profiles (
                  id,
                  email,
                  first_name,
                  last_name
                )
              `)
              .eq('id', selectedItem.id)
              .single();
              
            if (data) {
              setSelectedItem(data);
            }
          }
        }
      )
      .subscribe();

    // Cleanup subscription on component unmount
    return () => {
      supabase.removeChannel(channel);
    };
  }, [selectedItem]); // Add selectedItem as dependency since we use it in the callback

  const handleViewDetails = (item: WardrobeItem) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedItem(null);
  };

  const handleUpdateStatus = async (itemId: string, newStatus: BagRequestStatus) => {
    if (!itemId) {
      toast.error('Invalid item ID');
      return;
    }

    const loadingToastId = toast.loading(`Updating status to ${STATUS_CONFIG[newStatus].label}...`);
    setSaving(true);

    // Store the original item for potential rollback
    const itemToUpdate = items.find(item => item.id === itemId);
    if (!itemToUpdate) {
      toast.error('Item not found');
      setSaving(false);
      return;
    }

    try {
      // Optimistically update the UI
      setItems(prevItems => 
        prevItems.map(item => 
          item.id === itemId 
            ? { ...item, status: newStatus, updated_at: new Date().toISOString() } 
            : item
        )
      );

      // If this is the selected item in the modal, update it too
      if (selectedItem?.id === itemId) {
        setSelectedItem(prev => prev ? { ...prev, status: newStatus, updated_at: new Date().toISOString() } : null);
      }

      // Update the database - no need for .select() as we use realtime subscription
      const { error: updateError } = await supabase
        .from('wardrobe_items')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', itemId);

      if (updateError) {
        throw updateError;
      }

      // Send user notification about status update
      try {
        if (itemToUpdate.profiles?.email) {
          const response = await fetch('/api/notifications/bag-request-status', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              user_email: itemToUpdate.profiles.email,
              user_name: `${itemToUpdate.profiles.first_name || ''} ${itemToUpdate.profiles.last_name || ''}`.trim() || 'User',
              bag_name: itemToUpdate.name,
              old_status: itemToUpdate.status,
              new_status: newStatus
            })
          });

          if (response.ok) {
            console.log('User notification sent successfully');
          } else {
            console.error('Failed to send user notification');
          }
        }
      } catch (notificationError) {
        console.error('Error sending user notification:', notificationError);
        // Don't fail the status update if notification fails
      }

      toast.success(`Status updated to ${STATUS_CONFIG[newStatus].label}`, {
        id: loadingToastId
      });
    } catch (error) {
      // Rollback optimistic update on error
      setItems(prevItems =>
        prevItems.map(item =>
          item.id === itemId
            ? { ...itemToUpdate }  // Restore the original item state
            : item
        )
      );
      
      // Rollback selected item if needed
      if (selectedItem?.id === itemId) {
        setSelectedItem(itemToUpdate);
      }

      console.error('Error updating status:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update status', {
        id: loadingToastId
      });
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Bag Requests</h1>
      
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Brand</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {items.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">
                        {item.profiles?.first_name} {item.profiles?.last_name}
                      </div>
                      <div className="text-gray-500">{item.profiles?.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {item.image_url && (
                        <div className="flex-shrink-0 h-12 w-12 mr-4">
                          <Image
                            src={item.image_url}
                            alt={item.name}
                            width={48}
                            height={48}
                            className="h-12 w-12 rounded-md object-cover"
                          />
                        </div>
                      )}
                      <div className="text-sm text-gray-900">{item.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{item.category}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{item.brand || 'N/A'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      item.priority === 'high' 
                        ? 'bg-red-100 text-red-800' 
                        : item.priority === 'medium'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {item.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${STATUS_CONFIG[item.status as BagRequestStatus]?.color || STATUS_CONFIG.pending.color}`}>
                      {STATUS_CONFIG[item.status as BagRequestStatus]?.label || 'Unknown'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => handleViewDetails(item)}
                        className="inline-flex items-center px-3 py-1 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50"
                      >
                        <Eye size={16} className="mr-1" />
                        View
                      </button>
                      <div className="flex space-x-2">
                        {item.status !== 'approved' && (
                          <button
                            onClick={() => handleUpdateStatus(item.id, 'approved')}
                            className="p-1 text-green-600 hover:text-green-800 transition-colors disabled:opacity-50"
                            title="Approve"
                            disabled={saving}
                          >
                            <CheckCircle size={20} />
                          </button>
                        )}
                        {item.status !== 'rejected' && (
                          <button
                            onClick={() => handleUpdateStatus(item.id, 'rejected')}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors disabled:opacity-50"
                            title="Reject"
                            disabled={saving}
                          >
                            <XCircle size={20} />
                          </button>
                        )}
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {items.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No bag requests found
          </div>
        )}
      </div>

      {/* Details Modal */}
      {isModalOpen && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-xl font-semibold">Bag Request Details</h2>
              <button
                onClick={handleCloseModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={24} />
              </button>
            </div>
            
            <div className="p-6 space-y-6">
              {/* Customer Information */}
              <div>
                <h3 className="text-lg font-medium mb-3">Customer Information</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                  <p>
                    <span className="font-medium">Name: </span>
                    {selectedItem.profiles 
                      ? `${selectedItem.profiles.first_name || ''} ${selectedItem.profiles.last_name || ''}`
                      : 'Unknown User'}
                  </p>
                  <p>
                    <span className="font-medium">Email: </span>
                    {selectedItem.profiles?.email || 'N/A'}
                  </p>
                </div>
              </div>

              {/* Item Details */}
              <div>
                <h3 className="text-lg font-medium mb-3">Item Details</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  {selectedItem.image_url && (
                    <div className="mb-4">
                      <Image
                        src={selectedItem.image_url}
                        alt={selectedItem.name}
                        width={300}
                        height={300}
                        className="rounded-lg object-cover"
                      />
                    </div>
                  )}
                  <p><span className="font-medium">Item Name: </span>{selectedItem.name}</p>
                  <p><span className="font-medium">Brand: </span>{selectedItem.brand || 'N/A'}</p>
                  <p><span className="font-medium">Category: </span>{selectedItem.category}</p>
                  <p><span className="font-medium">Priority: </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      selectedItem.priority === 'high' 
                        ? 'bg-red-100 text-red-800' 
                        : selectedItem.priority === 'medium'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {selectedItem.priority}
                    </span>
                  </p>
                  <p><span className="font-medium">Status: </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${STATUS_CONFIG[selectedItem.status as BagRequestStatus]?.color || STATUS_CONFIG.pending.color}`}>
                      {STATUS_CONFIG[selectedItem.status as BagRequestStatus]?.label || 'Unknown'}
                    </span>
                  </p>
                  {selectedItem.color && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Color: </span>
                      <div 
                        className="w-6 h-6 rounded-full border"
                        style={{ backgroundColor: selectedItem.color }}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Notes */}
              {selectedItem.notes && (
                <div>
                  <h3 className="text-lg font-medium mb-3">Notes</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p>{selectedItem.notes}</p>
                  </div>
                </div>
              )}

              {/* Timestamps */}
              <div>
                <h3 className="text-lg font-medium mb-3">Timestamps</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                  <p><span className="font-medium">Created: </span>{formatDate(selectedItem.created_at)}</p>
                  <p><span className="font-medium">Last Updated: </span>{formatDate(selectedItem.updated_at)}</p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  onClick={() => handleUpdateStatus(selectedItem.id, 'approved')}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={selectedItem.status === 'approved' || saving}
                >
                  <CheckCircle size={18} className="mr-2" />
                  {saving ? 'Updating...' : 'Approve'}
                </button>
                <button
                  onClick={() => handleUpdateStatus(selectedItem.id, 'rejected')}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={selectedItem.status === 'rejected' || saving}
                >
                  <XCircle size={18} className="mr-2" />
                  {saving ? 'Updating...' : 'Reject'}
                </button>
                <button
                  onClick={handleCloseModal}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}