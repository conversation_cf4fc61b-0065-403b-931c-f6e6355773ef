'use client';

import { useState, useEffect, FormEvent } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/database.types';

// Define the database category type
type DatabaseCategory = {
  id: string;
  name: string;
  description?: string | null;
  slug?: string | null;
  image_url?: string | null;
  created_at?: string | null;
  parent_id?: string | null;
};

// Define our UI category type
type Category = {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  image_url: string | null;
  created_at: string;
  parent_id?: string | null;
};

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    slug: '',
    image_url: ''
  });
  
  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);
  
  // Function to fetch categories from the database
  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const supabase = createClientComponentClient<Database>();
      
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');
        
      if (error) {
        throw error;
      }
      
      // Map the data to ensure it matches our Category type
      const mappedCategories: Category[] = (data as DatabaseCategory[] || []).map(item => ({
        id: item.id,
        name: item.name,
        description: item.description || null,
        slug: item.slug || item.name.toLowerCase().replace(/\s+/g, '-'),
        image_url: item.image_url || null,
        created_at: item.created_at || new Date().toISOString(),
        parent_id: item.parent_id || null
      }));
      
      setCategories(mappedCategories);
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError('Failed to load categories. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // Auto-generate slug from name if the name field is being changed and we're creating a new category
    if (name === 'name' && !isEditing) {
      setFormData({
        ...formData,
        name: value,
        slug: value.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };
  
  // Start editing a category
  const startEditing = (category: Category) => {
    setIsEditing(category.id);
    setFormData({
      name: category.name,
      description: category.description || '',
      slug: category.slug,
      image_url: category.image_url || ''
    });
    setIsCreating(false);
  };
  
  // Cancel editing or creating
  const cancelAction = () => {
    setIsEditing(null);
    setIsCreating(false);
    setFormData({
      name: '',
      description: '',
      slug: '',
      image_url: ''
    });
  };
  
  // Submit form to create or update a category
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.slug) {
      setError('Name and slug are required.');
      return;
    }
    
    try {
      const supabase = createClientComponentClient<Database>();
      
      if (isEditing) {
        // Update existing category
        const { error } = await supabase
          .from('categories')
          .update({
            name: formData.name,
            description: formData.description || null,
            slug: formData.slug,
            image_url: formData.image_url || null,
            updated_at: new Date().toISOString()
          })
          .eq('id', isEditing);
          
        if (error) {
          console.error('Insert category error:', error.message);
          throw error;
        }
      } else {
        // Create new category
        const { error } = await supabase
          .from('categories')
          .insert({
            name: formData.name,
            description: formData.description || null,
            slug: formData.slug,
            image_url: formData.image_url || null
          });
          
        if (error) {
          console.error('Insert category error:', error.message);
          throw error;
        }
      }
      
      // Refresh categories list
      await fetchCategories();
      
      // Reset form
      cancelAction();
    } catch (err) {
      console.error('Error saving category:', (err as Error).message);
      setError('Failed to save category. Please try again.');
    }
  };
  
  // Delete a category
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this category? Products in this category will not be deleted but will no longer have a category assigned.')) {
      return;
    }
    
    try {
      const supabase = createClientComponentClient<Database>();
      
      const { error } = await supabase
        .from('categories')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
      
      await fetchCategories();
    } catch (err) {
      console.error('Error deleting category:', err);
      setError('Failed to delete category. Please try again.');
    }
  };

  return (
    <div className="container mx-auto">
      <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Categories</h1>
          <p className="text-gray-600 mt-1">Manage product categories</p>
        </div>
        
        {!isCreating && !isEditing && (
          <button
            onClick={() => {
              setIsCreating(true);
              setIsEditing(null);
              setFormData({
                name: '',
                description: '',
                slug: '',
                image_url: ''
              });
            }}
            className="px-4 py-2 bg-gray-800 text-white text-sm rounded-md hover:bg-gray-700 transition-colors inline-flex items-center"
          >
            <span className="mr-2">+</span> Add New Category
          </button>
        )}
      </div>
      
      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-600 rounded-md">
          {error}
        </div>
      )}
      
      {/* Category Form */}
      {(isCreating || isEditing) && (
        <div className="mb-8 bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold mb-4">
            {isCreating ? 'Create New Category' : 'Edit Category'}
          </h2>
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
                required
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Slug <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="slug"
                value={formData.slug}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Used in URLs. Only lowercase letters, numbers, and hyphens.
              </p>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
                rows={3}
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Image URL
              </label>
              <input
                type="text"
                name="image_url"
                value={formData.image_url}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={cancelAction}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700"
              >
                {isCreating ? 'Create Category' : 'Update Category'}
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* Categories Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-3 text-gray-600">Loading categories...</p>
          </div>
        ) : categories.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-600">No categories found.</p>
            {!isCreating && (
              <button
                onClick={() => setIsCreating(true)}
                className="mt-4 px-4 py-2 bg-gray-800 text-white text-sm rounded-md hover:bg-gray-700"
              >
                Add Your First Category
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Slug</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {categories.map((category) => (
                  <tr key={category.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{category.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-gray-500">{category.slug}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-gray-500 truncate max-w-xs">
                        {category.description || '—'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {category.image_url ? (
                        <a 
                          href={category.image_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-900"
                        >
                          View Image
                        </a>
                      ) : (
                        <span className="text-gray-400">—</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => startEditing(category)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(category.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
