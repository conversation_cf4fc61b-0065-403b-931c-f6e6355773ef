'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { Mail, Phone, MapPin, Calendar, ShoppingBag } from 'lucide-react';
import { customToast } from '@/components/ui/CustomToast';


// Order status labels and colors
const STATUS_LABELS = {
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  paid: { label: 'Paid', color: 'bg-green-100 text-green-800' },
  processing: { label: 'Processing', color: 'bg-blue-100 text-blue-800' },
  shipped: { label: 'Shipped', color: 'bg-purple-100 text-purple-800' },
  delivered: { label: 'Delivered', color: 'bg-emerald-100 text-emerald-800' },
  canceled: { label: 'Canceled', color: 'bg-red-100 text-red-800' },
  refunded: { label: 'Refunded', color: 'bg-gray-100 text-gray-800' },
  failed: { label: 'Failed', color: 'bg-red-100 text-red-800' },
};

export default function CustomerDetails() {
  const supabase = createClientComponentClient();
  const router = useRouter();
  const { id } = useParams();
  const customerId = Array.isArray(id) ? id[0] : id;
  
  const [customer, setCustomer] = useState<any>(null);
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [note, setNote] = useState('');
  const [savingNote, setSavingNote] = useState(false);
  
  // Fetch customer details
  useEffect(() => {
    const fetchCustomerDetails = async () => {
      try {
        setLoading(true);
        
        // Fetch customer profile
        const { data: customerData, error: customerError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', customerId)
          .single();
          
        if (customerError) throw customerError;
        
        // Try to parse admin_notes if they exist
        let parsedNote = '';
        if (customerData.metadata?.admin_notes) {
          parsedNote = customerData.metadata.admin_notes;
        }
        
        setCustomer(customerData);
        setNote(parsedNote);
        
        // Fetch customer orders
        const { data: ordersData, error: ordersError } = await supabase
          .from('orders')
          .select('*')
          .eq('user_id', customerId)
          .order('created_at', { ascending: false });
          
        if (ordersError) throw ordersError;
        
        setOrders(ordersData || []);
      } catch (error) {
        console.error('Error fetching customer details:', error);
        customToast.error('Failed to load customer details');
      } finally {
        setLoading(false);
      }
    };
    
    fetchCustomerDetails();
  }, [supabase, customerId]);
  
  // Save admin note
  const handleSaveNote = async () => {
    try {
      setSavingNote(true);
      
      // Prepare metadata object
      let metadata = customer.metadata || {};
      metadata = {
        ...metadata,
        admin_notes: note,
        last_updated: new Date().toISOString()
      };
      
      const { error } = await supabase
        .from('profiles')
        .update({
          metadata
        })
        .eq('id', customerId);
        
      if (error) throw error;
      
      // Update local state
      setCustomer({
        ...customer,
        metadata
      });
      
      customToast.success('Note saved successfully');
    } catch (error) {
      console.error('Error saving note:', error);
      customToast.error('Failed to save note');
    } finally {
      setSavingNote(false);
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  // Format order date with time
  const formatOrderDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Calculate total spend
  const calculateTotalSpend = () => {
    return orders.reduce((total, order) => {
      return total + (order.total_amount || 0);
    }, 0);
  };
  
  if (loading) {
    return (
        <div className="p-6 flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
        </div>
    );
  }
  
  if (!customer) {
    return (
        <div className="p-6">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Customer Not Found</h2>
            <p className="text-gray-600 mb-6">The requested customer could not be found.</p>
            <Link href="/admin/customers" className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333]">
              Back to Customers
            </Link>
          </div>
        </div>
    );
  }
  
  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <Link href="/admin/customers" className="text-[#171717] hover:underline flex items-center">
          <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
            Back to Customers
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="md:col-span-1">
            {/* Customer Information */}
            <div className="bg-white shadow-sm rounded-lg p-6 mb-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Customer Details</h2>
              
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  {customer.first_name} {customer.last_name}
                </h3>
                
                <div className="space-y-3">
                  <div className="flex items-start">
                    <Mail className="w-5 h-5 text-gray-500 mr-2 mt-0.5" />
                    <span className="text-gray-600">{customer.email}</span>
                  </div>
                  
                  {customer.phone && (
                    <div className="flex items-start">
                      <Phone className="w-5 h-5 text-gray-500 mr-2 mt-0.5" />
                      <span className="text-gray-600">{customer.phone}</span>
                    </div>
                  )}
                  
                  {/* Display address if it exists in metadata */}
                  {customer.metadata?.shipping_address && (
                    <div className="flex items-start">
                      <MapPin className="w-5 h-5 text-gray-500 mr-2 mt-0.5" />
                      <div className="text-gray-600">
                        <p>{customer.metadata.shipping_address.street}</p>
                        <p>
                          {customer.metadata.shipping_address.city}, {customer.metadata.shipping_address.state} {customer.metadata.shipping_address.postal_code}
                        </p>
                        <p>{customer.metadata.shipping_address.country}</p>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-start">
                    <Calendar className="w-5 h-5 text-gray-500 mr-2 mt-0.5" />
                    <div className="text-gray-600">
                      <p>Registered: {formatDate(customer.created_at)}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Customer Stats</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-md">
                    <div className="text-sm text-gray-500">Orders</div>
                    <div className="text-xl font-semibold">{orders.length}</div>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <div className="text-sm text-gray-500">Total Spent</div>
                    <div className="text-xl font-semibold">€{calculateTotalSpend().toLocaleString('en-US', { minimumFractionDigits: 2 })}</div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Admin Notes */}
            <div className="bg-white shadow-sm rounded-lg p-6 mb-6">
              <h2 className="text-lg font-bold text-gray-800 mb-4">Admin Notes</h2>
              
              <div className="space-y-4">
                <textarea
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  placeholder="Add notes about this customer..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                />
                
                <button
                  onClick={handleSaveNote}
                  disabled={savingNote}
                  className="w-full px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] disabled:opacity-50 transition-colors"
                >
                  {savingNote ? 'Saving...' : 'Save Note'}
                </button>
              </div>
            </div>
          </div>
          
          <div className="md:col-span-2">
            {/* Order History */}
            <div className="bg-white shadow-sm rounded-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Order History</h2>
              
              {orders.length === 0 ? (
                <div className="text-center py-12 border rounded-md">
                  <ShoppingBag className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium text-gray-900">No orders yet</h3>
                  <p className="mt-1 text-gray-500">This customer hasn't placed any orders yet.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Order #
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {orders.map((order) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {order.order_number}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">
                              {formatOrderDate(order.created_at)}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${STATUS_LABELS[order.status as keyof typeof STATUS_LABELS]?.color || 'bg-gray-100 text-gray-800'}`}>
                              {STATUS_LABELS[order.status as keyof typeof STATUS_LABELS]?.label || order.status}
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              €{order.total_amount?.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <Link href={`/admin/orders/${order.id}`} className="text-blue-600 hover:text-blue-900">
                              View
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>  
  );
} 