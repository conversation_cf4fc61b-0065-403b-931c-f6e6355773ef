'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ShoppingBag, Package, Users, CreditCard, TrendingUp, ChevronRight, Eye, DollarSign, Tag } from 'lucide-react';
import { customToast } from '@/components/ui/CustomToast';
import { getProductPerformanceInsights } from '@/lib/product-analytics';
import { getRecentProductViews } from '@/lib/product-analytics';

type DashboardStats = {
  totalProducts: number;
  activeProducts: number;
  totalViews: number;
  totalOrders: number;
  pendingOrders: number;
  totalRevenue: number;
  totalCustomers: number;
  avgOrderValue: number;
};

type OrdersByStatus = {
  status: string;
  count: number;
};

type OrdersByDay = {
  date: string;
  count: number;
  revenue: number;
};

type RecentOrder = {
  id: string;
  order_number: string;
  status: string;
  total_amount: number;
  created_at: string;
  customer_name?: string;
  customer_email?: string;
};

export default function AdminDashboard() {
  const supabase = createClientComponentClient();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    activeProducts: 0,
    totalViews: 0,
    totalOrders: 0,
    pendingOrders: 0,
    totalRevenue: 0,
    totalCustomers: 0,
    avgOrderValue: 0,
  });
  const [ordersByStatus, setOrdersByStatus] = useState<OrdersByStatus[]>([]);
  const [ordersByDay, setOrdersByDay] = useState<OrdersByDay[]>([]);
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [productInsights, setProductInsights] = useState<any[]>([]);
  const [recentViews, setRecentViews] = useState<any[]>([]);
  
  useEffect(() => {
    fetchStats();
    getProductPerformanceInsights().then(setProductInsights);
    getRecentProductViews().then(setRecentViews);
  }, []);
  
  const fetchStats = async () => {
    try {
      setLoading(true);
      console.log('Fetching dashboard stats...');

      // Use the existing authenticated supabase client
      console.log('Running database queries in parallel...');
      
      // Run all database queries in parallel using Promise.all
      const [
        productsResult,
        viewsResult,
        ordersResult,
        customerResult,
        orderStatusResult,
        ordersByDayResult,
        recentOrdersResult
      ] = await Promise.all([
        // Fetch product stats
        supabase.from('products').select('id, status', { count: 'exact' }),
        
        // Fetch view stats
        supabase.from('product_views').select('id', { count: 'exact' }),
        
        // Fetch order stats
        supabase.from('orders').select('id, status, total_amount', { count: 'exact' }),
        
        // Fetch customer stats
        supabase.from('profiles').select('id', { count: 'exact', head: true }).not('id', 'is', null),
        
        // Fetch orders by status
        supabase.from('orders').select('status').not('status', 'is', null),
        
        // Fetch orders for the last 7 days
        supabase.from('orders').select('created_at, total_amount').gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()),
        
        // Fetch recent orders
        supabase
          .from('orders')
          .select(`
            id, 
            order_number, 
            status, 
            total_amount, 
            created_at,
            profiles!user_id (
              full_name,
              first_name,
              last_name,
              email
            )
          `)
          .order('created_at', { ascending: false })
          .limit(5)
      ]);
      
      // Log any errors that occur
      if (productsResult.error) console.error('Error fetching products:', productsResult.error);
      if (viewsResult.error) console.error('Error fetching views:', viewsResult.error);
      if (ordersResult.error) console.error('Error fetching orders:', ordersResult.error);
      if (customerResult.error) console.error('Error fetching customers:', customerResult.error);
      if (orderStatusResult.error) console.error('Error fetching order statuses:', orderStatusResult.error);
      if (ordersByDayResult.error) console.error('Error fetching orders by day:', ordersByDayResult.error);
      if (recentOrdersResult.error) console.error('Error fetching recent orders:', recentOrdersResult.error);
      
      // Check for errors in any of the queries
      if (productsResult.error) throw productsResult.error;
      if (viewsResult.error) throw viewsResult.error;
      if (ordersResult.error) throw ordersResult.error;
      if (customerResult.error) throw customerResult.error;
      if (orderStatusResult.error) throw orderStatusResult.error;
      if (ordersByDayResult.error) throw ordersByDayResult.error;
      if (recentOrdersResult.error) throw recentOrdersResult.error;
      
      console.log('Successfully fetched all data');
      
      // Process products data
      const productsData = productsResult.data || [];
      const totalProducts = productsData.length;
      const activeProducts = productsData.filter(p => p.status === 'active').length;
      
      // Process views data
      const viewsData = viewsResult.data || [];
      const totalViews = viewsData.length;
      
      // Process orders data
      const ordersData = ordersResult.data || [];
      const totalOrders = ordersData.length;
      const pendingOrders = ordersData.filter(o => o.status === 'pending' || o.status === 'processing').length;
      const totalRevenue = ordersData.reduce((sum, order) => sum + (order.total_amount || 0), 0);
      const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
      
      // Process customer data
      const customerCount = customerResult.count || 0;
      
      // Process orders by status
      const orderStatusData = orderStatusResult.data || [];
      const statusCounts: Record<string, number> = {};
      orderStatusData.forEach(order => {
        statusCounts[order.status] = (statusCounts[order.status] || 0) + 1;
      });
      
      const formattedOrdersByStatus = Object.entries(statusCounts).map(([status, count]) => ({
        status,
        count
      }));
      
      // Process orders by day
      const ordersByDayData = ordersByDayResult.data || [];
      const ordersByDayMap: Record<string, { count: number; revenue: number }> = {};
      
      // Initialize the last 7 days
      for (let i = 0; i < 7; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        ordersByDayMap[dateStr] = { count: 0, revenue: 0 };
      }
      
      // Fill in the data
      ordersByDayData.forEach(order => {
        const dateStr = new Date(order.created_at).toISOString().split('T')[0];
        if (ordersByDayMap[dateStr]) {
          ordersByDayMap[dateStr].count += 1;
          ordersByDayMap[dateStr].revenue += order.total_amount || 0;
        }
      });
      
      const formattedOrdersByDay = Object.entries(ordersByDayMap)
        .map(([date, { count, revenue }]) => ({
          date,
          count,
          revenue
        }))
        .sort((a, b) => a.date.localeCompare(b.date));
      
      // Process recent orders
      const recentOrdersData = recentOrdersResult.data || [];
      const formattedRecentOrders = recentOrdersData.map(order => {
        // Handle the profile data correctly based on the structure
        const profile = order.profiles || null;

        // Check if profile is an array and access the first item if it is
        const profileData = Array.isArray(profile) ? profile[0] : profile;
        
        // Get customer name with fallback logic
        let customerName = 'Guest';
        if (profileData?.full_name) {
          customerName = profileData.full_name;
        } else if (profileData?.first_name) {
          customerName = `${profileData.first_name} ${profileData.last_name || ''}`.trim();
        }

        return {
          id: order.id,
          order_number: order.order_number,
          status: order.status,
          total_amount: order.total_amount,
          created_at: order.created_at,
          customer_name: customerName,
          customer_email: profileData?.email || '<EMAIL>',
        };
      });
      
      // Update state with all processed data
      setStats({
        totalProducts,
        activeProducts,
        totalViews,
        totalOrders,
        pendingOrders,
        totalRevenue,
        totalCustomers: customerCount,
        avgOrderValue
      });
      
      setOrdersByStatus(formattedOrdersByStatus);
      setOrdersByDay(formattedOrdersByDay);
      setRecentOrders(formattedRecentOrders);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      customToast.error('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };
  
  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      router.push('/auth/admin/login');
    } catch (error) {
      console.error('Error logging out:', error);
      customToast.error('Error logging out');
    }
  };
  
  const formatCurrency = (amount: number) => {
    return `€${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Status color mapping
  const getStatusColor = (status: string) => {
    const statusColors: { [key: string]: string } = {
      pending: 'bg-yellow-100 text-yellow-800',
      paid: 'bg-green-100 text-green-800',
      processing: 'bg-blue-100 text-blue-800',
      shipped: 'bg-purple-100 text-purple-800',
      delivered: 'bg-emerald-100 text-emerald-800',
      canceled: 'bg-red-100 text-red-800',
      refunded: 'bg-gray-100 text-gray-800',
      failed: 'bg-red-100 text-red-800',
    };
    
    return statusColors[status] || 'bg-gray-100 text-gray-800';
  };
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Dashboard</h1>
      
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
        </div>
      ) : (
        <>
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="bg-white shadow-sm rounded-lg p-6">
              <div className="flex items-center">
                <div className="bg-[#F7F9FC] p-3 rounded-full mr-4">
                  <ShoppingBag className="h-6 w-6 text-[#171717]" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Orders</p>
                  <h3 className="text-2xl font-bold">{stats.totalOrders}</h3>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Pending: <span className="font-semibold text-gray-700">{stats.pendingOrders}</span>
                </p>
              </div>
            </div>
            
            <div className="bg-white shadow-sm rounded-lg p-6">
              <div className="flex items-center">
                <div className="bg-[#F7F9FC] p-3 rounded-full mr-4">
                  <DollarSign className="h-6 w-6 text-[#171717]" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Revenue</p>
                  <h3 className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</h3>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Avg. Order: <span className="font-semibold text-gray-700">{formatCurrency(stats.avgOrderValue)}</span>
                </p>
              </div>
            </div>
            
            <div className="bg-white shadow-sm rounded-lg p-6">
              <div className="flex items-center">
                <div className="bg-[#F7F9FC] p-3 rounded-full mr-4">
                  <Package className="h-6 w-6 text-[#171717]" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Products</p>
                  <h3 className="text-2xl font-bold">{stats.totalProducts}</h3>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Active: <span className="font-semibold text-gray-700">{stats.activeProducts}</span>
                </p>
              </div>
            </div>
            
            <div className="bg-white shadow-sm rounded-lg p-6">
              <div className="flex items-center">
                <div className="bg-[#F7F9FC] p-3 rounded-full mr-4">
                  <Users className="h-6 w-6 text-[#171717]" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Customers</p>
                  <h3 className="text-2xl font-bold">{stats.totalCustomers}</h3>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Views: <span className="font-semibold text-gray-700">{stats.totalViews}</span>
                </p>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            {/* Recent Orders */}
            <div className="lg:col-span-2 bg-white shadow-sm rounded-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-bold text-gray-800">Recent Orders</h2>
                <Link href="/admin/orders" className="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                  View All <ChevronRight className="h-4 w-4 ml-1" />
                </Link>
              </div>
              
              {recentOrders.length === 0 ? (
                <div className="text-center py-8 border rounded-md">
                  <ShoppingBag className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium text-gray-900">No orders yet</h3>
                  <p className="mt-1 text-gray-500">Your orders will appear here when customers make purchases.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Order
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {recentOrders.map((order) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap">
                            <Link href={`/admin/orders/${order.id}`} className="text-blue-600 hover:text-blue-900">
                              #{order.order_number}
                            </Link>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(order.created_at)}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status)}`}>
                              {order.status}
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            {order.customer_name}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-right font-medium">
                            {formatCurrency(order.total_amount)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
            
            {/* Order Distribution */}
            <div className="bg-white shadow-sm rounded-lg p-6">
              <h2 className="text-lg font-bold text-gray-800 mb-6">Order Status Distribution</h2>
              
              {ordersByStatus.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No order data available</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {ordersByStatus.map(({ status, count }) => (
                    <div key={status} className="flex items-center">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(status).split(' ')[0]} mr-2`}></div>
                      <div className="flex-1">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm font-medium text-gray-700">
                            {status.charAt(0).toUpperCase() + status.slice(1)}
                          </span>
                          <span className="text-sm font-medium text-gray-700">{count}</span>
                        </div>
                        <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
                          <div 
                            className={`h-full ${getStatusColor(status).split(' ')[0]}`} 
                            style={{ width: `${(count / stats.totalOrders) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              <div className="mt-8">
                <h2 className="text-lg font-bold text-gray-800 mb-6">Quick Actions</h2>
                <div className="space-y-2">
                  <Link
                    href="/admin/orders/add"
                    className="flex items-center justify-between px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <div className="flex items-center">
                      <ShoppingBag className="h-5 w-5 text-gray-500 mr-3" />
                      <span className="text-sm font-medium text-gray-700">Create Order</span>
                    </div>
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  </Link>
                  <Link
                    href="/admin/products/add"
                    className="flex items-center justify-between px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <div className="flex items-center">
                      <Tag className="h-5 w-5 text-gray-500 mr-3" />
                      <span className="text-sm font-medium text-gray-700">Add Product</span>
                    </div>
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  </Link>
                  <Link
                    href="/admin/settings"
                    className="flex items-center justify-between px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <div className="flex items-center">
                      <TrendingUp className="h-5 w-5 text-gray-500 mr-3" />
                      <span className="text-sm font-medium text-gray-700">View Reports</span>
                    </div>
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
          
          {/* Sales Trend */}
          <div className="bg-white shadow-sm rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-bold text-gray-800">Sales Trend (Last 7 Days)</h2>
            </div>
            
            {ordersByDay.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No sales data available</p>
              </div>
            ) : (
              <div className="grid grid-cols-7 gap-2 h-64">
                {ordersByDay.map(({ date, count, revenue }) => {
                  const maxRevenue = Math.max(...ordersByDay.map(d => d.revenue));
                  const percentage = maxRevenue > 0 ? (revenue / maxRevenue) * 100 : 0;
                  const height = percentage > 0 ? `${Math.max(10, percentage)}%` : '5%';
                  
                  return (
                    <div key={date} className="flex flex-col items-center justify-end">
                      <div className="w-full flex justify-center items-end h-[80%]">
                        <div 
                          className="w-full max-w-[30px] bg-[#171717] rounded-t-sm" 
                          style={{ height }}
                          title={`${count} orders - ${formatCurrency(revenue)}`}
                        ></div>
                      </div>
                      <div className="w-full text-center mt-2">
                        <p className="text-xs text-gray-500">{new Date(date).toLocaleDateString('en-US', { day: 'numeric', month: 'short' })}</p>
                        <p className="text-xs font-medium">{count}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
          {/* Product Insights */}
          <div className="bg-white shadow-sm rounded-lg p-6 mt-6">
            <h2 className="text-lg font-bold text-gray-800 mb-4">Product Performance Insights</h2>
            {productInsights.length === 0 ? (
              <p className="text-gray-500">No insights available.</p>
            ) : (
              <div className="space-y-3">
                {productInsights.map((insight) => (
                  <div key={insight.id} className="p-4 border rounded-md">
                    <p className="font-medium text-gray-900">{insight.name}</p>
                    <p className="text-sm text-gray-600">
                      Views: {insight.views} | Orders: {insight.orders} | Conversion: {insight.conversion_rate}%
                    </p>
                    {insight.insight && (
                      <p className="text-sm mt-1 text-blue-700">{insight.insight}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Recent Product Viewers */}
          <div className="bg-white shadow-sm rounded-lg p-6 mt-6">
            <h2 className="text-lg font-bold text-gray-800 mb-4">Recent Product Viewers</h2>
            {recentViews.length === 0 ? (
              <p className="text-gray-500">No recent views recorded.</p>
            ) : (
              <div className="space-y-3">
                {recentViews.map((view) => (
                  <div key={view.id} className="p-4 border rounded-md">
                    <div>
                      <p className="font-medium text-gray-900 mb-2">Product Preview</p>
                      {/* Temporarily disabled model-viewer to fix React Server Components error */}
                      <div className="w-full max-w-[300px] h-[300px] bg-gray-100 rounded-md flex items-center justify-center">
                        <p className="text-gray-500">3D Preview Disabled</p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">
                      Viewed at: {new Date(view.viewed_at).toLocaleString()}
                    </p>
                    {view.user ? (
                      <p className="text-sm text-blue-700">
                        Viewer: {view.user.first_name || 'Unknown'} {view.user.last_name || ''} ({view.user.email})
                      </p>
                    ) : (
                      <p className="text-sm text-gray-500">Viewer: Guest</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}