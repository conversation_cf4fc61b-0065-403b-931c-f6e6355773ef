'use client'

import { useState, useEffect } from 'react'
import { PlusIcon, PencilIcon, TrashIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'

interface DiscountCode {
  id: string
  code: string
  description: string
  discount_type: 'percentage' | 'fixed_amount'
  discount_value: number
  minimum_order_amount: number
  maximum_discount_amount?: number
  usage_limit?: number
  used_count: number
  valid_from: string
  valid_until?: string
  is_active: boolean
  single_use_per_customer: boolean
  created_at: string
}

interface DiscountAnalytics extends DiscountCode {
  total_usage_records: number
  total_discount_given: number
  usage_percentage: number
  remaining_uses: number
}

export default function DiscountCodesPage() {
  const [discountCodes, setDiscountCodes] = useState<DiscountAnalytics[]>([])
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingCode, setEditingCode] = useState<DiscountCode | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Form state
  const [formData, setFormData] = useState({
    code: '',
    description: '',
    discount_type: 'percentage' as 'percentage' | 'fixed_amount',
    discount_value: 0,
    minimum_order_amount: 0,
    maximum_discount_amount: '',
    usage_limit: '',
    valid_from: new Date().toISOString().split('T')[0],
    valid_until: '',
    is_active: true,
    single_use_per_customer: false
  })

  useEffect(() => {
    fetchDiscountCodes()
  }, [])

  const fetchDiscountCodes = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/discount-codes?analytics=true')
      if (!response.ok) throw new Error('Failed to fetch discount codes')
      const data = await response.json()
      setDiscountCodes(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load discount codes')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const method = editingCode ? 'PUT' : 'POST'
      const body = editingCode 
        ? { id: editingCode.id, ...formData }
        : formData

      const response = await fetch('/api/discount-codes', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save discount code')
      }

      resetForm()
      setIsCreateModalOpen(false)
      setIsEditModalOpen(false)
      setEditingCode(null)
      fetchDiscountCodes()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save discount code')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this discount code?')) return
    
    try {
      const response = await fetch(`/api/discount-codes?id=${id}`, {
        method: 'DELETE'
      })
      if (!response.ok) throw new Error('Failed to delete discount code')
      fetchDiscountCodes()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete discount code')
    }
  }

  const toggleActive = async (code: DiscountCode) => {
    try {
      const response = await fetch('/api/discount-codes', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: code.id,
          is_active: !code.is_active
        })
      })
      if (!response.ok) throw new Error('Failed to update discount code')
      fetchDiscountCodes()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update discount code')
    }
  }

  const resetForm = () => {
    setFormData({
      code: '',
      description: '',
      discount_type: 'percentage',
      discount_value: 0,
      minimum_order_amount: 0,
      maximum_discount_amount: '',
      usage_limit: '',
      valid_from: new Date().toISOString().split('T')[0],
      valid_until: '',
      is_active: true,
      single_use_per_customer: false
    })
  }

  const openEditModal = (code: DiscountCode) => {
    setEditingCode(code)
    setFormData({
      code: code.code,
      description: code.description,
      discount_type: code.discount_type,
      discount_value: code.discount_value,
      minimum_order_amount: code.minimum_order_amount,
      maximum_discount_amount: code.maximum_discount_amount?.toString() || '',
      usage_limit: code.usage_limit?.toString() || '',
      valid_from: code.valid_from.split('T')[0],
      valid_until: code.valid_until?.split('T')[0] || '',
      is_active: code.is_active,
      single_use_per_customer: code.single_use_per_customer
    })
    setIsEditModalOpen(true)
  }

  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined || isNaN(amount)) return '€0.00'
    return `€${Number(amount).toFixed(2)}`
  }
  const formatPercentage = (value: number | null | undefined) => {
    if (value === null || value === undefined || isNaN(value)) return '0%'
    return `${Number(value)}%`
  }

  if (loading) return <div className="p-8">Loading discount codes...</div>
  if (error) return <div className="p-8 text-red-600">Error: {error}</div>

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Discount Codes</h1>
          <p className="text-gray-600 mt-2">Create and manage promotional codes for your store</p>
        </div>
        <button
          onClick={() => {
            resetForm()
            setIsCreateModalOpen(true)
          }}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center gap-2"
        >
          <PlusIcon className="w-5 h-5" />
          Create Discount Code
        </button>
      </div>

      {/* Discount Codes Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Code</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Discount</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Usage</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Valid Until</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {discountCodes.map((code) => (
              <tr key={code.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <div>
                    <div className="font-medium text-gray-900">{code.code}</div>
                    <div className="text-sm text-gray-500">{code.description}</div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm">
                    {code.discount_type === 'percentage' 
                      ? formatPercentage(code.discount_value)
                      : formatCurrency(code.discount_value)
                    }
                    {code.minimum_order_amount && code.minimum_order_amount > 0 && (
                      <div className="text-xs text-gray-500">
                        Min: {formatCurrency(code.minimum_order_amount)}
                      </div>
                    )}
                    {code.maximum_discount_amount && (
                      <div className="text-xs text-gray-500">
                        Max: {formatCurrency(code.maximum_discount_amount)}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm">
                    <div>{code.used_count}{code.usage_limit ? ` / ${code.usage_limit}` : ''}</div>
                    {code.usage_limit && (
                      <div className="text-xs text-gray-500">
                        {code.usage_percentage ? code.usage_percentage.toFixed(1) : '0.0'}% used
                      </div>
                    )}
                    <div className="text-xs text-gray-500">
                      {formatCurrency(code.total_discount_given || 0)} saved
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {code.valid_until 
                    ? new Date(code.valid_until).toLocaleDateString()
                    : 'No expiry'
                  }
                </td>
                <td className="px-6 py-4">
                  <button
                    onClick={() => toggleActive(code)}
                    className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${
                      code.is_active 
                        ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                  >
                    {code.is_active ? (
                      <>
                        <EyeIcon className="w-4 h-4" />
                        Active
                      </>
                    ) : (
                      <>
                        <EyeSlashIcon className="w-4 h-4" />
                        Inactive
                      </>
                    )}
                  </button>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => openEditModal(code)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <PencilIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handleDelete(code.id)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <TrashIcon className="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {discountCodes.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No discount codes yet. Create your first one!</p>
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      {(isCreateModalOpen || isEditModalOpen) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h2 className="text-2xl font-bold mb-6">
                {editingCode ? 'Edit Discount Code' : 'Create New Discount Code'}
              </h2>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Discount Code *
                    </label>
                    <input
                      type="text"
                      value={formData.code}
                      onChange={(e) => setFormData({...formData, code: e.target.value.toUpperCase()})}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., TRM-SUMMER25"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">Make it unique and memorable!</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Discount Type *
                    </label>
                    <select
                      value={formData.discount_type}
                      onChange={(e) => setFormData({...formData, discount_type: e.target.value as 'percentage' | 'fixed_amount'})}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="percentage">Percentage Off</option>
                      <option value="fixed_amount">Fixed Amount Off</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Discount Value *
                    </label>
                    <input
                      type="number"
                      value={formData.discount_value}
                      onChange={(e) => setFormData({...formData, discount_value: parseFloat(e.target.value) || 0})}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder={formData.discount_type === 'percentage' ? '15' : '25'}
                      min="0"
                      step="0.01"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {formData.discount_type === 'percentage' ? 'Percentage (0-100)' : 'Amount in EUR'}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Order Amount
                    </label>
                    <input
                      type="number"
                      value={formData.minimum_order_amount}
                      onChange={(e) => setFormData({...formData, minimum_order_amount: parseFloat(e.target.value) || 0})}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="0"
                      min="0"
                      step="0.01"
                    />
                    <p className="text-xs text-gray-500 mt-1">Minimum order to use this code</p>
                  </div>

                  {formData.discount_type === 'percentage' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Maximum Discount Amount
                      </label>
                      <input
                        type="number"
                        value={formData.maximum_discount_amount}
                        onChange={(e) => setFormData({...formData, maximum_discount_amount: e.target.value})}
                        className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="15"
                        min="0"
                        step="0.01"
                      />
                      <p className="text-xs text-gray-500 mt-1">Cap the discount amount (EUR)</p>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Usage Limit
                    </label>
                    <input
                      type="number"
                      value={formData.usage_limit}
                      onChange={(e) => setFormData({...formData, usage_limit: e.target.value})}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="Unlimited"
                      min="1"
                    />
                    <p className="text-xs text-gray-500 mt-1">Total number of uses allowed</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Valid From
                    </label>
                    <input
                      type="date"
                      value={formData.valid_from}
                      onChange={(e) => setFormData({...formData, valid_from: e.target.value})}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Valid Until
                    </label>
                    <input
                      type="date"
                      value={formData.valid_until}
                      onChange={(e) => setFormData({...formData, valid_until: e.target.value})}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">Leave empty for no expiry</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="Describe this discount code..."
                  />
                </div>

                <div className="flex items-center gap-6">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.is_active}
                      onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                      className="rounded"
                    />
                    <span className="text-sm font-medium text-gray-700">Active</span>
                  </label>

                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.single_use_per_customer}
                      onChange={(e) => setFormData({...formData, single_use_per_customer: e.target.checked})}
                      className="rounded"
                    />
                    <span className="text-sm font-medium text-gray-700">Single use per customer</span>
                  </label>
                </div>

                <div className="flex justify-end gap-4 pt-6">
                  <button
                    type="button"
                    onClick={() => {
                      setIsCreateModalOpen(false)
                      setIsEditModalOpen(false)
                      setEditingCode(null)
                      resetForm()
                    }}
                    className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    {editingCode ? 'Update Code' : 'Create Code'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}