'use client';

import { useEffect } from 'react';
import Link from 'next/link';

export default function AdminError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Admin Error:', error);
  }, [error]);

  return (
    <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-sm text-center">
        <h2 className="text-2xl font-heading text-[#171717] mb-4">Admin Access Error</h2>
        <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6 text-sm">
          <p className="mb-2 font-medium">Error: {error.message}</p>
          <p>This could be due to insufficient permissions or session issues.</p>
        </div>
        
        <div className="space-y-4">
          <button
            onClick={reset}
            className="px-4 py-2 bg-[#171717] text-white text-sm rounded-lg hover:bg-[#333333] transition-colors w-full"
          >
            Try Again
          </button>
          
          <Link 
            href="/admin/login" 
            className="block px-4 py-2 border border-[#171717] text-[#171717] text-sm rounded-lg hover:bg-[rgba(0,0,0,0.05)] transition-colors"
          >
            Go to Admin Login
          </Link>
          
          <Link 
            href="/admin/check-admin" 
            className="block px-4 py-2 border border-red-600 text-red-600 text-sm rounded-lg hover:bg-red-50 transition-colors"
          >
            Check Admin Status
          </Link>
          
          <Link 
            href="/force-logout" 
            className="block text-sm text-red-600 hover:underline mt-4"
          >
            Force Logout
          </Link>
        </div>
      </div>
    </div>
  );
}
