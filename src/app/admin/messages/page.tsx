'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';

interface ContactMessage {
  id: string;
  created_at: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'unread' | 'read' | 'responded';
  responded_at: string | null;
  responded_by: string | null;
}

const statusLabels = {
  unread: { text: 'Unread', class: 'bg-red-100 text-red-800' },
  read: { text: 'Read', class: 'bg-blue-100 text-blue-800' },
  responded: { text: 'Responded', class: 'bg-green-100 text-green-800' },
};

export default function AdminMessages() {
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'unread' | 'read' | 'responded'>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const messagesPerPage = 10;

  const supabase = createClientComponentClient();
  const router = useRouter();

  const fetchMessages = async () => {
    try {
      setLoading(true);

      // First check if user is authenticated and is admin
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError) throw authError;

      if (!user) {
        router.push('/auth/login');
        return;
      }

      // Check if user is admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single();

      if (profileError) throw profileError;

      if (!profile?.is_admin) {
        toast.error('Unauthorized access');
        router.push('/');
        return;
      }

      // Now fetch messages
      let query = supabase
        .from('contact_messages')
        .select('*', { count: 'exact' });

      // Apply filters
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }
      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,subject.ilike.%${searchTerm}%,message.ilike.%${searchTerm}%`);
      }

      // Add pagination
      const start = (page - 1) * messagesPerPage;
      const end = start + messagesPerPage - 1;
      query = query.range(start, end).order('created_at', { ascending: false });

      const { data, count, error } = await query;

      if (error) throw error;

      setMessages(data || []);
      setTotalPages(Math.ceil((count || 0) / messagesPerPage));
    } catch (error: any) {
      console.error('Error fetching messages:', error);
      toast.error(error.message || 'Failed to load messages');
      if (error.message?.includes('JWT')) {
        router.push('/auth/login');
      }
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (messageId: string) => {
    try {
      // Check if user is authenticated and is admin
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError) throw authError;

      if (!user) {
        router.push('/auth/login');
        return;
      }

      // Check if user is admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single();

      if (profileError) throw profileError;

      if (!profile?.is_admin) {
        toast.error('Unauthorized access');
        router.push('/');
        return;
      }

      const { error } = await supabase
        .from('contact_messages')
        .update({ status: 'read' })
        .eq('id', messageId);

      if (error) throw error;

      toast.success('Message marked as read');
      fetchMessages();
    } catch (error: any) {
      console.error('Error marking message as read:', error);
      toast.error(error.message || 'Failed to update message status');
    }
  };

  const markAsResponded = async (messageId: string) => {
    try {
      // Check if user is authenticated and is admin
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError) throw authError;

      if (!user) {
        router.push('/auth/login');
        return;
      }

      // Check if user is admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single();

      if (profileError) throw profileError;

      if (!profile?.is_admin) {
        toast.error('Unauthorized access');
        router.push('/');
        return;
      }

      const { error } = await supabase
        .from('contact_messages')
        .update({
          status: 'responded',
          responded_at: new Date().toISOString(),
          responded_by: user.id
        })
        .eq('id', messageId);

      if (error) throw error;

      toast.success('Message marked as responded');
      fetchMessages();
    } catch (error: any) {
      console.error('Error marking message as responded:', error);
      toast.error(error.message || 'Failed to update message status');
    }
  };

  useEffect(() => {
    fetchMessages();
  }, [page, statusFilter, searchTerm]);

  const formatDate = (date: string) => {
    return format(new Date(date), 'MMM d, yyyy h:mm a');
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h1 className="text-2xl font-semibold mb-6">Contact Messages</h1>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <input
          type="text"
          placeholder="Search messages..."
          className="flex-1 p-2 border rounded-lg"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <select
          className="p-2 border rounded-lg"
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as any)}
        >
          <option value="all">All Status</option>
          <option value="unread">Unread</option>
          <option value="read">Read</option>
          <option value="responded">Responded</option>
        </select>
      </div>

      {/* Messages Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-4 text-left">Date</th>
              <th className="p-4 text-left">Name</th>
              <th className="p-4 text-left">Email</th>
              <th className="p-4 text-left">Subject</th>
              <th className="p-4 text-left">Status</th>
              <th className="p-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={6} className="text-center py-4">Loading...</td>
              </tr>
            ) : messages.length === 0 ? (
              <tr>
                <td colSpan={6} className="text-center py-4">No messages found</td>
              </tr>
            ) : (
              messages.map((message) => (
                <tr key={message.id} className="border-t hover:bg-gray-50">
                  <td className="p-4">{formatDate(message.created_at)}</td>
                  <td className="p-4">{message.name}</td>
                  <td className="p-4">{message.email}</td>
                  <td className="p-4">{message.subject}</td>
                  <td className="p-4">
                    <span className={`px-2 py-1 rounded-full text-sm ${statusLabels[message.status].class}`}>
                      {statusLabels[message.status].text}
                    </span>
                  </td>
                  <td className="p-4">
                    <div className="flex gap-2">
                      <button
                        onClick={() => markAsRead(message.id)}
                        disabled={message.status !== 'unread'}
                        className={`px-3 py-1 rounded-lg text-sm ${
                          message.status === 'unread'
                            ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        Mark Read
                      </button>
                      <button
                        onClick={() => markAsResponded(message.id)}
                        disabled={message.status === 'responded'}
                        className={`px-3 py-1 rounded-lg text-sm ${
                          message.status !== 'responded'
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        Mark Responded
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2 mt-6">
          <button
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
            className="px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 disabled:opacity-50"
          >
            Previous
          </button>
          <span className="px-4 py-2">
            Page {page} of {totalPages}
          </span>
          <button
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
            className="px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}
