'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Bell, Mail, MessageSquare, Phone, Settings, TestTube, Check, X } from 'lucide-react';

interface NotificationPreference {
  id: string;
  notification_type: string;
  in_app_enabled: boolean;
  email_enabled: boolean;
  push_enabled: boolean; // Used for SMS and WhatsApp
}

interface AdminProfile {
  id: string;
  email: string;
  phone_number: string | null;
  first_name: string | null;
  last_name: string | null;
}

const notificationTypes = [
  { key: 'new_order', label: 'New Orders', description: 'When a new order is placed' },
  { key: 'order_status_change', label: 'Order Status Changes', description: 'When order status is updated' },
  { key: 'new_message', label: 'New Messages', description: 'When customers send contact messages' },
  { key: 'new_bag_request', label: 'Bag Requests', description: 'When customers submit bag requests' },
  { key: 'low_inventory', label: 'Low Inventory', description: 'When product stock is running low' },
  { key: 'payment_received', label: 'Payment Received', description: 'When payments are processed' },
  { key: 'refund_requested', label: 'Refund Requests', description: 'When refunds are requested' },
  { key: 'system_alert', label: 'System Alerts', description: 'Important system notifications' },
];

export default function NotificationSettingsPage() {
  const [preferences, setPreferences] = useState<NotificationPreference[]>([]);
  const [profile, setProfile] = useState<AdminProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isTestingSMS, setIsTestingSMS] = useState(false);
  const [isTestingWhatsApp, setIsTestingWhatsApp] = useState(false);
  const [isTestingEmail, setIsTestingEmail] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const supabase = createClientComponentClient();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Get current user profile
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load profile
      const { data: profileData } = await supabase
        .from('profiles')
        .select('id, email, phone_number, first_name, last_name')
        .eq('id', user.id)
        .single();

      if (profileData) {
        setProfile(profileData);
      }

      // Load notification preferences
      const { data: prefsData } = await supabase
        .from('admin_notification_preferences')
        .select('*')
        .eq('user_id', user.id);

      if (prefsData) {
        setPreferences(prefsData);
      } else {
        // Create default preferences
        await createDefaultPreferences(user.id);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      setMessage({ type: 'error', text: 'Failed to load notification settings' });
    } finally {
      setIsLoading(false);
    }
  };

  const createDefaultPreferences = async (userId: string) => {
    const defaultPrefs = notificationTypes.map(type => ({
      user_id: userId,
      notification_type: type.key,
      in_app_enabled: true,
      email_enabled: true,
      push_enabled: false, // SMS/WhatsApp disabled by default
    }));

    const { data, error } = await supabase
      .from('admin_notification_preferences')
      .insert(defaultPrefs)
      .select();

    if (!error && data) {
      setPreferences(data);
    }
  };

  const updatePreference = async (notificationType: string, field: string, value: boolean) => {
    try {
      const { error } = await supabase
        .from('admin_notification_preferences')
        .update({ [field]: value })
        .eq('user_id', profile?.id)
        .eq('notification_type', notificationType);

      if (error) throw error;

      setPreferences(prev =>
        prev.map(pref =>
          pref.notification_type === notificationType
            ? { ...pref, [field]: value }
            : pref
        )
      );
    } catch (error) {
      console.error('Error updating preference:', error);
      setMessage({ type: 'error', text: 'Failed to update preference' });
    }
  };

  const updatePhoneNumber = async (phoneNumber: string) => {
    try {
      setIsSaving(true);
      
      const { error } = await supabase
        .from('profiles')
        .update({ phone_number: phoneNumber })
        .eq('id', profile?.id);

      if (error) throw error;

      setProfile(prev => prev ? { ...prev, phone_number: phoneNumber } : null);
      setMessage({ type: 'success', text: 'Phone number updated successfully' });
    } catch (error) {
      console.error('Error updating phone number:', error);
      setMessage({ type: 'error', text: 'Failed to update phone number' });
    } finally {
      setIsSaving(false);
    }
  };

  const sendTestNotification = async (type: 'email' | 'sms' | 'whatsapp') => {
    try {
      if (type === 'email') setIsTestingEmail(true);
      if (type === 'sms') setIsTestingSMS(true);
      if (type === 'whatsapp') setIsTestingWhatsApp(true);

      // Get the current session for authentication
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const endpoint = type === 'email' ? '/api/notifications/send-email' :
                     type === 'sms' ? '/api/notifications/send-sms' :
                     '/api/notifications/send-whatsapp';

      const body = type === 'whatsapp' ? {
        type: 'test',
        phoneNumber: profile?.phone_number
      } : {
        type: 'system_alert',
        title: `Test ${type.toUpperCase()} Notification`,
        message: `This is a test ${type} notification from your Treasures of Maimi admin panel. If you received this, ${type} notifications are working correctly!`,
        priority: 'low'
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        setMessage({ type: 'success', text: `Test ${type} notification sent successfully!` });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to send test ${type} notification`);
      }
    } catch (error) {
      console.error(`Error sending test ${type} notification:`, error);
      setMessage({ type: 'error', text: `Failed to send test ${type} notification: ${error instanceof Error ? error.message : 'Unknown error'}` });
    } finally {
      if (type === 'email') setIsTestingEmail(false);
      if (type === 'sms') setIsTestingSMS(false);
      if (type === 'whatsapp') setIsTestingWhatsApp(false);
    }
  };

  const getPreference = (notificationType: string) => {
    return preferences.find(p => p.notification_type === notificationType);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3">
            <Settings className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Notification Settings</h1>
              <p className="text-gray-600">Manage how you receive notifications</p>
            </div>
          </div>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
          }`}>
            <div className="flex items-center">
              {message.type === 'success' ? (
                <Check className="h-5 w-5 mr-2" />
              ) : (
                <X className="h-5 w-5 mr-2" />
              )}
              {message.text}
            </div>
          </div>
        )}

        {/* Phone Number Settings */}
        <div className="bg-white shadow rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Contact Information</h2>
            <p className="text-sm text-gray-600">Required for SMS and WhatsApp notifications</p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={profile?.email || ''}
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <div className="flex space-x-2">
                  <input
                    type="tel"
                    value={profile?.phone_number || ''}
                    onChange={(e) => setProfile(prev => prev ? { ...prev, phone_number: e.target.value } : null)}
                    placeholder="+1234567890"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    onClick={() => updatePhoneNumber(profile?.phone_number || '')}
                    disabled={isSaving}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isSaving ? 'Saving...' : 'Save'}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Include country code (e.g., +1 for US, +44 for UK)
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Test Notifications */}
        <div className="bg-white shadow rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Test Notifications</h2>
            <p className="text-sm text-gray-600">Send test notifications to verify your settings</p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => sendTestNotification('email')}
                disabled={isTestingEmail}
                className="flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              >
                <Mail className="h-5 w-5" />
                <span>{isTestingEmail ? 'Sending...' : 'Test Email'}</span>
              </button>
              <button
                onClick={() => sendTestNotification('sms')}
                disabled={isTestingSMS || !profile?.phone_number}
                className="flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              >
                <Phone className="h-5 w-5" />
                <span>{isTestingSMS ? 'Sending...' : 'Test SMS'}</span>
              </button>
              <button
                onClick={() => sendTestNotification('whatsapp')}
                disabled={isTestingWhatsApp || !profile?.phone_number}
                className="flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              >
                <MessageSquare className="h-5 w-5" />
                <span>{isTestingWhatsApp ? 'Sending...' : 'Test WhatsApp'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Notification Preferences */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Notification Preferences</h2>
            <p className="text-sm text-gray-600">Choose how you want to receive different types of notifications</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Notification Type
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Bell className="h-4 w-4 mx-auto" />
                    <span className="block mt-1">In-App</span>
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Mail className="h-4 w-4 mx-auto" />
                    <span className="block mt-1">Email</span>
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Phone className="h-4 w-4 mx-auto" />
                    <span className="block mt-1">SMS</span>
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <MessageSquare className="h-4 w-4 mx-auto" />
                    <span className="block mt-1">WhatsApp</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {notificationTypes.map((type) => {
                  const pref = getPreference(type.key);
                  return (
                    <tr key={type.key}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{type.label}</div>
                          <div className="text-sm text-gray-500">{type.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={pref?.in_app_enabled || false}
                          onChange={(e) => updatePreference(type.key, 'in_app_enabled', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={pref?.email_enabled || false}
                          onChange={(e) => updatePreference(type.key, 'email_enabled', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={pref?.push_enabled || false}
                          onChange={(e) => updatePreference(type.key, 'push_enabled', e.target.checked)}
                          disabled={!profile?.phone_number}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={pref?.push_enabled || false}
                          onChange={(e) => updatePreference(type.key, 'push_enabled', e.target.checked)}
                          disabled={!profile?.phone_number}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                        />
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
