'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { customToast } from '@/components/ui/CustomToast';
import { generateTrackingUrl } from '@/lib/order-notifications';

// Define Order interface
interface OrderItem {
  id: string;
  product_id: string;
  quantity: number;
  price: number;
  name: string;
  image_url: string;
  product?: {
    media: any;
    name?: string;
    main_image_url?: string;
    slug?: string;
    category?: {
      name: string;
    }
  };
}

interface CustomerProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
}

interface Order {
  id: string;
  user_id: string;
  status: string;
  total: number;
  created_at: string;
  updated_at: string;
  shipping_address_id?: string | null;
  shipping_address?: {
    id: string;
    name: string;
    street: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
  };
  billing_address: any;
  tracking_number?: string;
  carrier?: string;
  tracking_url?: string;
  tracking_email_sent?: boolean;
  tracking_email_sent_at?: string;
  notes?: string;
  payment_intent?: string;
  payment_status?: string;
  profiles?: CustomerProfile;
  items?: OrderItem[];
  order_number?: string;
  subtotal?: number;
  shipping_amount?: number;
  tax_amount?: number;
  discount_amount?: number;
  total_amount?: number;
  payment_method?: string;
}

// Order status labels and colors
const STATUS_LABELS = {
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  paid: { label: 'Paid', color: 'bg-green-100 text-green-800' },
  processing: { label: 'Processing', color: 'bg-blue-100 text-blue-800' },
  shipped: { label: 'Shipped', color: 'bg-purple-100 text-purple-800' },
  delivered: { label: 'Delivered', color: 'bg-emerald-100 text-emerald-800' },
  canceled: { label: 'Canceled', color: 'bg-red-100 text-red-800' },
  refunded: { label: 'Refunded', color: 'bg-gray-100 text-gray-800' },
  failed: { label: 'Failed', color: 'bg-red-100 text-red-800' },
};

export default function OrderDetails() {
  const supabase = createClientComponentClient();
  const router = useRouter();
  const { id } = useParams();
  const orderId = Array.isArray(id) ? id[0] : id;
  
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [trackingNumber, setTrackingNumber] = useState('');
  const [carrier, setCarrier] = useState('');
  const [trackingUrl, setTrackingUrl] = useState('');
  const [status, setStatus] = useState('');
  const [notes, setNotes] = useState('');
  const [saving, setSaving] = useState(false);
  
  // Fetch order details
  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        setLoading(true);
        console.log('Fetching order details for ID:', orderId);
        
        // First check if the order exists
        const { data: orderExists, error: checkError } = await supabase
          .from('orders')
          .select('id')
          .eq('id', orderId)
          .single();
          
        if (checkError) {
          console.error('Error checking if order exists:', checkError);
          customToast.error(`Order not found: ${checkError.message}`);
          return;
        }
        
        // Now fetch the full order with all related data
        const { data, error } = await supabase
          .from('orders')
        .select(`
            *,
            profiles:user_id(id, email, first_name, last_name, phone),
            shipping_address:shipping_addresses(id, name, street, city, state, country, postal_code),
            items:order_items(
              id,
              product_id,
              quantity,
              price,
              product:products(
                id,
                name,
                slug,
                media:product_media(
                  url,
                  is_main
                ),
                category:categories(name)
              )
            )
          `)
          .eq('id', orderId)
          .single();
          
        if (error) {
          console.error('Error fetching order details:', error);
          customToast.error(`Failed to load order details: ${error.message}`);
          return;
        }
        
        console.log('Order data retrieved:', data);
        
        // Handle profiles data which might be an array
        if (data.profiles && Array.isArray(data.profiles)) {
          data.profiles = data.profiles[0];
        }
        
        setOrder(data);
        setStatus(data.status || 'pending');
        setTrackingNumber(data.tracking_number || '');
        setCarrier(data.carrier || '');
        setTrackingUrl(data.tracking_url || '');
        
        try {
          if (data.notes) {
            const parsedNotes = JSON.parse(data.notes);
            setNotes(parsedNotes.admin_notes || '');
          }
        } catch (e) {
          // If notes is not valid JSON, just use it as is
          setNotes(data.notes || '');
        }
      } catch (error) {
        console.error('Error fetching order details:', error);
        customToast.error('Failed to load order details');
      } finally {
        setLoading(false);
      }
    };
    
    fetchOrderDetails();
  }, [supabase, orderId]);
  
  // Update order
  const handleUpdateOrder = async () => {
    try {
      setUpdating(true);
      
      // Prepare notes
      let notesObject = {};
      
      try {
        // Try to parse existing notes if they're JSON
        if (order?.notes) {
          notesObject = JSON.parse(order.notes);
        }
      } catch (e) {
        // If parsing fails, start with empty object
      }
      
      // Add admin notes
      notesObject = {
        ...notesObject,
        admin_notes: notes,
        last_updated: new Date().toISOString()
      };
      
      // Generate tracking URL if carrier and tracking number are provided but URL is not
      let finalTrackingUrl = trackingUrl;
      if (trackingNumber && carrier && !trackingUrl) {
        finalTrackingUrl = generateTrackingUrl(carrier, trackingNumber);
      }
      
      // Check if tracking number was added or changed
      const trackingChanged = order?.tracking_number !== trackingNumber && trackingNumber;
      const statusIsShipped = status === 'shipped';
      
      const { error } = await supabase
        .from('orders')
        .update({
          status,
          tracking_number: trackingNumber || null,
          carrier: carrier || null,
          tracking_url: finalTrackingUrl || null,
          notes: JSON.stringify(notesObject) || null,
          updated_at: new Date().toISOString(),
          shipping_address_id: order?.shipping_address?.id || null
        })
        .eq('id', orderId);
        
      if (error) throw error;
      
      // Show toast message based on status
      switch (status) {
        case 'pending':
          customToast.success('Order marked as pending');
          break;
        case 'paid':
          customToast.success('Order marked as paid');
          break;
        case 'processing':
          customToast.success('Order is being processed');
          break;
        case 'shipped':
          customToast.success('Order marked as shipped');
          break;
        case 'delivered':
          customToast.success('Order marked as delivered');
          break;
        case 'canceled':
          customToast.success('Order has been canceled');
          break;
        case 'refunded':
          customToast.success('Order has been refunded');
          break;
        case 'failed':
          customToast.error('Order failed');
          break;
        default:
          customToast.success('Order updated successfully');
      }
      
      // Update local state
      setOrder(prev => {
        if (!prev) return null;
        return {
          ...prev,
          status,
          tracking_number: trackingNumber,
          carrier,
          tracking_url: finalTrackingUrl,
          notes: JSON.stringify(notesObject),
          shipping_address_id: order?.shipping_address?.id || null
        };
      });
      
      // If tracking number was added or changed and status is shipped, send notification via Edge Function
      if (trackingChanged && statusIsShipped) {
        try {
          // Fetch customer email if not already available
          let customerEmail = order?.profiles?.email;
          
          if (!customerEmail) {
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('email')
              .eq('id', order?.user_id)
              .single();
              
            if (profileError) {
              throw new Error('Could not fetch customer email');
            }
            
            customerEmail = profileData.email;
          }
          
          // Call the Supabase Edge Function to send the email
          const response = await fetch('/api/functions/v1/send-tracking-email', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              order_number: order?.order_number,
              email: customerEmail,
              tracking_number: trackingNumber,
              carrier,
              tracking_url: finalTrackingUrl,
            }),
          });
          
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to send email: ${JSON.stringify(errorData)}`);
          }
          
          customToast.success('Tracking notification email sent to customer');
        } catch (emailError) {
          console.error('Error sending tracking email:', emailError);
          customToast.warning('Order updated but failed to send notification email');
        }
      } else {
        customToast.success('Order updated successfully');
      }
    } catch (error) {
      console.error('Error updating order:', error);
      customToast.error('Failed to update order');
    } finally {
      setUpdating(false);
    }
  };
  
  // Handle tracking number submission
  const handleTrackingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    // Update order with tracking number
    const { error: updateError } = await supabase
      .from('orders')
      .update({ 
        tracking_number: trackingNumber,
        carrier,
        tracking_url: trackingUrl || (carrier && trackingNumber ? generateTrackingUrl(carrier, trackingNumber) : ''),
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId);

    if (updateError) {
      console.error('Failed to save tracking number:', updateError);
      customToast.error('Failed to save tracking number.');
      setSaving(false);
      return;
    }

    // Update local state
    setOrder(prev => {
      if (!prev) return null;
      return {
        ...prev,
        tracking_number: trackingNumber,
        carrier,
        tracking_url: trackingUrl || (carrier && trackingNumber ? generateTrackingUrl(carrier, trackingNumber) : '')
      };
    });

    // Fetch customer email from profiles
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', order?.user_id)
      .single();

    if (profileError || !profileData?.email) {
      console.error('Failed to fetch profile email:', profileError);
      customToast.warning('Tracking saved but failed to send email.');
      setSaving(false);
      return;
    }

    // Call Next.js API route to send the tracking email
    const emailRes = await fetch('/api/send-tracking-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        order_number: order?.order_number,
        email: profileData.email,
        tracking_number: trackingNumber,
        carrier: carrier || 'Local Dispatch',
        tracking_url: trackingUrl || (carrier && trackingNumber ? generateTrackingUrl(carrier, trackingNumber) : ''),
      }),
    });

    const result = await emailRes.json();

    if (result.success) {
      // Log email delivery in the orders table
      const { error: updateEmailStatusError } = await supabase
        .from('orders')
        .update({
          tracking_email_sent: true,
          tracking_email_sent_at: new Date().toISOString(),
        })
        .eq('id', orderId);
      
      if (updateEmailStatusError) {
        console.error('Failed to update email status:', updateEmailStatusError);
      } else {
        // Update local state
        setOrder(prev => {
          if (!prev) return null;
          return {
            ...prev,
            tracking_email_sent: true,
            tracking_email_sent_at: new Date().toISOString()
          };
        });
      }
      
      customToast.success('Tracking number saved and email sent!');
    } else {
      console.error('Email delivery failed:', result.error);
      customToast.warning('Tracking updated but email was not sent.');
    }

    setSaving(false);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Generate shipment label (this would normally connect to a shipping API)
  const handleGenerateShippingLabel = () => {
    customToast.info('Shipping label generation would connect to your shipping provider API');
  };
  
  // Mark as shipped
  const handleMarkAsShipped = async () => {
    if (!trackingNumber) {
      customToast.error('Please enter a tracking number first');
      return;
    }
    
    try {
      setUpdating(true);
      
      const { error } = await supabase
        .from('orders')
        .update({
          status: 'shipped',
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);
        
      if (error) throw error;
      
      // Update local state
      setOrder(prev => {
        if (!prev) return null;
        return {
          ...prev,
          status: 'shipped'
        };
      });
      
      setStatus('shipped');
      
      customToast.success('Order marked as shipped');
    } catch (error) {
      console.error('Error marking order as shipped:', error);
      customToast.error('Failed to update order');
    } finally {
      setUpdating(false);
    }
  };
  
  // Send notification to customer (would integrate with email service)
  const handleSendNotification = () => {
    customToast.info('This would send an email notification to the customer');
  };
  
  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
      </div>
    );
  }
  
  if (!order) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Order Not Found</h2>
          <p className="text-gray-600 mb-6">The requested order could not be found.</p>
          <Link href="/admin/orders" className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333]">
              Back to Orders
            </Link>
          </div>
        </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Link href="/admin/orders" className="text-gray-600 hover:text-gray-900 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Orders
              </Link>
              <span className="text-gray-400">/</span>
              <h1 className="text-xl font-semibold text-gray-900">
                Order #{order.order_number}
              </h1>
            </div>
            <span className={`px-3 py-1 text-sm rounded-full ${STATUS_LABELS[order.status as keyof typeof STATUS_LABELS]?.color || 'bg-gray-100 text-gray-800'}`}>
              {STATUS_LABELS[order.status as keyof typeof STATUS_LABELS]?.label || order.status}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Items Card */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Order Items</h2>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {order?.items?.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {item.product?.media?.find((m: any) => m.is_main)?.url && (
                              <div className="flex-shrink-0 h-12 w-12 mr-4">
                                <Image
                                  src={item.product.media.find((m: any) => m.is_main)?.url || ''}
                                  alt={item.product?.name || 'Product image'}
                                  width={48}
                                  height={48}
                                  className="h-12 w-12 rounded-md object-cover"
                                />
                              </div>
                            )}
                            <div>
                              <div className="text-sm font-medium text-gray-900">{item.product?.name || 'Unknown product'}</div>
                              <div className="text-sm text-gray-500">SKU: {item.product_id.substring(0, 8)}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          €{item.price.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          €{(item.price * item.quantity).toLocaleString('en-US', { minimumFractionDigits: 2 })}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Order Summary Card */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Order Summary</h2>
              </div>
              <div className="p-6">
                <dl className="space-y-3">
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">Subtotal</dt>
                    <dd className="text-sm font-medium text-gray-900">€{order?.subtotal?.toLocaleString('en-US', { minimumFractionDigits: 2 }) || '0.00'}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">Shipping</dt>
                    <dd className="text-sm font-medium text-gray-900">€{order?.shipping_amount?.toLocaleString('en-US', { minimumFractionDigits: 2 }) || '0.00'}</dd>
                  </div>
                  {order?.tax_amount && order.tax_amount > 0 && (
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600">Tax</dt>
                      <dd className="text-sm font-medium text-gray-900">€{order.tax_amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}</dd>
                    </div>
                  )}
                  {order?.discount_amount && order.discount_amount > 0 && (
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600">Discount</dt>
                      <dd className="text-sm font-medium text-green-600">-€{order.discount_amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}</dd>
                    </div>
                  )}
                  <div className="pt-3 border-t border-gray-200">
                    <div className="flex justify-between">
                      <dt className="text-base font-medium text-gray-900">Total</dt>
                      <dd className="text-base font-medium text-gray-900">€{order?.total_amount?.toLocaleString('en-US', { minimumFractionDigits: 2 }) || order?.total?.toLocaleString('en-US', { minimumFractionDigits: 2 }) || '0.00'}</dd>
                    </div>
                  </div>
                </dl>
              </div>
            </div>

            {/* Shipping Information Card */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Shipping Information</h2>
              </div>
              <div className="p-6">
                {order.shipping_address ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 mb-3">Shipping Address</h3>
                      <div className="bg-gray-50 rounded-lg p-4 text-sm space-y-1">
                        <p className="text-gray-900">{order.shipping_address.name}</p>
                        <p className="text-gray-700">{order.shipping_address.street}</p>
                        <p className="text-gray-700">{order.shipping_address.city}, {order.shipping_address.state}</p>
                        <p className="text-gray-700">{order.shipping_address.postal_code}, {order.shipping_address.country}</p>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 mb-3">Shipping Details</h3>
                      <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                        <p className="text-sm"><span className="text-gray-600">Method:</span> <span className="text-gray-900">{order.payment_method}</span></p>
                        <p className="text-sm"><span className="text-gray-600">Status:</span> <span className="text-gray-900">{order.payment_status}</span></p>
                        {order.tracking_number && (
                          <p className="text-sm"><span className="text-gray-600">Tracking:</span> <span className="text-gray-900">{order.tracking_number}</span></p>
                        )}
                        {order.carrier && (
                          <p className="text-sm"><span className="text-gray-600">Carrier:</span> <span className="text-gray-900">{order.carrier}</span></p>
                        )}
                        {order.tracking_url && (
                          <p className="text-sm break-all">
                            <span className="text-gray-600">Tracking URL:</span>{' '}
                            <a href={order.tracking_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              {order.tracking_url}
                            </a>
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No shipping information available</p>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Actions and Customer Info */}
          <div className="space-y-6">
            {/* Customer Information Card */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Customer</h2>
              </div>
              <div className="p-6">
                {order.profiles ? (
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-600">Name</p>
                      <p className="text-sm font-medium text-gray-900">{order.profiles.first_name} {order.profiles.last_name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="text-sm font-medium text-gray-900">{order.profiles.email}</p>
                    </div>
                    {order.profiles.phone && (
                      <div>
                        <p className="text-sm text-gray-600">Phone</p>
                        <p className="text-sm font-medium text-gray-900">{order.profiles.phone}</p>
                      </div>
                    )}
                    <div className="pt-3">
                      <Link 
                        href={`/admin/customers/${order.profiles.id}`}
                        className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                      >
                        View customer profile →
                      </Link>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">Guest order</p>
                )}
              </div>
            </div>

            {/* Order Actions Card */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Order Actions</h2>
              </div>
              <div className="p-6 space-y-6">
                {/* Status Update */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Update Status
                  </label>
                  <select
                    value={status}
                    onChange={(e) => setStatus(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-black focus:border-black text-sm"
                  >
                    {Object.entries(STATUS_LABELS).map(([value, { label }]) => (
                      <option key={value} value={value}>{label}</option>
                    ))}
                  </select>
                </div>

                {/* Tracking Information */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tracking Number
                    </label>
                    <input
                      type="text"
                      value={trackingNumber}
                      onChange={(e) => setTrackingNumber(e.target.value)}
                      placeholder="Enter tracking number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-black focus:border-black text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Carrier
                    </label>
                    <input
                      type="text"
                      value={carrier}
                      onChange={(e) => setCarrier(e.target.value)}
                      placeholder="Enter carrier name"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-black focus:border-black text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      e.g., ValenciaPost, MaimiDispatch, DHL, FedEx
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tracking URL
                    </label>
                    <input
                      type="text"
                      value={trackingUrl}
                      onChange={(e) => setTrackingUrl(e.target.value)}
                      placeholder="Enter or auto-generate tracking URL"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-black focus:border-black text-sm"
                    />
                  </div>
                </div>

                {/* Admin Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Admin Notes
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-black focus:border-black text-sm"
                    placeholder="Add internal notes about this order"
                  />
                </div>

                {/* Action Buttons */}
                <div className="space-y-3 pt-3">
                  <button
                    onClick={handleUpdateOrder}
                    disabled={updating}
                    className="w-full px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium transition-colors"
                  >
                    {updating ? 'Updating...' : 'Update Order'}
                  </button>

                  <button
                    onClick={handleMarkAsShipped}
                    disabled={updating || order.status === 'shipped' || order.status === 'delivered'}
                    className="w-full px-4 py-2 bg-white text-black border border-black rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium transition-colors"
                  >
                    Mark as Shipped
                  </button>

                  <button
                    onClick={handleGenerateShippingLabel}
                    className="w-full px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-sm font-medium transition-colors"
                  >
                    Generate Shipping Label
                  </button>

                  <button
                    onClick={handleSendNotification}
                    className="w-full px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-sm font-medium transition-colors"
                  >
                    Send Customer Notification
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}