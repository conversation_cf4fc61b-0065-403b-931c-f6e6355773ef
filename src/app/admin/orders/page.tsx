'use client';
import { Tables } from '@/lib/database.types'; // adjust path if needed
import { Database } from '@/lib/database.types';

import { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';
import { 
  Search, 
  Edit, 
  Package, 
  Truck, 
  XCircle
} from 'lucide-react';
import toast from 'react-hot-toast';
import { makeAuthenticatedRequest } from '@/lib/session-utils';

type Order = {
  id: string;
  order_number: string | null;
  total_amount: number;
  status: string | null;
  payment_status: string | null;
  created_at: string | null;
  profiles: {
    id: string;
    email: string;
    first_name: string | null;
    last_name: string | null;
    street_address: string | null;
    city: string | null;
  } | null;
  shipping_addresses: {
    id: string;
    name: string | null;
    street: string | null;
    city: string | null;
    state: string | null;
    country: string | null;
    postal_code: string | null;
  } | null;
  order_items: Array<{
    id: string;
    quantity: number;
    product: {
      id: string;
      name: string;
      price: number;
      product_media: Array<{
        url: string;
        is_main: boolean;
      }> | null;
    } | null;
  }>;
};

type OrderStatus = 'pending' | 'paid' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded' | 'failed';

// Order status labels and colors
const STATUS_LABELS = {
  pending: { label: 'Payment Not Completed', color: 'bg-orange-100 text-orange-800' },
  paid: { label: 'Paid', color: 'bg-green-100 text-green-800' },
  processing: { label: 'Processing', color: 'bg-blue-100 text-blue-800' },
  shipped: { label: 'Shipped', color: 'bg-purple-100 text-purple-800' },
  delivered: { label: 'Delivered', color: 'bg-emerald-100 text-emerald-800' },
  cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800' },
  refunded: { label: 'Refunded', color: 'bg-gray-100 text-gray-800' },
  failed: { label: 'Failed', color: 'bg-red-100 text-red-800' },
};

// Helper function to get status label based on status and payment_status
const getStatusLabel = (status: string, paymentStatus?: string) => {
  if (status === 'cancelled' && paymentStatus === 'incomplete') {
    return { label: 'Payment Not Completed', color: 'bg-orange-100 text-orange-800' };
  }
  return STATUS_LABELS[status as keyof typeof STATUS_LABELS] || { label: status, color: 'bg-orange-100 text-orange-800' };
};

export default function AdminOrders() {
  const supabase = createClientComponentClient();
  const router = useRouter();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [orderCount, setOrderCount] = useState(0);
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [updatingOrders, setUpdatingOrders] = useState<Set<string>>(new Set());
  
  // Fetch orders using the new API endpoint
  const fetchOrders = useCallback(async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      
      if (selectedStatus) {
        params.append('status', selectedStatus);
      }
      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const response = await fetch(`/api/admin/orders?${params}`, {
        credentials: 'include' // Include session cookies
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch orders');
      }

      const data = await response.json();
      setOrders(data.orders);
      setOrderCount(data.total);
    } catch (error: any) {
      console.error('Error fetching orders:', error);
      toast.error(error.message || 'Failed to load orders');
      
      // Redirect to login if unauthorized
      if (error.message?.includes('Unauthorized')) {
        router.push('/auth/login');
      }
    } finally {
      setLoading(false);
    }
  }, [page, limit, selectedStatus, searchTerm, router]);
  
  // Update order status with retry logic
  const handleStatusChange = async (orderId: string, newStatus: OrderStatus) => {
    // Prevent concurrent updates to the same order
    if (updatingOrders.has(orderId)) {
      toast.error('Order update already in progress');
      return;
    }

    setUpdatingOrders(prev => new Set(prev).add(orderId));
    const loadingToastId = toast.loading('Updating order status...');

    const makeRequest = async (attempt: number = 1): Promise<any> => {
      console.log(`Updating order status (attempt ${attempt}):`, { orderId, newStatus });
      if (attempt === 1) {
        console.log('User agent:', navigator.userAgent);
        console.log('Current URL:', window.location.href);
      }
      
      let response;
      try {
        response = await makeAuthenticatedRequest(`/api/admin/orders/${orderId}`, {
          method: 'PATCH',
          headers: {
            'Cache-Control': 'no-cache',
          },
          cache: 'no-store',
          body: JSON.stringify({ status: newStatus })
        });
      } catch (sessionError: any) {
        console.error(`Session error on attempt ${attempt}:`, sessionError);
        
        // If session error, create a simpler fallback request
        console.log('🔄 Falling back to basic fetch request');
        response = await fetch(`/api/admin/orders/${orderId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
          },
          credentials: 'include',
          cache: 'no-store',
          body: JSON.stringify({ status: newStatus })
        });
      }

      const data = await response.json();
      console.log(`Response (attempt ${attempt}):`, { 
        status: response.status, 
        data,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (response.status === 401 && attempt < 3) {
        console.log(`🔄 Authentication failed on attempt ${attempt}, retrying...`);
        
        // Add a longer delay for subsequent attempts in case it's a rate limiting issue
        const delay = attempt === 1 ? 1000 : 2000;
        await new Promise(resolve => setTimeout(resolve, delay));
        
        return makeRequest(attempt + 1);
      }
      
      // If all retries failed with 401, show a helpful message
      if (response.status === 401 && attempt === 3) {
        console.error('🚨 All authentication attempts failed. This might be a session sync issue.');
        console.log('💡 Suggestion: Try refreshing the page or logging out and back in.');
      }

      if (!response.ok) {
        throw new Error(data.error || `Failed to update order status: ${response.status}`);
      }

      return { response, data };
    };

    try {
      const { response, data } = await makeRequest();

      // Update local state
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId 
            ? { ...order, status: newStatus, updated_at: new Date().toISOString() }
            : order
        )
      );

      toast.success(`Order status updated to ${newStatus}`, {
        id: loadingToastId
      });
    } catch (error: any) {
      console.error('Error updating order status:', error);
      toast.error(`Error: ${error.message}`, {
        id: loadingToastId
      });
    } finally {
      // Remove order from updating set
      setUpdatingOrders(prev => {
        const newSet = new Set(prev);
        newSet.delete(orderId);
        return newSet;
      });
    }
  };
  
  // Format date string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);
  
  return (
      <div className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
          
          <div className="flex flex-col md:flex-row gap-3">
            {/* Search */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search order #, tracking..."
                className="w-full md:w-64 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search
                className="absolute right-3 top-2.5 text-gray-400"
                width="20"
                height="20"
              />
            </div>
            
            {/* Status filter */}
            <select
              className="w-full md:w-auto px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
              value={selectedStatus || ''}
              onChange={(e) => setSelectedStatus(e.target.value || null)}
            >
              <option value="">All Statuses</option>
              {Object.entries(STATUS_LABELS).map(([value, { label }]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>
          </div>
        </div>
        
        {/* Orders table */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#171717] mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading orders...</p>
            </div>
          ) : orders.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-gray-600">No orders found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Items
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {order.order_number}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-700">
                          {formatDate(order.created_at || '')}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        {order.profiles ? (
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {order.profiles.first_name || 'Guest'} {order.profiles.last_name || ''}
                            </div>
                            <div className="text-xs text-gray-500">
                              {order.profiles.email}
                            </div>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500">Guest order</div>
                        )}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {order.order_items?.map((item: any) => (
                            <div key={item.id}>
                              {item.product?.name} x {item.quantity} (€{item.product?.price})
                            </div>
                          )) || 'No items'}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {order.shipping_addresses
                            ? `${order.shipping_addresses.city || ''}, ${order.shipping_addresses.country || ''}`
                            : 'No address'}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        {(() => {
                          const statusInfo = getStatusLabel(order.status || 'pending', order.payment_status || undefined);
                          return (
                            <span className={`px-2 py-1 text-xs rounded-full whitespace-nowrap ${statusInfo.color}`}>
                              {statusInfo.label}
                            </span>
                          );
                        })()}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          €{order.total_amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/admin/orders/${order.id}`)}
                            className="p-1 text-blue-600 hover:text-blue-800"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleStatusChange(order.id, 'shipped')}
                            className="p-1 text-green-600 hover:text-green-800 disabled:opacity-50"
                            disabled={order.status === 'shipped' || order.status === 'delivered' || updatingOrders.has(order.id)}
                          >
                            <Truck size={16} />
                          </button>
                          <button
                            onClick={() => handleStatusChange(order.id, 'delivered')}
                            className="p-1 text-purple-600 hover:text-purple-800 disabled:opacity-50"
                            disabled={order.status === 'delivered' || updatingOrders.has(order.id)}
                          >
                            <Package size={16} />
                          </button>
                          <button
                            onClick={() => handleStatusChange(order.id, 'cancelled')}
                            className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50"
                            disabled={order.status === 'cancelled' || order.status === 'delivered' || updatingOrders.has(order.id)}
                          >
                            <XCircle size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {/* Pagination */}
          {orderCount > limit && (
            <div className="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6 flex items-center justify-between">
              <div className="hidden sm:block">
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{(page - 1) * limit + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(page * limit, orderCount)}
                  </span>{' '}
                  of <span className="font-medium">{orderCount}</span> results
                </p>
              </div>
              <div className="flex-1 flex justify-between sm:justify-end">
                <button
                  onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
                  disabled={page === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setPage((prev) => (prev * limit < orderCount ? prev + 1 : prev))}
                  disabled={page * limit >= orderCount}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
  );
}