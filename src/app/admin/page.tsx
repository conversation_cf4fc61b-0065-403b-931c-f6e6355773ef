'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/database.types';
import { createLogger } from '@/lib/debug';
import Link from 'next/link';

// Create a logger for this component
const logger = createLogger('AdminPage');

// Simple AdminDashboard component
const AdminDashboard = () => {
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();
  
  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session) {
        setUserEmail(data.session.user.email || null);
      }
    };
    
    getUser();
  }, [supabase]);
  
  const handleLogout = async () => {
    await supabase.auth.signOut();
    window.location.href = '/admin/login';
  };
  
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Admin Dashboard</h1>
          {userEmail && (
            <p className="text-gray-600 mt-1">Logged in as: {userEmail}</p>
          )}
        </div>
        <button
          onClick={handleLogout}
          className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md transition text-gray-800"
        >
          Logout
        </button>
      </div>
      
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Welcome to the Admin Dashboard</h2>
        <p className="text-gray-600">
          You have successfully logged in as an admin. This dashboard will allow you to manage products, 
          orders, customers, and other aspects of your store.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[
          { title: 'Products', icon: '📦', link: '/admin/products', description: 'Manage your product inventory' },
          { title: 'Orders', icon: '🧾', link: '/admin/orders', description: 'View and manage customer orders' },
          { title: 'Customers', icon: '👥', link: '/admin/customers', description: 'Manage customer accounts' },
          { title: 'Categories', icon: '🗂️', link: '/admin/categories', description: 'Organize your products' },
          { title: 'Settings', icon: '⚙️', link: '/admin/settings', description: 'Configure store settings' }
        ].map((item, index) => (
          <div key={index} className="bg-white rounded-lg shadow hover:shadow-md transition p-6">
            <div className="flex items-start">
              <span className="text-3xl mr-4">{item.icon}</span>
              <div>
                <h3 className="text-xl font-semibold text-gray-800">{item.title}</h3>
                <p className="text-gray-600 text-sm mt-1">{item.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default function AdminPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [profileInfo, setProfileInfo] = useState<any>(null);
  const [debugMode, setDebugMode] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const authParam = searchParams.get('auth');
  
  const supabase = createClientComponentClient<Database>();
  
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        setLoading(true);
        logger.info('Checking admin status on page load');

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Admin verification timeout')), 5000)
        );

        const verificationPromise = (async () => {
          // Get current session
          const { data: { session }, error: sessionError } = await supabase.auth.getSession();

          if (sessionError) {
            logger.error('Error getting session:', sessionError.message);
            throw new Error('Failed to verify your session. Please try logging in again.');
          }

          if (!session) {
            logger.warn('No session found, redirecting to login');
            router.push('/admin/login');
            return;
          }

          // Store session info for debugging
          setSessionInfo({
            id: session.user.id,
            email: session.user.email,
            sessionId: session.access_token || 'unknown',
            expiresAt: session.expires_at
          });

          return session;
        })();

        const session = await Promise.race([verificationPromise, timeoutPromise]) as any;
        
        logger.info(`Session found for user: ${session.user.email}`);

        // In development, skip admin check if NEXT_PUBLIC_SKIP_ADMIN_CHECK is true
        if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_SKIP_ADMIN_CHECK === 'true') {
          logger.info('Skipping admin check in development mode');
          setIsAdmin(true);
          setLoading(false);
          return;
        }

        // Check if admin status is cached in session metadata first
        const isAdminFromSession = session.user.user_metadata?.is_admin;
        if (isAdminFromSession) {
          logger.info('Admin status confirmed from session metadata');
          setIsAdmin(true);
          setLoading(false);
          return;
        }

        // Check if user has admin profile with timeout
        const profilePromise = supabase
          .from('profiles')
          .select('is_admin, email, id')
          .eq('id', session.user.id)
          .single();

        const profileTimeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Profile query timeout')), 3000)
        );

        const { data: profile, error: profileError } = await Promise.race([
          profilePromise,
          profileTimeoutPromise
        ]) as any;
        
        if (profileError) {
          logger.error('Error getting profile:', profileError.message);
          
          // Try to create profile if it doesn't exist
          if (profileError.message.includes('not found')) {
            logger.info('Profile not found, attempting to create');
            
            // Check if user email is admin
            const isAdminEmail = session.user.email?.toLowerCase() === '<EMAIL>' ||
              session.user.email?.toLowerCase().includes('<EMAIL>');
            
            if (isAdminEmail) {
              // Create admin profile
              const { data: newProfile, error: createError } = await supabase
                .from('profiles')
                .insert({
                  id: session.user.id,
                  email: session.user.email || '',
                  role: 'admin',
                  is_admin: true,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                })
                .select()
                .single();
              
              if (createError) {
                logger.error('Failed to create admin profile:', createError.message);
                setError('Failed to create your admin profile. Please try again.');
                setLoading(false);
                return;
              }
              
              logger.info('Successfully created admin profile');
              setProfileInfo(newProfile);
              setIsAdmin(true);
              setLoading(false);
              return;
            } else {
              setError('You do not have admin privileges. Please use an admin account.');
              setLoading(false);
              return;
            }
          } else {
            setError('Failed to verify your admin status. Please try again.');
            setLoading(false);
            return;
          }
        }
        
        // Store profile info for debugging
        setProfileInfo(profile);
        
        // Check if user is admin - use only is_admin flag for consistency
        const isAdminFlag = profile.is_admin === true;

        logger.info('Admin status check:', { isAdminFlag, profile });

        if (isAdminFlag) {
          logger.info('User confirmed as admin');
          setIsAdmin(true);
          setLoading(false);
        } else {
          logger.warn('User is not admin, redirecting to login');
          setError('You do not have admin privileges. Please use an admin account.');
          setLoading(false);
        }
      } catch (err) {
        logger.error('Unexpected error in admin check:', err);
        setError('An unexpected error occurred. Please try again.');
        setLoading(false);
      }
    };
    
    checkAdminStatus();
  }, [router, supabase]);
  
  // If we're loading, show loading indicator with timeout message
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mb-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          </div>
          <h2 className="text-xl font-semibold text-gray-700">Verifying admin access...</h2>
          <p className="text-gray-500 mt-2">Please wait while we confirm your credentials.</p>
          <p className="text-xs text-gray-400 mt-4">
            If this takes more than 10 seconds, please refresh the page.
          </p>
        </div>
      </div>
    );
  }
  
  // If there's an error, show error message with retry button
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="bg-white rounded-lg shadow-md p-8 max-w-md w-full">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="flex flex-col space-y-3">
              <button
                onClick={() => router.push('/admin/login')}
                className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded transition"
              >
                Return to Login
              </button>
              <button
                onClick={() => setDebugMode(!debugMode)}
                className="text-gray-500 hover:text-gray-700 text-sm underline"
              >
                {debugMode ? 'Hide Debug Info' : 'Show Debug Info'}
              </button>
            </div>
            
            {debugMode && (
              <div className="mt-6 text-left bg-gray-50 p-4 rounded text-xs overflow-auto max-h-60">
                <h3 className="font-bold mb-2">Debug Information:</h3>
                <div className="mb-2">
                  <strong>Auth Param:</strong> {authParam || 'None'}
                </div>
                {sessionInfo && (
                  <div className="mb-2">
                    <strong>Session Info:</strong>
                    <pre>{JSON.stringify(sessionInfo, null, 2)}</pre>
                  </div>
                )}
                {profileInfo && (
                  <div>
                    <strong>Profile Info:</strong>
                    <pre>{JSON.stringify(profileInfo, null, 2)}</pre>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
  
  // If user is admin, show admin dashboard
  if (isAdmin) {
    return <AdminDashboard />;
  }
  
  // Fallback - should never reach here
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <p className="text-gray-700">Something went wrong. Please try again.</p>
        <button
          onClick={() => router.push('/admin/login')}
          className="mt-4 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded transition"
        >
          Return to Login
        </button>
      </div>
    </div>
  );
}
