'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { customToast } from '@/components/ui/CustomToast';
import { Trash2, Plus, Edit, X, Save, CreditCard, CheckCircle } from 'lucide-react';
import AdminLayout from '../layout';

interface PaymentMethod {
  id: string;
  name: string;
  description: string | null;
  provider: string;
  is_active: boolean;
  requires_api_key: boolean;
  api_key_name: string | null;
  api_key_value: string | null;
  created_at: string;
}

export default function PaymentMethodsPage() {
  const supabase = createClientComponentClient();
  
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    provider: 'stripe',
    is_active: true,
    requires_api_key: false,
    api_key_name: '',
    api_key_value: ''
  });
  
  // Fetch payment methods on component mount
  useEffect(() => {
    fetchPaymentMethods();
  }, []);
  
  // Function to fetch payment methods from the database
  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('payment_methods')
        .select('*')
        .order('created_at', { ascending: false });
        
      if (error) {
        throw error;
      }
      
      setPaymentMethods(data || []);
    } catch (err) {
      console.error('Error fetching payment methods:', err);
      customToast.error('Failed to load payment methods');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const target = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: target.checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };
  
  // Start editing a payment method
  const startEditing = (method: PaymentMethod) => {
    setIsEditing(method.id);
    setFormData({
      name: method.name,
      description: method.description || '',
      provider: method.provider,
      is_active: method.is_active,
      requires_api_key: method.requires_api_key,
      api_key_name: method.api_key_name || '',
      api_key_value: method.api_key_value || ''
    });
    setIsCreating(false);
  };
  
  // Cancel editing or creating
  const cancelAction = () => {
    setIsEditing(null);
    setIsCreating(false);
    setFormData({
      name: '',
      description: '',
      provider: 'stripe',
      is_active: true,
      requires_api_key: false,
      api_key_name: '',
      api_key_value: ''
    });
  };
  
  // Submit form to create or update a payment method
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.provider) {
      customToast.error('Method name and provider are required');
      return;
    }
    
    try {
      if (isEditing) {
        // Update existing payment method
        const { error } = await supabase
          .from('payment_methods')
          .update({
            name: formData.name,
            description: formData.description || null,
            provider: formData.provider,
            is_active: formData.is_active,
            requires_api_key: formData.requires_api_key,
            api_key_name: formData.requires_api_key ? formData.api_key_name : null,
            api_key_value: formData.requires_api_key ? formData.api_key_value : null,
            updated_at: new Date().toISOString()
          })
          .eq('id', isEditing);
          
        if (error) throw error;
        
        customToast.success('Payment method updated successfully');
      } else {
        // Create new payment method
        const { error } = await supabase
          .from('payment_methods')
          .insert({
            name: formData.name,
            description: formData.description || null,
            provider: formData.provider,
            is_active: formData.is_active,
            requires_api_key: formData.requires_api_key,
            api_key_name: formData.requires_api_key ? formData.api_key_name : null,
            api_key_value: formData.requires_api_key ? formData.api_key_value : null
          });
          
        if (error) throw error;
        
        customToast.success('Payment method created successfully');
      }
      
      // Refresh payment methods list
      await fetchPaymentMethods();
      
      // Reset form
      cancelAction();
    } catch (err) {
      console.error('Error saving payment method:', err);
      customToast.error('Failed to save payment method');
    }
  };
  
  // Delete a payment method
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this payment method? This action cannot be undone.')) {
      return;
    }
    
    try {
      const { error } = await supabase
        .from('payment_methods')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
      
      customToast.success('Payment method deleted successfully');
      await fetchPaymentMethods();
    } catch (err) {
      console.error('Error deleting payment method:', err);
      customToast.error('Failed to delete payment method');
    }
  };
  
  // Toggle payment method active status
  const toggleActiveStatus = async (id: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('payment_methods')
        .update({
          is_active: !currentStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
        
      if (error) throw error;
      
      customToast.success(`Payment method ${currentStatus ? 'disabled' : 'enabled'}`);
      await fetchPaymentMethods();
    } catch (err) {
      console.error('Error updating payment method status:', err);
      customToast.error('Failed to update payment method status');
    }
  };
  
  // Get provider display name
  const getProviderDisplayName = (provider: string) => {
    const providers: Record<string, string> = {
      'stripe': 'Stripe',
      'paypal': 'PayPal',
      'bank_transfer': 'Bank Transfer',
      'cash_on_delivery': 'Cash on Delivery',
      'klarna': 'Klarna',
      'apple_pay': 'Apple Pay',
      'google_pay': 'Google Pay',
      'other': 'Other'
    };
    
    return providers[provider] || provider;
  };

  return (
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Payment Methods</h1>
            <p className="text-gray-600 mt-1">Manage payment options for your store</p>
          </div>
          
          {!isCreating && !isEditing && (
            <button
              onClick={() => {
                setIsCreating(true);
                setIsEditing(null);
                setFormData({
                  name: '',
                  description: '',
                  provider: 'stripe',
                  is_active: true,
                  requires_api_key: false,
                  api_key_name: '',
                  api_key_value: ''
                });
              }}
              className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] flex items-center"
            >
              <Plus size={16} className="mr-2" />
              Add Payment Method
            </button>
          )}
        </div>
        
        {/* Form for creating or editing payment methods */}
        {(isCreating || isEditing) && (
          <div className="bg-white p-6 rounded-lg shadow-sm mb-6 border border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800">
                {isEditing ? 'Edit Payment Method' : 'Add New Payment Method'}
              </h2>
              <button
                onClick={cancelAction}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Method Name*
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                    placeholder="e.g., Credit Card"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Provider*
                  </label>
                  <select
                    name="provider"
                    value={formData.provider}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                  >
                    <option value="stripe">Stripe</option>
                    <option value="paypal">PayPal</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="cash_on_delivery">Cash on Delivery</option>
                    <option value="klarna">Klarna</option>
                    <option value="apple_pay">Apple Pay</option>
                    <option value="google_pay">Google Pay</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                  rows={2}
                  placeholder="Brief description of the payment method"
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requires_api_key"
                  name="requires_api_key"
                  checked={formData.requires_api_key}
                  onChange={(e) => setFormData({...formData, requires_api_key: e.target.checked})}
                  className="h-4 w-4 text-[#171717] focus:ring-[#171717] border-gray-300 rounded"
                />
                <label htmlFor="requires_api_key" className="ml-2 block text-sm text-gray-700">
                  Requires API Key
                </label>
              </div>
              
              {formData.requires_api_key && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      API Key Name
                    </label>
                    <input
                      type="text"
                      name="api_key_name"
                      value={formData.api_key_name}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                      placeholder="e.g., STRIPE_SECRET_KEY"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      API Key Value
                    </label>
                    <input
                      type="password"
                      name="api_key_value"
                      value={formData.api_key_value}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                      placeholder="Enter API key value"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Note: API keys are stored securely and encrypted in the database
                    </p>
                  </div>
                </div>
              )}
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                  className="h-4 w-4 text-[#171717] focus:ring-[#171717] border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700">
                  Active (available for customers to use)
                </label>
              </div>
              
              <div className="flex justify-end space-x-3 pt-2">
                <button
                  type="button"
                  onClick={cancelAction}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] flex items-center"
                >
                  <Save size={16} className="mr-2" />
                  {isEditing ? 'Update Method' : 'Create Method'}
                </button>
              </div>
            </form>
          </div>
        )}
        
        {/* Payment methods list */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
            {paymentMethods.length === 0 ? (
              <div className="p-6 text-center">
                <p className="text-gray-500">No payment methods found</p>
                <button
                  onClick={() => {
                    setIsCreating(true);
                    setIsEditing(null);
                  }}
                  className="mt-4 px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] inline-flex items-center"
                >
                  <Plus size={16} className="mr-2" />
                  Add Your First Payment Method
                </button>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Method
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Provider
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      API Key
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paymentMethods.map((method) => (
                    <tr key={method.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <CreditCard className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">{method.name}</div>
                            {method.description && (
                              <div className="text-xs text-gray-500 mt-1">{method.description}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{getProviderDisplayName(method.provider)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {method.requires_api_key ? (
                            <span className="flex items-center">
                              <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                              {method.api_key_name || 'Configured'}
                            </span>
                          ) : (
                            'Not required'
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            method.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {method.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-3">
                          <button
                            onClick={() => toggleActiveStatus(method.id, method.is_active)}
                            className={`text-sm ${
                              method.is_active ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'
                            }`}
                          >
                            {method.is_active ? 'Disable' : 'Enable'}
                          </button>
                          <button
                            onClick={() => startEditing(method)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(method.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        )}
      </div>
  );
}
