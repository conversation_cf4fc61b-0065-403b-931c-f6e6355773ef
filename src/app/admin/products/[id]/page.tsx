'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/database.types';
import { customToast } from '@/components/ui/CustomToast';
import Link from 'next/link';
import { trackProductView } from '@/lib/product-analytics';

// Add Cloudinary types
declare global {
  interface Window {
    cloudinary?: {
      openUploadWidget: (
        options: any,
        callback: (error: any, result: any) => void
      ) => void;
    };
  }
}

// NOTE: Wishlist button not found in this file.
// If the wishlist button is rendered in another component (e.g., ProductCard),
// update its onClick handler to: onClick={handleAddToWishlist}

const generateSlugFromName = (name: string) =>
  name.toLowerCase().trim().replace(/\s+/g, '-').replace(/[^\w-]+/g, '');
declare global {
  interface Window {
    cloudinary?: {
      openUploadWidget: (
        options: any,
        callback: (error: any, result: any) => void
      ) => void;
    };
  }
}

type Product = Database['public']['Tables']['products']['Row'];
type ProductMedia = Database['public']['Tables']['product_media']['Row'];
type Category = Database['public']['Tables']['categories']['Row'];
type ProductCondition = Database['public']['Tables']['product_conditions']['Row'];

interface MediaItem {
  id: string;
  url: string;
  alt: string | null;
  product_id: string | null;
  created_at: string | null;
  position: number | null;
  type: string | null;
  is_main: boolean | null;
}

interface ProductSpecifications {
  color?: string;
  material?: string;
  size?: string;
  design?: string;
  strap?: string;
  interior?: string;
  condition?: string;
  rarity?: string;
  versatility?: string;
  [key: string]: any;
}

export default function ProductPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const isNewProduct = params.id === 'new';
  const supabase = createClientComponentClient<Database>();

  // States for product form
  const [product, setProduct] = useState<Partial<Product>>({
    id: isNewProduct ? undefined : params.id,
    name: '',
    description: '',
    price: 0,
    quantity: 1, // Default quantity to 1
    slug: '',
    category_id: null,
    condition_id: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  });

  const [categories, setCategories] = useState<Category[]>([]);
  const [conditions, setConditions] = useState<ProductCondition[]>([]);
  const [collections, setCollections] = useState<Database['public']['Tables']['collections']['Row'][]>([]);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [media, setMedia] = useState<ProductMedia[]>([]);
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [specs, setSpecs] = useState<ProductSpecifications>({
    color: '',
    material: '',
    size: '',
    design: '',
    strap: '',
    interior: '',
    condition: '',
    rarity: '',
    versatility: ''
  });
  const [selectedImage, setSelectedImage] = useState<MediaItem | null>(null);
  const [showModelViewer, setShowModelViewer] = useState(false);

  useEffect(() => {
    const script = document.createElement('script');
    script.type = 'module';
    script.src = 'https://unpkg.com/@google/model-viewer/dist/model-viewer.min.js';
    document.head.appendChild(script);
    
    return () => {
      // Clean up if needed
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);
  
  useEffect(() => {
    if (!isNewProduct && typeof product.id === 'string' && !!product.name?.trim()) {
      console.log('[ProductPage] Tracking view for product:', product.id);
      trackProductView(product.id).catch((error) => {
        console.error('[ProductPage] Failed to track product view:', error);
      });
    }
  }, [isNewProduct, product.id, product.name]);

  useEffect(() => {
    const fetchData = () => {
      setLoading(true);
      
      // Start with Promise.resolve() to ensure proper Promise chain typing
      Promise.resolve()
        .then(() => {
          // Fetch categories
          return supabase
            .from('categories')
            .select('*');
        })
        .then(({ data: categoriesData, error: categoriesError }) => {
          if (categoriesError) {
            console.error(`Error fetching categories: ${categoriesError.message}`);
            setError('Failed to load categories');
            setLoading(false);
            return null;
          }
          
          setCategories(categoriesData || []);
          
          // Fetch product conditions
          return supabase
            .from('product_conditions')
            .select('*');
        })
        .then(result => {
          if (!result) return null; // Early return if previous promise didn't resolve
          
          const { data: conditionsData, error: conditionsError } = result;
          if (conditionsError) {
            console.error(`Error fetching conditions: ${conditionsError.message}`);
            setError('Failed to load product conditions');
            setLoading(false);
            return null;
          }
          
          setConditions(conditionsData || []);
          return supabase.from('collections').select('*');
        })
        .then(result => {
          if (!result) return null;
          
          const { data: collectionsData, error: collectionsError } = result;
          if (collectionsError) {
            console.error(`Error fetching collections: ${collectionsError.message}`);
            setError('Failed to load collections');
            setLoading(false);
            return null;
          }
          setCollections(collectionsData || []);
          
          // If editing an existing product, fetch its data
          if (!isNewProduct) {
            return supabase
              .from('products')
              .select('*')
              .eq('id', params.id)
              .single();
          }
          return null;
        })
        .then(result => {
          if (!result) {
            // If we're creating a new product or previous promise didn't resolve
            setLoading(false);
            return null;
          }
          
          const { data: productData, error: productError } = result;
          if (productError) {
            console.error(`Error fetching product: ${productError.message}`);
            customToast.error('Error fetching product');
            setLoading(false);
            return null;
          }
          
          if (productData) {
            setProduct({
              ...productData,
              price: productData.price || 0,
              quantity: productData.quantity || 1
            });
            
            // Set specifications
            if (productData.specifications) {
              const specifications = productData.specifications as ProductSpecifications;
              setSpecs({
                color: specifications.color || '',
                material: specifications.material || '',
                size: specifications.size || '',
                design: specifications.design || '',
                strap: specifications.strap || '',
                interior: specifications.interior || '',
                condition: specifications.condition || '',
                rarity: specifications.rarity || '',
                versatility: specifications.versatility || ''
              });
            }
            
            // Fetch product media
            return supabase
              .from('product_media')
              .select('*')
              .eq('product_id', params.id)
              .order('position', { ascending: true });
          }
          
          return null;
        })
        .then(result => {
          if (!result) {
            // If we're creating a new product or previous promise didn't resolve
            setLoading(false);
            return;
          }
          
          const { data: mediaData, error: mediaError } = result;
          if (mediaError) {
            console.error(`Error fetching product media: ${mediaError.message}`);
            customToast.error('Error fetching product media');
          } else if (mediaData) {
            setMedia(mediaData);
          }
          
          setLoading(false);
        })
        .catch((error: Error) => {
          console.error('Error in fetch chain:', error);
          setLoading(false);
          customToast.error('Error loading data');
        });
    };

    fetchData();
  }, [isNewProduct, params.id]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'price' || name === 'quantity') {
      // Ensure price and quantity are numbers
      setProduct({ ...product, [name]: parseFloat(value) || 0 });
    } else {
      setProduct({ ...product, [name]: value });
    }
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')  // Remove special characters
      .replace(/\s+/g, '-')      // Replace spaces with hyphens
      .replace(/-+/g, '-')       // Replace multiple hyphens with single hyphen
      .trim();
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    const slug = generateSlug(name);
    setProduct({ ...product, name, slug });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setFiles([...files, ...newFiles]);
    }
  };

  const handleCreateCollection = async () => {
    if (!newCollectionName) return;
    const slug = generateSlugFromName(newCollectionName);
    const { data, error } = await supabase
      .from('collections')
      .insert({ name: newCollectionName, slug })
      .select()
      .single();

    if (error) {
      console.error('Failed to create collection:', error);
      customToast.error('Failed to create collection');
      return;
    }

    setCollections(prev => [...prev, data]);
    setProduct(prev => ({ ...prev, collection_id: data.id }));
    setNewCollectionName('');
    customToast.success('Collection created and assigned!');
  };

  // Cloudinary upload widget
  const openCloudinaryWidget = () => {
    if (typeof window !== 'undefined' && window.cloudinary) {
      window.cloudinary.openUploadWidget(
        {
          cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'dlfizdedi',
          uploadPreset: 'shop_maimi', // UPDATED preset name matches Cloudinary exactly
          multiple: true,
          sources: ['local', 'url', 'camera'],
          maxFiles: 5,
          folder: 'products',             // Save to products folder
          tags: ['product', 'auto_3d'],   // Important for AR/3D
        },
        async (error, result) => {
          if (!error && result && result.event === 'success') {
            // Check if product ID exists
            if (!product.id) {
              customToast.error('Please save the product first before adding images');
              return;
            }
            
            // Create a new media item from the uploaded image
            const newMedia: ProductMedia = {
              id: crypto.randomUUID(),
              product_id: product.id,
              url: result.info.secure_url,
              alt: product.name || '',
              position: media.length,
              type: 'image',
              created_at: new Date().toISOString(),
              is_main: null
            };

            const { error: mediaInsertError } = await supabase
              .from('product_media')
              .insert(newMedia);

            if (mediaInsertError) {
              console.error('Error saving media to Supabase:', mediaInsertError);
              customToast.error('Failed to save image to database');
              return;
            }

            // Add to media state
            setMedia([...media, newMedia]);

            // Automatically generate 3D model URL for first uploaded image
            if (media.length === 0) {
              const auto3DUrl = result.info.secure_url
                .replace('/image/upload/', '/image/upload/auto_3d/v1/')
                .replace(/\.\w+$/, '.glb');

              // Update product in Supabase with model_url
              const { error: modelError } = await supabase
                .from('products')
                .update({ model_url: auto3DUrl })
                .eq('id', product.id || '');

              if (modelError) {
                console.error('Failed to save model_url:', modelError);
              } else {
                setProduct(prev => ({ ...prev, model_url: auto3DUrl }));
                customToast.success('3D model auto-generated!');
              }
            }

            customToast.success('Image uploaded successfully');
          } else if (error) {
            console.error('Cloudinary upload error:', error);
            customToast.error('Failed to upload image');
          }
        }
      );
    } else {
      customToast.error('Cloudinary widget not available');
    }
  };

  const handleDeleteMedia = async (mediaId: string) => {
    try {
      // Find the media item to get its URL
      const mediaItem = media.find(item => item.id === mediaId);
      if (!mediaItem) return;
      
      // No need to delete from Supabase Storage anymore
      // Cloudinary URLs are managed through Cloudinary's dashboard
      
      // Delete the database record
      const { error } = await supabase
        .from('product_media')
        .delete()
        .eq('id', mediaId);
      
      if (error) throw error;

      // Optionally, delete image from Cloudinary if configured
      // Example: Extract public_id from the URL and call Cloudinary Admin API
      // const publicId = mediaItem.url.split('/').pop()?.split('.')[0];
      // await deleteImageFromCloudinary(publicId);

      // Update local state
      setMedia(media.filter(item => item.id !== mediaId));
      customToast.success('Media deleted successfully');
    } catch (err) {
      console.error('Error deleting media:', err);
      customToast.error('Failed to delete media');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccessMessage(null);
    
    try {
      // Validate required fields
      if (!product.name || !product.price || !product.quantity) {
        throw new Error('Name, price, and quantity are required');
      }
      
      // Ensure slug is set
      if (!product.slug) {
        product.slug = generateSlug(product.name);
      }
      
      // Set updated timestamp
      product.updated_at = new Date().toISOString();

      if (!product.collection_id && specs.rarity) {
        const raritySlug = generateSlugFromName(specs.rarity);
        const matchingCollection = collections.find(c => c.slug === raritySlug);
        if (matchingCollection) {
          product.collection_id = matchingCollection.id;
        }
      }
      
      // Prepare specifications as a proper JSON object
      const specifications: ProductSpecifications = {
        ...specs
      };
      
      // Save product to database
      if (isNewProduct) {
        // For new products, don't include the id field - let Supabase generate it
        const { id, ...productWithoutId } = product;
        
        // Ensure required fields are present for insert
        const productToInsert = {
          ...productWithoutId,
          name: product.name || '',
          price: product.price || 0,
          quantity: product.quantity || 0,
          slug: product.slug || '',
          collection_id: product.collection_id || null,
          specifications
        };
        
        const { data: newProduct, error } = await supabase
          .from('products')
          .insert(productToInsert)
          .select()
          .single();
          
        if (error) throw error;
        if (!newProduct) throw new Error('Failed to create product');
          
        // Update the product state with the newly generated ID
        setProduct(prev => ({ ...prev, id: newProduct.id }));
        
        setSuccessMessage('Product created successfully!');
        customToast.success('Product created!');
        
        // Redirect after a short delay
        setTimeout(() => {
          router.push(`/admin/products/${newProduct.id}`);
        }, 1500);
      } else {
        // Ensure required fields are present for update
        const productToUpdate = {
          ...product,
          name: product.name || '',
          price: product.price || 0,
          quantity: product.quantity || 0,
          slug: product.slug || '',
          model: product.model || '',
          collection_id: product.collection_id || null,
          specifications
        };
        
        const { error } = await supabase
          .from('products')
          .update(productToUpdate)
          .eq('id', product.id || '')
          .select()
          .single();
          
        if (error) throw error;
        
        setSuccessMessage('Product updated successfully!');
        customToast.success('Product updated!');
      }
      
      // No need to upload files via Supabase Storage anymore
      // All uploads are handled by Cloudinary widget
      // Just clear the files array
      if (files.length > 0) {
        setFiles([]);
      }
    } catch (err) {
      console.error('Error saving product:', err);
      setError(err instanceof Error ? err.message : 'Failed to save product');
      customToast.error('Failed to save product');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
        <p className="ml-3">Loading product data...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">
            {isNewProduct ? 'Create New Product' : 'Edit Product'}
          </h1>
          <p className="text-gray-600 mt-1">
            {isNewProduct ? 'Add a new product to your store' : 'Update product details'}
          </p>
        </div>
        
        <Link
          href="/admin/products"
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Products
        </Link>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-600 rounded-md">
          {error}
        </div>
      )}
      
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 text-green-600 rounded-md">
          {successMessage}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column - Basic info */}
          <div className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Product Name*
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={product.name || ''}
                onChange={handleNameChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
                required
              />
            </div>
            
            <div>
              <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-1">
                Slug (URL)
              </label>
              <input
                type="text"
                id="slug"
                name="slug"
                value={product.slug || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
              <p className="text-xs text-gray-500 mt-1">
                Auto-generated from name. Only edit if needed.
              </p>
            </div>
            <div>
              <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                Model
              </label>
              <input
                type="text"
                id="model"
                name="model"
                value={product.model || ''}
                onChange={handleInputChange}
                placeholder="e.g. M51980"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            
            <div>
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                Price*
              </label>
              <input
                type="number"
                id="price"
                name="price"
                value={product.price || 0}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
                required
              />
            </div>
            
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                Quantity*
              </label>
              <input
                type="number"
                id="quantity"
                name="quantity"
                value={product.quantity || 0}
                onChange={handleInputChange}
                min="0"
                step="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
                required
              />
            </div>
            
            <div>
              <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category_id"
                name="category_id"
                value={product.category_id || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="mt-2">
              <input
                type="text"
                placeholder="Create new collection..."
                value={newCollectionName}
                onChange={(e) => setNewCollectionName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-md"
              />
              <button
                type="button"
                onClick={handleCreateCollection}
                className="mt-1 px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
              >
                + Add Collection
              </button>
            </div>
            
            <div>
              <label htmlFor="condition_id" className="block text-sm font-medium text-gray-700 mb-1">
                Condition
              </label>
              <select
                id="condition_id"
                name="condition_id"
                value={product.condition_id || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              >
                <option value="">Select a condition</option>
                {conditions.map(condition => (
                  <option key={condition.value} value={condition.value}>
                    {condition.label} - {condition.description}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="collection_id" className="block text-sm font-medium text-gray-700 mb-1">
                Collection
              </label>
              <select
                id="collection_id"
                name="collection_id"
                value={product.collection_id || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              >
                <option value="">Select a collection</option>
                {collections.map(collection => (
                  <option key={collection.id} value={collection.id}>
                    {collection.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          {/* Right column - Description and media */}
          <div className="space-y-6">
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={product.description || ''}
                onChange={handleInputChange}
                rows={5}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Images
              </label>
              
              {selectedImage && (
                <div className="mb-4 w-full aspect-[4/5] relative bg-white overflow-hidden rounded-lg">
                  <Image
                    src={selectedImage.url}
                    alt={selectedImage.alt || 'Selected product image'}
                    fill
                    className="object-contain w-full h-full"
                  />
                </div>
              )}
              <div className="flex flex-wrap gap-3 justify-center">
                {media.map(item => (
                  <div
                    key={item.id}
                    onClick={() => setSelectedImage(item)}
                    className={`cursor-pointer border-2 rounded-md p-1 ${
                      selectedImage?.id === item.id ? 'border-black' : 'border-transparent'
                    }`}
                  >
                    <Image
                      src={item.url}
                      alt={item.alt || 'Product thumbnail'}
                      width={80}
                      height={80}
                      className="object-contain rounded-md"
                    />
                  </div>
                ))}
              </div>
              
              {/* File upload */}
              <div className="mt-2">
                <div className="flex space-x-2">
                  <label className="flex-1 block p-4 border-2 border-dashed border-gray-300 rounded-md text-center cursor-pointer hover:bg-gray-50">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                    <span className="text-gray-600">
                      {files.length > 0 
                        ? `${files.length} file(s) selected` 
                        : 'Click to upload images or drag and drop'}
                    </span>
                  </label>
                  
                  <button
                    type="button"
                    onClick={openCloudinaryWidget}
                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                  >
                    Use Cloudinary
                  </button>
                </div>
                
                {files.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600">Selected files:</p>
                    <ul className="text-xs text-gray-500 mt-1">
                      {files.map((file, index) => (
                        <li key={index}>{file.name}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Specifications Section */}
        <div className="mt-8 bg-white p-6 rounded-lg border border-gray-300">
          <h3 className="text-lg font-semibold mb-4">Specifications</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Color</label>
              <input
                type="text"
                placeholder="e.g. Black, Red, etc."
                value={specs.color}
                onChange={(e) => setSpecs({ ...specs, color: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Material</label>
              <input
                type="text"
                placeholder="e.g. Leather, Canvas, etc."
                value={specs.material}
                onChange={(e) => setSpecs({ ...specs, material: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Size</label>
              <input
                type="text"
                placeholder="e.g. Small, Medium, etc."
                value={specs.size}
                onChange={(e) => setSpecs({ ...specs, size: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Design</label>
              <input
                type="text"
                placeholder="e.g. Monogram, Damier, etc."
                value={specs.design}
                onChange={(e) => setSpecs({ ...specs, design: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Strap</label>
              <input
                type="text"
                placeholder="e.g. Adjustable, Chain, etc."
                value={specs.strap}
                onChange={(e) => setSpecs({ ...specs, strap: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Interior</label>
              <input
                type="text"
                placeholder="e.g. Microfiber, Suede, etc."
                value={specs.interior}
                onChange={(e) => setSpecs({ ...specs, interior: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Condition Detail</label>
              <input
                type="text"
                placeholder="e.g. Like New, Vintage, etc."
                value={specs.condition}
                onChange={(e) => setSpecs({ ...specs, condition: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Rarity</label>
              <input
                type="text"
                placeholder="e.g. Limited Edition, Common, etc."
                value={specs.rarity}
                onChange={(e) => setSpecs({ ...specs, rarity: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Versatility</label>
              <input
                type="text"
                placeholder="e.g. Clutch, shoulder bag, tote, etc."
                value={specs.versatility}
                onChange={(e) => setSpecs({ ...specs, versatility: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
              />
            </div>
          </div>
        </div>
        
        <div className="mt-8 flex justify-end">
          <button
            type="button"
            onClick={() => router.push('/admin/products')}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors mr-3"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={saving}
            className={`px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700 transition-colors ${saving ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {saving ? 'Saving...' : isNewProduct ? 'Create Product' : 'Update Product'}
          </button>
        </div>
      </form>
      
      {/* 3D/AR Model Viewer Modal */}
      {showModelViewer && selectedImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
          <div className="relative bg-white p-4 rounded-lg w-full max-w-2xl shadow-lg">
            {/* Close Button */}
            <button
              onClick={() => setShowModelViewer(false)}
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-800"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <h3 className="text-lg font-medium mb-2">3D/AR Preview</h3>
            
            {/* Model Viewer */}
            <model-viewer
              src={product.model_url || selectedImage.url.replace('/image/upload/', '/image/upload/auto_3d/v1/').replace(/\.\w+$/, '.glb')}
              alt="3D model"
              auto-rotate
              camera-controls
              ar
              ar-modes="scene-viewer webxr quick-look"
              style={{ width: '100%', height: '500px' }}
            ></model-viewer>
            
            <div className="mt-4 text-center text-sm text-gray-500">
              <p>Use your mouse to rotate and zoom. On mobile devices, you can view in AR.</p>
              <p className="mt-2">Note: 3D models are automatically generated from product images.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
