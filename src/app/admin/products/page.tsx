'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Link from 'next/link';
import Image from 'next/image';
import { Database } from '@/lib/database.types';
import { customToast } from '@/components/ui/CustomToast';

type Product = Database['public']['Tables']['products']['Row'] & {
  category?: {
    name: string;
    id: string;
  } | null;
  media?: {
    url: string;
    alt: string | null;
    id: string;
    position: number | null;
  }[] | null;
};

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [categories, setCategories] = useState<{id: string, name: string}[]>([]);
  
  // Fetch products on component mount
  useEffect(() => {
    fetchCategories();
    fetchProducts();
  }, [filterCategory, sortBy, sortDirection]);
  
  // Function to fetch categories
  const fetchCategories = async () => {
    try {
      const supabase = createClientComponentClient<Database>();
      const { data, error } = await supabase
        .from('categories')
        .select('id, name');
      
      if (error) {
        console.error('Error fetching categories:', error);
        return;
      }
      
      setCategories(data || []);
    } catch (err) {
      console.error('Exception fetching categories:', err);
    }
  };
  
  // Function to fetch products from the database
  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const supabase = createClientComponentClient<Database>();
      
      // Start query for products
      let query = supabase
        .from('products')
        .select('*');
      
      // Apply category filter if not 'all'
      if (filterCategory !== 'all') {
        query = query.eq('category_id', filterCategory);
      }
      
      // Apply sorting
      query = query.order(sortBy as any, { ascending: sortDirection === 'asc' });
      
      // Execute query
      const { data: productsData, error: productsError } = await query;
      
      if (productsError) {
        console.error('Error fetching products:', productsError);
        setError('Failed to load products. Please try again.');
        return;
      }
      
      // For each product, fetch its category and media
      const productsWithDetails = await Promise.all(
        (productsData || []).map(async (product) => {
          // Fetch category if category_id exists
          let category = null;
          if (product.category_id) {
            const { data: categoryData } = await supabase
              .from('categories')
              .select('id, name')
              .eq('id', product.category_id)
              .single();
            
            category = categoryData;
          }
          
          // Fetch product media
          const { data: mediaData } = await supabase
            .from('product_media')
            .select('id, url, alt, position')
            .eq('product_id', product.id)
            .order('position', { ascending: true });
          
          return {
            ...product,
            category,
            media: mediaData
          };
        })
      );
      
      setProducts(productsWithDetails);
    } catch (err) {
      console.error('Exception fetching products:', err);
      setError('An unexpected error occurred.');
    } finally {
      setLoading(false);
    }
  };
  
  // Filter products by search query
  const filteredProducts = products.filter(product => 
    product.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Delete a product
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this product?')) {
      return;
    }
    
    try {
      setLoading(true);
      const supabase = createClientComponentClient<Database>();
      
      // First delete associated media
      const { error: mediaError } = await supabase
        .from('product_media')
        .delete()
        .eq('product_id', id);
        
      if (mediaError) {
        console.error('Error deleting product media:', mediaError);
      }
      
      // Then delete the product
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', id);
        
      if (error) {
        throw error;
      }
      
      customToast.success('Product deleted successfully');
      
      // Refresh product list
      fetchProducts();
    } catch (err) {
      console.error('Error deleting product:', err);
      setError('Failed to delete product. Please try again.');
      customToast.error('Failed to delete product');
    } finally {
      setLoading(false);
    }
  };
  
  // Format currency
  const formatCurrency = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  // Handle marking a product as sold out
  const handleMarkSoldOut = async (id: string) => {
    try {
      console.log('Marking product as sold out:', id);
      setLoading(true);
      const supabase = createClientComponentClient<Database>();
 
      // ✅ First: Update the product without select().single()
      const { error: updateError } = await supabase
        .from('products')
        .update({
          status: 'sold_out',
          quantity: 0,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
 
      if (updateError) {
        console.error('Error updating product:', updateError);
        throw updateError;
      }
 
      // ✅ Then: Fetch the updated product
      const { data: updatedProducts, error: fetchError } = await supabase
        .from('products')
        .select('*, category:categories(name, id), media:product_media(url, alt, id, position)')
        .eq('id', id);
 
      if (fetchError || !updatedProducts || updatedProducts.length === 0) {
        throw new Error('Failed to fetch updated product');
      }
 
      const updatedProduct = updatedProducts[0];
 
      // ✅ Update the local state with the updated product
      setProducts((prevProducts) =>
        prevProducts.map((product) =>
          product.id === id
            ? {
                ...product,
                ...updatedProduct,
                status: 'sold_out',
                quantity: 0
              }
            : product
        )
      );
 
      customToast.success('Product marked as sold out');
      await fetchProducts();
    } catch (err) {
      console.error('Error marking product as sold out:', err);
      customToast.error('Failed to mark product as sold out');
    } finally {
      setLoading(false);
    }
  };

  // Handle unmarking a product as sold out
  const handleUnmarkSoldOut = async (id: string) => {
    try {
      setLoading(true);
      const supabase = createClientComponentClient<Database>();
      
      // Update the product status and quantity
      const { error } = await supabase
        .from('products')
        .update({
          status: 'active',
          quantity: 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        throw error;
      }

      const { data: updatedProduct, error: fetchError } = await supabase
        .from('products')
        .select('*, category:categories(name, id), media:product_media(url, alt, id, position)')
        .eq('id', id)
        .single();

      if (fetchError || !updatedProduct) {
        throw new Error('Failed to fetch updated product');
      }
      
      // Update the local state with the updated product
      setProducts(prevProducts => 
        prevProducts.map(product => 
          product.id === id ? {
            ...product,
            ...updatedProduct,
            status: 'active',
            quantity: 1
          } : product
        )
      );
      
      customToast.success('Product unmarked as sold out');
      await fetchProducts();
    } catch (err) {
      console.error('Error unmarking product as sold out:', err);
      customToast.error('Failed to unmark product as sold out');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto">
      <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Products</h1>
          <p className="text-gray-600 mt-1">Manage your product catalog</p>
        </div>
        
        <Link
          href="/admin/products/new"
          className="px-4 py-2 bg-gray-800 text-white text-sm rounded-md hover:bg-gray-700 transition-colors inline-flex items-center"
        >
          <span className="mr-2">+</span> Add New Product
        </Link>
      </div>
      
      {/* Filters and search */}
      <div className="mb-6 bg-white p-4 rounded-lg shadow-sm">
        <div className="flex flex-col md:flex-row md:items-center gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400"
            />
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            
            <select
              value={`${sortBy}-${sortDirection}`}
              onChange={(e) => {
                const [newSortBy, newSortDirection] = e.target.value.split('-');
                setSortBy(newSortBy);
                setSortDirection(newSortDirection);
              }}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="created_at-desc">Newest First</option>
              <option value="created_at-asc">Oldest First</option>
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
              <option value="price-asc">Price (Low to High)</option>
              <option value="price-desc">Price (High to Low)</option>
            </select>
            
            <button
              onClick={() => fetchProducts()}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>
      
      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-600 rounded-md">
          {error}
        </div>
      )}
      
      {/* Products table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-3 text-gray-600">Loading products...</p>
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-600">No products found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredProducts.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0 mr-3">
                          {product.media && product.media.length > 0 ? (
                            <div className="flex space-x-1 overflow-x-auto max-w-[120px]">
                              {product.media.slice(0, 3).map((media) => (
                                <Image
                                  key={media.id}
                                  src={media.url}
                                  alt={media.alt || product.name}
                                  width={40}
                                  height={40}
                                  className="h-10 w-10 rounded-md object-cover"
                                />
                              ))}
                            </div>
                          ) : (
                            <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center text-gray-500">
                              No img
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {product.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {product.slug}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatCurrency(product.price)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {product.category?.name || 'Uncategorized'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(product.created_at || '').toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link
                        href={`/admin/products/${product.id}`}
                        className="text-indigo-600 hover:text-indigo-900 mr-4"
                      >
                        Edit
                      </Link>
                      {product.status === 'sold_out' ? (
                        <button
                          onClick={() => handleUnmarkSoldOut(product.id)}
                          className="text-green-600 hover:text-green-900 mr-4"
                          disabled={loading}
                        >
                          Unmark Sold Out
                        </button>
                      ) : (
                        <button
                          onClick={() => {
                            console.log('Mark Sold Out button clicked for product:', product.id);
                            handleMarkSoldOut(product.id);
                          }}
                          className="text-orange-600 hover:text-orange-900 mr-4"
                          disabled={loading}
                        >
                          Mark Sold Out
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(product.id)}
                        className="text-red-600 hover:text-red-900"
                        disabled={loading}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
