'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { customToast } from '@/components/ui/CustomToast';
import { registerAdmin } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default function AdminRegistration() {
  const router = useRouter();
  const supabase = createClientComponentClient();
  
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState(1); // 1 = register, 2 = enter token
  const [token, setToken] = useState('');
  
  // Check if any admin account exists
  useEffect(() => {
    const checkExistingAdmin = async () => {
      try {
        const { count, error } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .eq('is_admin', true);
          
        if (error) throw error;
        
        if (count && count > 0) {
          // Admin already exists, redirect to login
          customToast.info('Admin account already exists');
          router.push('/auth');
        }
      } catch (error) {
        console.error('Error checking for existing admin:', error);
      }
    };
    
    checkExistingAdmin();
  }, [supabase, router]);
  
  // console.warn('handleAdminRegister function is moved to src/app/actions/handleAdminRegister.ts');

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear previous errors
    setError(null);
    
    // Validation
    if (!name || !email || !password || !confirmPassword) {
      setError('All fields are required');
      return;
    }
    
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    if (password.length < 8) {
      setError('Password must be at least 8 characters');
      return;
    }
    
    try {
      setLoading(true);
      
      // Create the user account
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: name.split(' ')[0],
            last_name: name.split(' ').slice(1).join(' '),
          }
        }
      });
      
      if (authError) throw authError;
      
      if (!authData.user) {
        throw new Error('Failed to create user account');
      }
      
      // Generate a one-time admin token
      const { data: tokenData, error: tokenError } = await supabase
        .rpc('generate_admin_token', { token_expiry_hours: 1 });
        
      if (tokenError) throw tokenError;
      
      if (tokenData.success) {
        setToken(tokenData.token);
        customToast.success('Account created successfully');
        setStep(2); // Move to token step
      } else {
        throw new Error('Failed to generate admin token');
      }
    } catch (error: any) {
      console.error('Sign-up error:', error);
      setError(error.message || 'Failed to create account');
      customToast.error(error.message || 'Failed to create account');
    } finally {
      setLoading(false);
    }
  };
  
  const handleTokenSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) {
      setError('Please enter the token');
      return;
    }
    
    try {
      setLoading(true);
      
      // Use the token to register as admin
      const { data, error } = await supabase
        .rpc('register_admin_with_token', { p_token: token });
        
      if (error) throw error;
      
      if (data.success) {
        customToast.success('Admin account created successfully');
        
        // Delete the admin registration page to prevent further use
        try {
          // This won't actually work in the browser due to security restrictions,
          // but is here to indicate the intention. Server-side logic should handle this.
          // In practice, you'd only allow this route to be accessible if no admin exists yet.
          console.log('In a real implementation, the route would be disabled now');
        } catch (err) {
          console.error('Failed to remove admin register route:', err);
        }
        
        // Redirect to admin dashboard
        router.push('/admin/dashboard');
      } else {
        throw new Error(data.message || 'Failed to register as admin');
      }
    } catch (error: any) {
      console.error('Token submission error:', error);
      setError(error.message || 'Failed to validate token');
      customToast.error(error.message || 'Failed to validate token');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            {step === 1 ? 'Create Admin Account' : 'Confirm Admin Registration'}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {step === 1 
              ? 'This is a one-time setup for your admin account' 
              : 'Enter the token to activate your admin account'}
          </p>
        </div>
        
        {step === 1 ? (
          <form className="mt-8 space-y-6" onSubmit={handleSignUp}>
            {error && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}
            
            <div className="rounded-md shadow-sm -space-y-px">
              <div>
                <label htmlFor="name" className="sr-only">Full Name</label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  autoComplete="name"
                  required
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-[#171717] focus:border-[#171717] focus:z-10 sm:text-sm"
                  placeholder="Full Name"
                />
              </div>
              <div>
                <label htmlFor="email" className="sr-only">Email address</label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#171717] focus:border-[#171717] focus:z-10 sm:text-sm"
                  placeholder="Email address"
                />
              </div>
              <div>
                <label htmlFor="password" className="sr-only">Password</label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#171717] focus:border-[#171717] focus:z-10 sm:text-sm"
                  placeholder="Password"
                />
              </div>
              <div>
                <label htmlFor="confirm-password" className="sr-only">Confirm Password</label>
                <input
                  id="confirm-password"
                  name="confirm-password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-[#171717] focus:border-[#171717] focus:z-10 sm:text-sm"
                  placeholder="Confirm Password"
                />
              </div>
            </div>
            
            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#171717] hover:bg-[#3e3e3e] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#171717] disabled:opacity-50"
              >
                {loading ? 'Creating Account...' : 'Create Admin Account'}
              </button>
            </div>
            
            <div className="text-center">
              <Link href="/auth" className="text-sm text-[#171717] hover:underline">
                Already have an account? Sign in
              </Link>
            </div>
          </form>
        ) : (
          // Token confirmation step
          <form className="mt-8 space-y-6" onSubmit={handleTokenSubmit}>
            {error && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}
            
            <div>
              <p className="text-sm text-gray-700 mb-2">
                Your secure token has been generated. Copy and paste it below to complete your admin registration:
              </p>
              <div className="relative">
                <input
                  type="text"
                  id="admin-token"
                  value={token}
                  readOnly
                  className="block w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717] text-sm bg-gray-50"
                />
                <button
                  type="button"
                  onClick={() => {
                    navigator.clipboard.writeText(token);
                    customToast.success('Token copied to clipboard');
                  }}
                  className="absolute inset-y-0 right-0 px-3 flex items-center text-sm text-[#171717] hover:text-[#3e3e3e]"
                >
                  Copy
                </button>
              </div>
            </div>
            
            <div>
              <label htmlFor="token-confirm" className="block text-sm font-medium text-gray-700 mb-1">
                Confirm token to activate admin privileges
              </label>
              <input
                id="token-confirm"
                type="text"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-[#171717] focus:border-[#171717] sm:text-sm"
                placeholder="Paste your token here"
              />
            </div>
            
            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#171717] hover:bg-[#3e3e3e] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#171717] disabled:opacity-50"
              >
                {loading ? 'Activating Admin Account...' : 'Activate Admin Account'}
              </button>
            </div>
            
            <div className="mt-4 text-center">
              <p className="text-sm text-red-600 font-medium">
                Important: This link will be disabled after successful registration.
              </p>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}