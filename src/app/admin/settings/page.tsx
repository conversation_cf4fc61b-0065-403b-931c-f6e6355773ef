'use client';

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

interface StoreSettings {
  store_name: string;
  contact_email: string;
  shipping_policy: string;
  return_policy: string;
  currency: string;
}

export default function SettingsPage() {
  const supabase = createClientComponentClient();
  const [settings, setSettings] = useState<StoreSettings>({
    store_name: '<PERSON><PERSON>',
    contact_email: '',
    shipping_policy: '',
    return_policy: '',
    currency: 'EUR'
  });
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  useEffect(() => {
    fetchSettings();
  }, []);

  useEffect(() => {
    if (message.text) {
      const timer = setTimeout(() => setMessage({ type: '', text: '' }), 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  const fetchSettings = async () => {
    const { data, error } = await supabase
      .from('store_settings')
      .select('*')
      .limit(1);

    if (error) {
      console.error('Error fetching settings:', error);

      if (error.code === '42P01') {
        setMessage({
          type: 'error',
          text:
            'Store settings table not found. Please create the table in Supabase using the provided SQL.',
        });
      } else {
        setMessage({ type: 'error', text: 'Failed to load settings. Please try again.' });
      }
      return;
    }

    if (!data || data.length === 0) {
      setMessage({
        type: 'info',
        text: 'No store settings found yet. You can save your settings now.',
      });
      return;
    }

    setSettings(data[0]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setMessage({ type: '', text: '' });

    try {
      const { error } = await supabase
        .from('store_settings')
        .upsert(settings);

      if (error) throw error;

      setMessage({ type: 'success', text: 'Settings saved successfully' });
    } catch (error) {
      console.error('Error saving settings:', error);
      setMessage({ type: 'error', text: 'Failed to save settings' });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-heading text-[#171717]">Store Settings</h1>

      <form onSubmit={handleSubmit} className="space-y-6">
        {message.text && (
          <div
            className={`p-4 rounded-lg ${
              message.type === 'success'
                ? 'bg-green-50 text-green-700'
                : 'bg-red-50 text-red-700'
            }`}
          >
            {message.text}
          </div>
        )}

        <div className="bg-white p-6 rounded-lg border border-[rgba(0,0,0,0.1)]">
          <h2 className="text-lg font-heading text-[#171717] mb-4">General Settings</h2>
          <div className="space-y-4">
            <div className="space-y-1">
              <label className="block text-sm font-medium text-[#666666] mb-1">
                Store Name
              </label>
              <input
                type="text"
                value={settings.store_name}
                onChange={(e) => setSettings({ ...settings, store_name: e.target.value })}
                className="w-full px-3 py-2 border border-[rgba(0,0,0,0.1)] rounded-md"
                placeholder="e.g. Maimi Luxury"
                required
              />
            </div>

            <div className="space-y-1">
              <label className="block text-sm font-medium text-[#666666] mb-1">
                Contact Email
              </label>
              <input
                type="email"
                value={settings.contact_email}
                onChange={(e) => setSettings({ ...settings, contact_email: e.target.value })}
                className="w-full px-3 py-2 border border-[rgba(0,0,0,0.1)] rounded-md"
                placeholder="e.g. <EMAIL>"
                required
              />
            </div>

            <div className="space-y-1">
              <label className="block text-sm font-medium text-[#666666] mb-1">
                Currency
              </label>
              <select
                value={settings.currency}
                onChange={(e) => setSettings({ ...settings, currency: e.target.value })}
                className="w-full px-3 py-2 border border-[rgba(0,0,0,0.1)] rounded-md"
                required
              >
                <option value="USD">USD ($)</option>
                <option value="EUR">EUR (€)</option>
                <option value="GBP">GBP (£)</option>
              </select>
            </div>
          </div>
        </div>

        <hr className="my-6 border-gray-200" />

        <div className="bg-white p-6 rounded-lg border border-[rgba(0,0,0,0.1)]">
          <h2 className="text-lg font-heading text-[#171717] mb-4">Policies</h2>
          <div className="space-y-4">
            <div className="space-y-1">
              <label className="block text-sm font-medium text-[#666666] mb-1">
                Shipping Policy
              </label>
              <textarea
                value={settings.shipping_policy}
                onChange={(e) => setSettings({ ...settings, shipping_policy: e.target.value })}
                className="w-full px-3 py-2 border border-[rgba(0,0,0,0.1)] rounded-md"
                rows={4}
                placeholder="e.g. Free shipping on orders over €50"
                required
              />
            </div>

            <div className="space-y-1">
              <label className="block text-sm font-medium text-[#666666] mb-1">
                Return Policy
              </label>
              <textarea
                value={settings.return_policy}
                onChange={(e) => setSettings({ ...settings, return_policy: e.target.value })}
                className="w-full px-3 py-2 border border-[rgba(0,0,0,0.1)] rounded-md"
                rows={4}
                placeholder="e.g. Returns accepted within 30 days"
                required
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSaving}
            className="inline-flex items-center justify-center px-5 py-2 text-sm font-medium text-white bg-[#171717] hover:bg-[#333333] rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#171717] transition disabled:opacity-50"
          >
            {isSaving ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </form>
    </div>
  );
}
