'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { customToast } from '@/components/ui/CustomToast';
import { Trash2, Plus, Edit, X, Save } from 'lucide-react';

interface ShippingMethod {
  id: string;
  name: string;
  description: string | null;
  price: number;
  estimated_days: string | null;
  is_active: boolean;
  created_at: string;
}

export default function ShippingMethodsPage() {
  const supabase = createClientComponentClient();
  
  const [shippingMethods, setShippingMethods] = useState<ShippingMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    estimated_days: '',
    is_active: true
  });
  
  // Fetch shipping methods on component mount
  useEffect(() => {
    fetchShippingMethods();
  }, []);
  
  // Function to fetch shipping methods from the database
  const fetchShippingMethods = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('shipping_methods')
        .select('*')
        .order('created_at', { ascending: false });
        
      if (error) {
        throw error;
      }
      
      setShippingMethods(data || []);
    } catch (err) {
      console.error('Error fetching shipping methods:', err);
      customToast.error('Failed to load shipping methods');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const target = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: target.checked
      });
    } else if (name === 'price') {
      // Ensure price is a number
      setFormData({
        ...formData,
        [name]: parseFloat(value) || 0
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };
  
  // Start editing a shipping method
  const startEditing = (method: ShippingMethod) => {
    setIsEditing(method.id);
    setFormData({
      name: method.name,
      description: method.description || '',
      price: method.price,
      estimated_days: method.estimated_days || '',
      is_active: method.is_active
    });
    setIsCreating(false);
  };
  
  // Cancel editing or creating
  const cancelAction = () => {
    setIsEditing(null);
    setIsCreating(false);
    setFormData({
      name: '',
      description: '',
      price: 0,
      estimated_days: '',
      is_active: true
    });
  };
  
  // Submit form to create or update a shipping method
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name) {
      customToast.error('Method name is required');
      return;
    }
    
    try {
      if (isEditing) {
        // Update existing shipping method
        const { error } = await supabase
          .from('shipping_methods')
          .update({
            name: formData.name,
            description: formData.description || null,
            price: formData.price,
            estimated_days: formData.estimated_days || null,
            is_active: formData.is_active,
            updated_at: new Date().toISOString()
          })
          .eq('id', isEditing);
          
        if (error) throw error;
        
        customToast.success('Shipping method updated successfully');
      } else {
        // Create new shipping method
        const { error } = await supabase
          .from('shipping_methods')
          .insert({
            name: formData.name,
            description: formData.description || null,
            price: formData.price,
            estimated_days: formData.estimated_days || null,
            is_active: formData.is_active
          });
          
        if (error) throw error;
        
        customToast.success('Shipping method created successfully');
      }
      
      // Refresh shipping methods list
      await fetchShippingMethods();
      
      // Reset form
      cancelAction();
    } catch (err) {
      console.error('Error saving shipping method:', err);
      customToast.error('Failed to save shipping method');
    }
  };
  
  // Delete a shipping method
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this shipping method? This action cannot be undone.')) {
      return;
    }
    
    try {
      const { error } = await supabase
        .from('shipping_methods')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
      
      customToast.success('Shipping method deleted successfully');
      await fetchShippingMethods();
    } catch (err) {
      console.error('Error deleting shipping method:', err);
      customToast.error('Failed to delete shipping method');
    }
  };
  
  // Toggle shipping method active status
  const toggleActiveStatus = async (id: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('shipping_methods')
        .update({
          is_active: !currentStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
        
      if (error) throw error;
      
      customToast.success(`Shipping method ${currentStatus ? 'disabled' : 'enabled'}`);
      await fetchShippingMethods();
    } catch (err) {
      console.error('Error updating shipping method status:', err);
      customToast.error('Failed to update shipping method status');
    }
  };
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  return (
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Shipping Methods</h1>
            <p className="text-gray-600 mt-1">Manage shipping options for your store</p>
          </div>
          
          {!isCreating && !isEditing && (
            <button
              onClick={() => {
                setIsCreating(true);
                setIsEditing(null);
                setFormData({
                  name: '',
                  description: '',
                  price: 0,
                  estimated_days: '',
                  is_active: true
                });
              }}
              className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] flex items-center"
            >
              <Plus size={16} className="mr-2" />
              Add Shipping Method
            </button>
          )}
        </div>
        
        {/* Form for creating or editing shipping methods */}
        {(isCreating || isEditing) && (
          <div className="bg-white p-6 rounded-lg shadow-sm mb-6 border border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800">
                {isEditing ? 'Edit Shipping Method' : 'Add New Shipping Method'}
              </h2>
              <button
                onClick={cancelAction}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Method Name*
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                    placeholder="e.g., Standard Shipping"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price (€)*
                  </label>
                  <input
                    type="number"
                    name="price"
                    value={formData.price}
                    onChange={handleInputChange}
                    required
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                    placeholder="0.00"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                  rows={2}
                  placeholder="Brief description of the shipping method"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Estimated Delivery Time
                  </label>
                  <input
                    type="text"
                    name="estimated_days"
                    value={formData.estimated_days}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#171717]"
                    placeholder="e.g., 3-5 business days"
                  />
                </div>
                
                <div className="flex items-center h-full pt-6">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      name="is_active"
                      checked={formData.is_active}
                      onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                      className="h-4 w-4 text-[#171717] focus:ring-[#171717] border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">Active</span>
                  </label>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 pt-2">
                <button
                  type="button"
                  onClick={cancelAction}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] flex items-center"
                >
                  <Save size={16} className="mr-2" />
                  {isEditing ? 'Update Method' : 'Create Method'}
                </button>
              </div>
            </form>
          </div>
        )}
        
        {/* Shipping methods list */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
            {shippingMethods.length === 0 ? (
              <div className="p-6 text-center">
                <p className="text-gray-500">No shipping methods found</p>
                <button
                  onClick={() => {
                    setIsCreating(true);
                    setIsEditing(null);
                  }}
                  className="mt-4 px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] inline-flex items-center"
                >
                  <Plus size={16} className="mr-2" />
                  Add Your First Shipping Method
                </button>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Method
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Delivery Time
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {shippingMethods.map((method) => (
                    <tr key={method.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{method.name}</div>
                        {method.description && (
                          <div className="text-xs text-gray-500 mt-1">{method.description}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{formatCurrency(method.price)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {method.estimated_days || 'Not specified'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            method.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {method.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-3">
                          <button
                            onClick={() => toggleActiveStatus(method.id, method.is_active)}
                            className={`text-sm ${
                              method.is_active ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'
                            }`}
                          >
                            {method.is_active ? 'Disable' : 'Enable'}
                          </button>
                          <button
                            onClick={() => startEditing(method)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(method.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        )}
      </div>
  );
}
