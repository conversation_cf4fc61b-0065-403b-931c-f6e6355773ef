import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import supabaseAdmin from '@/lib/supabaseAdmin';
import { unifiedNotificationService } from '@/lib/unified-notifications';

export async function PATCH(request: Request) {
  try {
    // Verify admin access using session
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please log in' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();
    const { id, status } = body;

    if (!id || !status || !['approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid request body - id and status (approved/rejected) are required' },
        { status: 400 }
      );
    }

    // First, get the current item to check the old status and get user info
    const { data: currentItem, error: fetchError } = await supabaseAdmin
      .from('wardrobe_items')
      .select(`
        *,
        profiles:user_id(
          first_name,
          last_name,
          email,
          phone_number
        )
      `)
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Fetch error:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch bag request' },
        { status: 500 }
      );
    }

    const oldStatus = currentItem.status;

    // Update the wardrobe item status using supabaseAdmin
    const { data: updatedItem, error: updateError } = await supabaseAdmin
      .from('wardrobe_items')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to update bag request status' },
        { status: 500 }
      );
    }

    // Send admin notification for bag request status change (if status actually changed)
    if (oldStatus !== status) {
      try {
        const userProfile = currentItem.profiles;
        const userName = userProfile ? `${userProfile.first_name || ''} ${userProfile.last_name || ''}`.trim() : 'Unknown User';
        const userEmail = userProfile?.email || 'Unknown Email';
        const bagDetails = `${currentItem.brand || 'Unknown Brand'} ${currentItem.name} (${currentItem.category})`;

        // Send admin notification about status change
        await unifiedNotificationService.sendNewBagRequestNotification({
          request_id: id,
          requester_name: userName,
          requester_email: userEmail,
          bag_details: `Status changed from ${oldStatus} to ${status}: ${bagDetails}`
        });
        console.log('📧 Bag request status change notification sent to admin');

        // Send user notification about status change
        if (userEmail !== 'Unknown Email') {
          await unifiedNotificationService.sendBagRequestStatusNotification({
            user_email: userEmail,
            user_name: userName,
            bag_name: currentItem.name,
            old_status: oldStatus || 'pending',
            new_status: status,
            notes: status === 'rejected' ? 'Your request has been reviewed.' : undefined,
            user_phone: userProfile?.phone_number || undefined
          });
          console.log('📧 Bag request status notification sent to user');
        }
      } catch (notificationError) {
        console.error('❌ Error sending bag request status change notification:', notificationError);
        // Don't fail the request if notification fails
      }
    }

    return NextResponse.json(updatedItem);

  } catch (error) {
    console.error('Server error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
