import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/lib/database.types';
import { isUserAdmin } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check if user is admin
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const adminStatus = await isUserAdmin(supabase, session.user.id);
    if (!adminStatus) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get the threshold time (e.g., 24 hours ago)
    const hoursAgo = 24;
    const thresholdTime = new Date();
    thresholdTime.setHours(thresholdTime.getHours() - hoursAgo);

    // Find and update abandoned orders older than threshold
    const { data: abandonedOrders, error: findError } = await supabase
      .from('orders')
      .select('id, created_at')
      .eq('status', 'processing')
      .lt('created_at', thresholdTime.toISOString());

    if (findError) {
      console.error('Error finding abandoned orders:', findError);
      return NextResponse.json(
        { error: 'Failed to find abandoned orders' },
        { status: 500 }
      );
    }

    if (!abandonedOrders || abandonedOrders.length === 0) {
      return NextResponse.json({ 
        message: 'No abandoned orders found',
        cleaned: 0 
      });
    }

    // Update abandoned orders to 'cancelled' status
    const orderIds = abandonedOrders.map(order => order.id);
    const { error: updateError } = await supabase
      .from('orders')
      .update({
        status: 'cancelled',
        payment_status: 'failed',
        updated_at: new Date().toISOString(),
        status_updated_at: new Date().toISOString()
      })
      .in('id', orderIds)
      .eq('status', 'processing'); // Double check status hasn't changed

    if (updateError) {
      console.error('Error updating abandoned orders:', updateError);
      return NextResponse.json(
        { error: 'Failed to update abandoned orders' },
        { status: 500 }
      );
    }

    console.log(`✅ Cleaned up ${orderIds.length} abandoned orders`);

    return NextResponse.json({ 
      message: `Successfully cleaned up abandoned orders`,
      cleaned: orderIds.length,
      orders: orderIds
    });

  } catch (error) {
    console.error('Cleanup abandoned orders error:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup abandoned orders' },
      { status: 500 }
    );
  }
}