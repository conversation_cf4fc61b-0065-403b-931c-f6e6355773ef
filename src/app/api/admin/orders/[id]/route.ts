import { NextResponse } from 'next/server';
import supabaseAdmin from '@/lib/supabaseAdmin';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { unifiedNotificationService } from '@/lib/unified-notifications';

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // DEVELOPMENT BYPASS: Skip all auth if explicitly enabled
    if (process.env.SKIP_AUTH === 'true' || process.env.NODE_ENV === 'development') {
      console.log('🚀 DEVELOPMENT MODE: Skipping all authentication checks');
      
      // Get the new status from the request
      const { status } = await request.json();

      // Update the order with the new status and current timestamp
      const { error } = await supabaseAdmin
        .from('orders')
        .update({
          status,
          updated_at: new Date().toISOString(),
          status_updated_at: new Date().toISOString(),
        })
        .eq('id', params.id);

      if (error) {
        console.error('Database error while updating order:', error);
        return NextResponse.json(
          { error: `Failed to update order status: ${error.message}` },
          { status: 500 }
        );
      }

      console.log('✅ Order status updated successfully for Order ID:', params.id);
      return NextResponse.json({ success: true });
    }
    // Try multiple authentication methods
    let session = null;
    let sessionError = null;
    
    // Method 1: Try Authorization header first
    const authHeader = request.headers.get('authorization');
    if (authHeader?.startsWith('Bearer ')) {
      console.log('🔑 Attempting auth via Authorization header');
      const token = authHeader.replace('Bearer ', '');
      
      try {
        const supabase = createRouteHandlerClient({ cookies: () => cookies() });
        const { data: { user }, error } = await supabase.auth.getUser(token);
        
        if (user && !error) {
          session = { user, access_token: token };
          console.log('✅ Authorization header auth successful');
        }
      } catch (error) {
        console.warn('⚠️ Authorization header auth failed:', error);
      }
    }
    
    // Method 2: Fallback to cookie-based auth
    if (!session) {
      console.log('🍪 Attempting auth via cookies');
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      
      // Add timeout to prevent hanging requests
      const sessionPromise = supabase.auth.getSession();
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Session timeout')), 5000)
      );
      
      try {
        const result = await Promise.race([sessionPromise, timeoutPromise]) as any;
        session = result.data?.session;
        sessionError = result.error;
        
        if (session) {
          console.log('✅ Cookie auth successful');
        }
      } catch (timeoutError: any) {
        if (timeoutError.message === 'Session timeout') {
          console.warn('⚠️ Cookie auth timed out');
          sessionError = null;
        } else {
          sessionError = timeoutError;
        }
      }
    }

    if (sessionError) {
      console.error('Session error in admin orders API:', sessionError);
      return NextResponse.json(
        { error: `Authentication error: ${sessionError.message}` },
        { status: 401 }
      );
    }

    if (!session?.user) {
      console.log('❌ No session found in admin orders API - Order ID:', params.id);
      
      // In development, provide comprehensive debugging
      if (process.env.NODE_ENV === 'development') {
        const cookies = cookieStore.getAll();
        console.log('🍪 Available cookies:', cookies.map(c => `${c.name}=${c.value.substring(0, 20)}...`));
        
        // Check if we have auth cookies but session failed
        const authCookies = cookies.filter(c => 
          c.name.includes('sb-') || c.name.includes('supabase')
        );
        
        console.log('🔐 Auth cookies found:', authCookies.length);
        authCookies.forEach(cookie => {
          console.log(`   - ${cookie.name}: ${cookie.value.length} chars`);
        });
        
        if (authCookies.length > 0) {
          console.log('🚨 Auth cookies present but session failed - BYPASSING in development');
          console.log('🔧 Development bypass: allowing request to proceed');
          
          // Create a mock session for development bypass
          session = {
            user: {
              id: 'dev-bypass-user',
              email: '<EMAIL>'
            }
          };
        } else {
          console.log('❌ No auth cookies found - returning 401');
          return NextResponse.json(
            { error: 'Unauthorized - Please log in' },
            { status: 401 }
          );
        }
      } else {
        return NextResponse.json(
          { error: 'Unauthorized - Please log in' },
          { status: 401 }
        );
      }
    }

    // Only log session info if we have a session
    if (session?.user) {
      console.log('✅ Session found for user:', session.user.email, 'Order ID:', params.id);
    } else {
      console.log('⚠️ No session but continuing with development bypass for Order ID:', params.id);
    }

    // Skip admin check in development if NEXT_PUBLIC_SKIP_ADMIN_CHECK is true
    const skipAdminCheck = process.env.NEXT_PUBLIC_SKIP_ADMIN_CHECK === 'true' || process.env.NODE_ENV === 'development';
    
    if (!skipAdminCheck && session?.user) {
      // Check if user is admin (only if we have a session)
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('is_admin')
        .eq('id', session.user.id)
        .single();

      if (profileError) {
        console.error('Profile error in admin orders API:', profileError);
        return NextResponse.json(
          { error: `Failed to verify admin status: ${profileError.message}` },
          { status: 500 }
        );
      }

      if (!profile?.is_admin) {
        console.log('User is not admin:', session.user.email);
        return NextResponse.json(
          { error: 'Unauthorized - Admin access required' },
          { status: 403 }
        );
      }

      console.log('Admin access verified for user:', session.user.email);
    } else if (skipAdminCheck) {
      console.log('Admin check skipped in development mode');
    }

    // Get the new status from the request
    const { status } = await request.json();

    // First, fetch the current order to get the old status and customer info
    const { data: currentOrder, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        status, 
        order_number, 
        total_amount, 
        customer_email,
        user_id,
        profiles:user_id(phone_number)
      `)
      .eq('id', params.id)
      .single();

    if (fetchError) {
      console.error('Error fetching current order:', fetchError);
      return NextResponse.json(
        { error: `Failed to fetch order: ${fetchError.message}` },
        { status: 500 }
      );
    }

    const oldStatus = currentOrder.status;

    // Update the order with the new status and current timestamp
    const { error } = await supabaseAdmin
      .from('orders')
      .update({
        status,
        updated_at: new Date().toISOString(),
        status_updated_at: new Date().toISOString(),
      })
      .eq('id', params.id);

    if (error) {
      console.error('Database error while updating order:', error);
      return NextResponse.json(
        { error: `Failed to update order status: ${error.message}` },
        { status: 500 }
      );
    }

    // Send notifications for order status change (if status actually changed)
    if (oldStatus !== status) {
      try {
        // Send admin notification
        await unifiedNotificationService.sendOrderStatusChangeNotification({
          order_id: params.id,
          order_number: currentOrder.order_number || params.id,
          old_status: oldStatus || 'unknown',
          new_status: status,
          total_amount: currentOrder.total_amount,
          customer_email: currentOrder.customer_email || 'Unknown'
        });
        console.log('📧 Order status change notification sent to admin');

        // Send user notification if customer email is available
        if (currentOrder.customer_email) {
          await unifiedNotificationService.sendOrderStatusNotification({
            user_email: currentOrder.customer_email,
            user_name: 'Customer', // Could be enhanced with actual name
            order_number: currentOrder.order_number || params.id,
            old_status: oldStatus || 'unknown',
            new_status: status,
            user_phone: currentOrder.profiles?.phone_number || undefined
          });
          console.log('📧 Order status notification sent to customer');
        }
      } catch (notificationError) {
        console.error('❌ Error sending order status notifications:', notificationError);
        // Don't fail the request if notification fails
      }
    }

    console.log('✅ Order status updated successfully for Order ID:', params.id);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Server error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin access
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Session error in admin orders GET API:', sessionError);
      return NextResponse.json(
        { error: `Authentication error: ${sessionError.message}` },
        { status: 401 }
      );
    }

    if (!session?.user) {
      console.log('No session found in admin orders GET API');
      return NextResponse.json(
        { error: 'Unauthorized - Please log in' },
        { status: 401 }
      );
    }

    console.log('GET Session found for user:', session.user.email);

    // Check if user is admin
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    // Skip admin check in development if NEXT_PUBLIC_SKIP_ADMIN_CHECK is true
    const skipAdminCheck = process.env.NEXT_PUBLIC_SKIP_ADMIN_CHECK === 'true' || process.env.NODE_ENV === 'development';
    
    if (!skipAdminCheck && !profile?.is_admin) {
      console.log('User is not admin in GET:', session.user.email);
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    console.log('GET Admin access verified for user:', session.user.email);

    // Fetch order with items and customer profile
    const { data: order, error } = await supabaseAdmin
      .from('orders')
      .select(`
        *,
        order_items(*),
        profiles(id, full_name, email),
        shipping_addresses(id, name, street, city, state, country, postal_code)
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch order' },
        { status: 500 }
      );
    }

    return NextResponse.json({ order });

  } catch (error) {
    console.error('Server error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}