import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import supabaseAdmin from '@/lib/supabaseAdmin';

export async function GET(request: Request) {
  try {
    // Verify admin access using session
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please log in' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    // Skip admin check in development if NEXT_PUBLIC_SKIP_ADMIN_CHECK is true
    const skipAdminCheck = process.env.NEXT_PUBLIC_SKIP_ADMIN_CHECK === 'true' || process.env.NODE_ENV === 'development';
    
    if (!skipAdminCheck && !profile?.is_admin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    // Get URL parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    // Build query with correct relations
    let query = supabaseAdmin
      .from('orders')
      .select(`
        *,
        profiles!user_id (
          id,
          email,
          first_name,
          last_name,
          street_address,
          city
        ),
        shipping_addresses!shipping_address_id (
          id,
          name,
          street,
          city,
          state,
          country,
          postal_code
        ),
        order_items (
          id,
          quantity,
          product:products (
            id,
            name,
            price,
            product_media (
              url,
              is_main
            )
          )
        )
      `, { count: 'exact' });

    if (status) {
      query = query.eq('status', status);
    }
    if (search) {
      query = query.or(`order_number.ilike.%${search}%,customer_email.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(from, to);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch orders' },
        { status: 500 }
      );
    }

    // Transform orders with proper null checks and better customer name handling
    const transformedOrders = data?.map((order: any) => {
      // Try to get customer name from profile, then from email, then fallback to 'Guest'
      let customerName = 'Guest';
      let customerEmail = order.customer_email || 'No email';

      if (order.profiles?.first_name) {
        // Profile data available - use it
        customerName = `${order.profiles.first_name} ${order.profiles.last_name || ''}`.trim();
        customerEmail = order.profiles.email || order.customer_email || 'No email';
      } else if (order.customer_email) {
        // No profile but we have customer email - extract name from email
        const emailParts = order.customer_email.split('@');
        if (emailParts[0]) {
          // Use the part before @ as customer name, capitalize first letter
          customerName = emailParts[0].charAt(0).toUpperCase() + emailParts[0].slice(1);
        }
        customerEmail = order.customer_email;
      }

      return {
        ...order,
        customer_name: customerName,
        customer_email: customerEmail,
        shipping_address: order.shipping_addresses
          ? `${order.shipping_addresses.street || ''}, ${order.shipping_addresses.city || ''}`
          : 'No address',
        items: order.order_items?.map((item: any) => ({
          id: item.id,
          quantity: item.quantity,
          product_name: item.product?.name || 'Unknown Product',
          product_price: item.product?.price || 0,
          product_image: item.product?.product_media?.find((m: any) => m.is_main)?.url || null
        })) || []
      };
    }) || [];

    return NextResponse.json({
      orders: transformedOrders,
      total: count || 0,
      page,
      limit
    });

  } catch (error) {
    console.error('Server error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: Request) {
  try {
    console.log('Starting PATCH request...');
    
    // Get the order ID from the URL
    const { pathname } = new URL(request.url);
    const orderId = pathname.split('/').pop();
    
    console.log('Order ID from URL:', orderId);
    
    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Verify admin access using session
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Session error:', sessionError);
      return NextResponse.json(
        { error: `Authentication error: ${sessionError.message}` },
        { status: 401 }
      );
    }

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please log in' },
        { status: 401 }
      );
    }

    console.log('User authenticated:', session.user.id);

    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (profileError) {
      console.error('Profile error:', profileError);
      return NextResponse.json(
        { error: `Failed to verify admin status: ${profileError.message}` },
        { status: 500 }
      );
    }

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    console.log('Admin status verified');

    // Get request body
    const body = await request.json();
    console.log('Request body:', body);
    
    // Validate status value
    if (!body.status || !['pending', 'paid', 'processing', 'shipped', 'delivered', 'canceled', 'refunded', 'failed'].includes(body.status)) {
      return NextResponse.json(
        { error: 'Invalid status value' },
        { status: 400 }
      );
    }

    // First check if the order exists
    const { data: existingOrder, error: checkError } = await supabaseAdmin
      .from('orders')
      .select('id, status')
      .eq('id', orderId)
      .single();

    if (checkError) {
      console.error('Order check error:', checkError);
      return NextResponse.json(
        { error: `Failed to find order: ${checkError.message}` },
        { status: 404 }
      );
    }

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    console.log('Existing order found:', existingOrder);

    // Update the order
    const updateData = {
      status: body.status,
      status_updated_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('Attempting to update order with data:', updateData);

    // Update the order using supabaseAdmin
    const { error: updateError } = await supabaseAdmin
      .from('orders')
      .update(updateData)
      .eq('id', orderId);

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { error: `Failed to update order: ${updateError.message}` },
        { status: 500 }
      );
    }

    // Fetch the updated order with all necessary relations
    const { data: updatedOrder, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        *,
        profiles!user_id (
          id,
          email,
          first_name,
          last_name
        ),
        shipping_addresses!shipping_address_id (
          id,
          name,
          street,
          city,
          state,
          country,
          postal_code
        ),
        order_items (
          id,
          quantity,
          price,
          product:products (
            id,
            name,
            product_media (
              url,
              is_main
            )
          )
        )
      `)
      .eq('id', orderId)
      .single();

    if (fetchError) {
      console.error('Error fetching updated order:', fetchError);
      return NextResponse.json(
        { error: 'Order updated but failed to fetch updated data' },
        { status: 200 }
      );
    }

    console.log('Order successfully updated and fetched:', updatedOrder);

    return NextResponse.json({
      message: 'Order updated successfully',
      order: updatedOrder
    });

  } catch (error: any) {
    console.error('Server error:', error);
    return NextResponse.json(
      { error: `Internal server error: ${error.message}` },
      { status: 500 }
    );
  }
}