import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import supabaseAdmin from '@/lib/supabaseAdmin';

export async function POST(request: Request) {
  try {
    // Verify admin access
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please log in' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    console.log('Setting up email marketing tables...');

    // Check if tables already exist
    try {
      const { data: campaignsCheck } = await (supabaseAdmin as any)
        .from('email_campaigns')
        .select('id')
        .limit(1);
      
      console.log('Email campaigns table already exists');
      
      return NextResponse.json({ 
        success: true, 
        message: 'Email marketing tables already exist',
        alreadyExists: true
      });
    } catch (error) {
      console.log('Email campaigns table does not exist, will create');
    }

    // Since we can't execute DDL through Supabase client directly,
    // let's add the marketing columns to profiles table via an update
    try {
      console.log('Adding marketing columns to profiles table...');
      
      // Try to query the new columns to see if they exist
      const { data: profileTest } = await supabaseAdmin
        .from('profiles')
        .select('marketing_emails_enabled, newsletter_subscribed')
        .limit(1);
      
      console.log('Marketing columns already exist in profiles table');
      
    } catch (error) {
      console.log('Marketing columns do not exist in profiles table');
      // We would need to add these via direct SQL execution or Supabase dashboard
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Email marketing setup initiated. Please run the migration manually in Supabase dashboard.',
      instructions: [
        '1. Go to Supabase Dashboard -> SQL Editor',
        '2. Run the migration file: 20250720_create_email_marketing.sql',
        '3. This will create email_campaigns and campaign_recipients tables',
        '4. It will also add marketing_emails_enabled and newsletter_subscribed columns to profiles'
      ]
    });

  } catch (error) {
    console.error('Server error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}