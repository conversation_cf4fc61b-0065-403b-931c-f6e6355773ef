import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import supabaseAdmin from '@/lib/supabaseAdmin';
import { unifiedNotificationService } from '@/lib/unified-notifications';

export async function GET() {
  try {
    // Verify admin access
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please log in' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    // Get admin notification setup status
    const { data: adminUsers, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('id, email, phone_number, first_name, last_name, is_admin')
      .eq('is_admin', true);

    if (profileError) {
      console.error('Error fetching admin profiles:', profileError);
      return NextResponse.json(
        { error: 'Failed to fetch admin profiles' },
        { status: 500 }
      );
    }

    const setup = {
      adminUsers: adminUsers || [],
      emailConfigured: !!process.env.RESEND_API_KEY,
      smsConfigured: !!(process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN && process.env.TWILIO_PHONE_NUMBER),
      whatsappConfigured: !!(process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN),
      environmentVariables: {
        RESEND_API_KEY: !!process.env.RESEND_API_KEY,
        TWILIO_ACCOUNT_SID: !!process.env.TWILIO_ACCOUNT_SID,
        TWILIO_AUTH_TOKEN: !!process.env.TWILIO_AUTH_TOKEN,
        TWILIO_PHONE_NUMBER: !!process.env.TWILIO_PHONE_NUMBER
      }
    };

    return NextResponse.json(setup);

  } catch (error) {
    console.error('Setup check error:', error);
    return NextResponse.json(
      { error: 'Failed to check notification setup' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Verify admin access
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please log in' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    const { phone_number } = await request.json();

    // Update the admin's phone number
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({ 
        phone_number,
        updated_at: new Date().toISOString()
      })
      .eq('id', session.user.id);

    if (updateError) {
      console.error('Error updating admin phone number:', updateError);
      return NextResponse.json(
        { error: 'Failed to update phone number' },
        { status: 500 }
      );
    }

    // Send a test notification
    try {
      await unifiedNotificationService.sendSystemAlertNotification({
        alert_type: 'Setup Test',
        message: 'Admin notification system has been configured successfully!',
        severity: 'medium',
        details: { timestamp: new Date().toISOString() }
      });
    } catch (testError) {
      console.error('Test notification failed:', testError);
      // Don't fail the update if test notification fails
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Phone number updated and test notification sent' 
    });

  } catch (error) {
    console.error('Setup update error:', error);
    return NextResponse.json(
      { error: 'Failed to update notification setup' },
      { status: 500 }
    );
  }
}