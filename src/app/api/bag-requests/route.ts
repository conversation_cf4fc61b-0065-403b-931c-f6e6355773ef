import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/lib/database.types';
import { unifiedNotificationService } from '@/lib/unified-notifications';

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, category, color, brand, notes, priority, image_url } = body;

    if (!name || !category) {
      return NextResponse.json(
        { error: 'Name and category are required' },
        { status: 400 }
      );
    }

    // Insert the wardrobe item
    const { data: insertedItem, error: insertError } = await supabase
      .from('wardrobe_items')
      .insert({
        user_id: session.user.id,
        name,
        category,
        color,
        brand,
        notes,
        priority,
        status: 'pending',
        image_url
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting wardrobe item:', insertError);
      return NextResponse.json(
        { error: 'Failed to create bag request' },
        { status: 500 }
      );
    }

    // Get user details for notification
    try {
      const { data: userProfile, error: profileError } = await supabase
        .from('profiles')
        .select('first_name, last_name, email')
        .eq('id', session.user.id)
        .single();

      if (profileError) {
        console.error('Error fetching user profile for notification:', profileError);
      }

      // Send admin notification for new bag request
      const userName = userProfile ? `${userProfile.first_name || ''} ${userProfile.last_name || ''}`.trim() : 'Unknown User';
      const userEmail = userProfile?.email || 'Unknown Email';
      const bagDetails = `${brand || 'Unknown Brand'} ${name} (${category}) - Priority: ${priority}`;

      console.log('Sending bag request notification:', {
        userName,
        userEmail,
        bagDetails
      });

      await unifiedNotificationService.sendNewBagRequestNotification({
        request_id: insertedItem.id,
        requester_name: userName,
        requester_email: userEmail,
        bag_details: bagDetails
      });

      console.log('Bag request notification sent successfully');
    } catch (notificationError) {
      console.error('Error sending bag request notification:', notificationError);
      // Don't fail the request if notification fails
    }

    return NextResponse.json({ 
      success: true, 
      data: insertedItem 
    });

  } catch (error) {
    console.error('Bag request error:', error);
    return NextResponse.json(
      { error: 'Failed to create bag request' },
      { status: 500 }
    );
  }
}