import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// Use environment variables for Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.NEXT_SUPABASE_SERVICE_ROLE_KEY;

// Helper function to check if the user is an admin
async function isAdmin(supabase: any) {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log('No session found in isAdmin check');
      return false;
    }
    
    // Check admin status through is_admin flag in profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return false;
    }

    return profile?.is_admin === true;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// Create a slug from a name
function createSlug(name: string) {
  return name
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-');
}

// GET categories
export async function GET(request: NextRequest) {
  try {
    // Use hardcoded categories based on brand names instead of database
    const hardcodedCategories = [
      {
        id: '1',
        name: 'Louis Vuitton',
        description: 'Explore our curated collection of authentic Louis Vuitton pieces',
        slug: 'louis-vuitton',
        image_url: '/images/products/LV 1/DSC01227.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Gucci',
        description: 'Discover our selection of authentic Gucci items',
        slug: 'gucci',
        image_url: '/images/products/Gucci/DSC01296.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Fendi',
        description: 'Browse our collection of authentic Fendi pieces',
        slug: 'fendi',
        image_url: '/images/products/Fendi/DSC01359.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
    
    console.log(`[categories/GET] Successfully returned ${hardcodedCategories.length} hardcoded categories`);
    return NextResponse.json(hardcodedCategories);
  } catch (error) {
    console.error('[categories/GET] Unexpected error:', error);
    return NextResponse.json(
      { message: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

// POST new category
export async function POST(request: NextRequest) {
  try {
    // Check authentication - always required for security
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const adminStatus = await isAdmin(supabase);
    if (!adminStatus) {
      return NextResponse.json(
        { error: 'Unauthorized. Only admins can create categories.' },
        { status: 403 }
      );
    }
    
    // Parse request body
    const { name, slug: providedSlug, parent_id } = await request.json();
    
    if (!name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      );
    }
    
    // Create slug if not provided
    const slug = providedSlug || createSlug(name);
    
    // First, try with the regular authenticated client
    let result = await supabase
      .from('categories')
      .insert([{ name, slug, parent_id }])
      .select()
      .single();
      
    // If we got a permission error, but the user is an admin according to our check,
    // fall back to using the service role client
    if (result.error && result.error.message.includes('permission denied')) {
      console.log('Permission denied with regular client. Falling back to service role for admin.');
      
      // Validate required environment variables
      if (!supabaseUrl || !supabaseServiceKey) {
        return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
      }
      
      // Create a service role client as fallback
      const serviceClient = createClient(supabaseUrl, supabaseServiceKey);
      
      // Get the current user's ID from the session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        return NextResponse.json({ error: 'No active session' }, { status: 401 });
      }
      
      // Verify again that this user is an admin in profiles
      const { data: profile } = await serviceClient
        .from('profiles')
        .select('role')
        .eq('id', session.user.id)
        .single();
        
      if (profile?.role !== 'admin') {
        return NextResponse.json({ error: 'Not an admin' }, { status: 403 });
      }
      
      // Use service client to insert the category
      result = await serviceClient
        .from('categories')
        .insert([{ name, slug, parent_id }])
        .select()
        .single();
    }
    
    if (result.error) {
      console.error('Error creating category:', result.error);
      return NextResponse.json({ error: result.error.message }, { status: 500 });
    }
    
    return NextResponse.json(result.data);
  } catch (error) {
    console.error('Error in POST category:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
}

// PUT update category
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Skip admin check if specified in environment
    const skipAdminCheck = process.env.NEXT_PUBLIC_SKIP_ADMIN_CHECK === 'true';
    if (!skipAdminCheck) {
      const adminStatus = await isAdmin(supabase);
      if (!adminStatus) {
        return NextResponse.json(
          { error: 'Unauthorized. Only admins can update categories.' },
          { status: 403 }
        );
      }
    }
    
    // Parse request body
    const { id, name, slug: providedSlug, parent_id } = await request.json();
    
    if (!id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      );
    }
    
    if (!name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      );
    }
    
    // Create slug if not provided
    const slug = providedSlug || createSlug(name);
    
    // Update category
    const { data, error } = await supabase
      .from('categories')
      .update({ name, slug, parent_id })
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      console.error('Error updating category:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT category:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
}

// DELETE category
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Skip admin check if specified in environment
    const skipAdminCheck = process.env.NEXT_PUBLIC_SKIP_ADMIN_CHECK === 'true';
    if (!skipAdminCheck) {
      const adminStatus = await isAdmin(supabase);
      if (!adminStatus) {
        return NextResponse.json(
          { error: 'Unauthorized. Only admins can delete categories.' },
          { status: 403 }
        );
      }
    }
    
    // Get category ID from URL or request body
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    
    if (!id) {
      const body = await request.json().catch(() => ({}));
      if (!body.id) {
        return NextResponse.json(
          { error: 'Category ID is required' },
          { status: 400 }
        );
      }
    }
    
    // Check if any products are using this category
    const { count, error: countError } = await supabase
      .from('products')
      .select('id', { count: 'exact', head: true })
      .eq('category', id);
      
    if (countError) {
      return NextResponse.json({ error: countError.message }, { status: 500 });
    }
    
    if (count && count > 0) {
      return NextResponse.json(
        { 
          error: `Cannot delete category because it is being used by ${count} products. Please reassign these products first.`,
          productsUsingCount: count
        }, 
        { status: 400 }
      );
    }
    
    // Delete category
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);
      
    if (error) {
      console.error('Error deleting category:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE category:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
} 