import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/lib/database.types';

// Helper function to check admin status
async function isAdmin(supabase: any): Promise<boolean> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return false;
    }
    
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();
    
    return profile?.is_admin === true;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// Validate discount code data
function validateDiscountCodeData(data: any) {
  const errors: string[] = [];

  if (!data.code || typeof data.code !== 'string' || data.code.trim().length === 0) {
    errors.push('Code is required and must be a non-empty string');
  }

  if (!data.discount_type || !['percentage', 'fixed_amount'].includes(data.discount_type)) {
    errors.push('discount_type must be either "percentage" or "fixed_amount"');
  }

  // Database constraint: discount_value > 0 (cannot be 0 or negative)
  if (data.discount_value === undefined || data.discount_value === null || typeof data.discount_value !== 'number' || data.discount_value <= 0) {
    errors.push('discount_value must be a positive number greater than 0');
  }

  if (data.discount_type === 'percentage' && data.discount_value > 100) {
    errors.push('Percentage discount cannot exceed 100%');
  }

  // minimum_order_amount must be a non-negative number (defaults to 0 in database)
  if (data.minimum_order_amount !== undefined && data.minimum_order_amount !== null &&
      (typeof data.minimum_order_amount !== 'number' || data.minimum_order_amount < 0)) {
    errors.push('minimum_order_amount must be a non-negative number');
  }

  // Fix: Handle usage_limit properly - convert empty strings to null and validate numbers
  if (data.usage_limit !== undefined && data.usage_limit !== null && data.usage_limit !== '') {
    const usageLimit = typeof data.usage_limit === 'string' ? parseInt(data.usage_limit) : data.usage_limit;
    if (typeof usageLimit !== 'number' || isNaN(usageLimit) || usageLimit <= 0) {
      errors.push('usage_limit must be a positive number');
    }
  }

  return errors;
}

// GET - Fetch discount codes (admin only)
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check admin status
    if (!(await isAdmin(supabase))) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }
    
    const url = new URL(request.url);
    const includeAnalytics = url.searchParams.get('analytics') === 'true';
    
    if (includeAnalytics) {
      // Fetch analytics data
      const { data, error } = await supabase
        .from('discount_analytics')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching discount analytics:', error);
        return NextResponse.json(
          { error: 'Failed to fetch discount analytics' },
          { status: 500 }
        );
      }
      
      // Map database columns to frontend expected format
      const mappedData = data?.map(item => ({
        ...item,
        discount_type: item.type, // Map database 'type' to frontend 'discount_type'
        discount_value: item.value, // Map database 'value' to frontend 'discount_value'
        used_count: item.usage_count || 0, // Database column is 'usage_count', frontend expects 'used_count'
        total_discount_given: item.total_discount_given || 0,
        usage_percentage: item.usage_limit ? ((item.usage_count || 0) / item.usage_limit) * 100 : 0
      }));
      
      return NextResponse.json(mappedData);
    } else {
      // Fetch basic discount codes
      const { data, error } = await supabase
        .from('discount_codes')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching discount codes:', error);
        return NextResponse.json(
          { error: 'Failed to fetch discount codes' },
          { status: 500 }
        );
      }
      
      // Map database columns to frontend expected format
      const mappedData = data?.map(item => ({
        ...item,
        discount_type: item.type, // Map database 'type' to frontend 'discount_type'
        discount_value: item.value, // Map database 'value' to frontend 'discount_value'
        used_count: item.usage_count || 0 // Database column is 'usage_count', frontend expects 'used_count'
      }));
      
      return NextResponse.json(mappedData);
    }
  } catch (error) {
    console.error('Unexpected error in GET /discount-codes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new discount code (admin only)
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check admin status
    if (!(await isAdmin(supabase))) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }
    
    const body = await request.json();
    
    // Validate input data
    const validationErrors = validateDiscountCodeData(body);
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { status: 400 }
      );
    }
    
    // Get current user
    const { data: { session } } = await supabase.auth.getSession();
    
    // Prepare discount code data using actual database column names from schema
    const discountCodeData = {
      code: body.code.toUpperCase().trim(),
      description: body.description || null,
      type: body.discount_type, // Database column is 'type', not 'discount_type'
      value: body.discount_value, // Database column is 'value', not 'discount_value'
      minimum_order_amount: body.minimum_order_amount || 0,
      // Handle empty strings for optional numeric fields
      maximum_discount_amount: body.maximum_discount_amount && body.maximum_discount_amount !== ''
        ? parseFloat(body.maximum_discount_amount)
        : null,
      usage_limit: body.usage_limit && body.usage_limit !== ''
        ? parseInt(body.usage_limit)
        : null,
      // Handle date formatting - ensure ISO format for database
      valid_from: body.valid_from ? new Date(body.valid_from).toISOString() : new Date().toISOString(),
      valid_until: body.valid_until && body.valid_until !== ''
        ? new Date(body.valid_until).toISOString()
        : null,
      is_active: body.is_active !== false, // Default to true
      created_by: session?.user.id,
    };
    
    // Insert into database
    const { data, error } = await supabase
      .from('discount_codes')
      .insert(discountCodeData)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating discount code:', error);
      
      // Handle unique constraint violation
      if (error.code === '23505') {
        return NextResponse.json(
          { error: 'A discount code with this code already exists' },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to create discount code' },
        { status: 500 }
      );
    }
    
    // Map database columns to frontend expected format
    const mappedData = {
      ...data,
      discount_type: data.type, // Map database 'type' to frontend 'discount_type'
      discount_value: data.value, // Map database 'value' to frontend 'discount_value'
      used_count: data.usage_count || 0 // Database column is 'usage_count', frontend expects 'used_count'
    };
    
    return NextResponse.json(mappedData, { status: 201 });
  } catch (error) {
    console.error('Unexpected error in POST /discount-codes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update discount code (admin only)
export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check admin status
    if (!(await isAdmin(supabase))) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }
    
    const body = await request.json();
    const { id, ...updateData } = body;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Discount code ID is required' },
        { status: 400 }
      );
    }
    
    // Validate input data if provided
    if (Object.keys(updateData).length > 0) {
      const validationErrors = validateDiscountCodeData({ ...updateData, code: updateData.code || 'TEMP' });
      if (validationErrors.length > 0) {
        const filteredErrors = validationErrors.filter(error => 
          !error.includes('Code is required') || updateData.code
        );
        if (filteredErrors.length > 0) {
          return NextResponse.json(
            { error: 'Validation failed', details: filteredErrors },
            { status: 400 }
          );
        }
      }
    }
    
    // Prepare update data (map frontend fields to database columns)
    const cleanedUpdateData = { ...updateData };
    if (cleanedUpdateData.code) {
      cleanedUpdateData.code = cleanedUpdateData.code.toUpperCase().trim();
    }
    
    // Map frontend field names to database column names
    if (cleanedUpdateData.discount_type) {
      cleanedUpdateData.type = cleanedUpdateData.discount_type;
      delete cleanedUpdateData.discount_type;
    }

    if (cleanedUpdateData.discount_value !== undefined) {
      cleanedUpdateData.value = cleanedUpdateData.discount_value;
      delete cleanedUpdateData.discount_value;
    }

    // Ensure proper data types for optional fields
    if (cleanedUpdateData.maximum_discount_amount === '') {
      cleanedUpdateData.maximum_discount_amount = null;
    } else if (cleanedUpdateData.maximum_discount_amount) {
      cleanedUpdateData.maximum_discount_amount = parseFloat(cleanedUpdateData.maximum_discount_amount);
    }

    if (cleanedUpdateData.usage_limit === '') {
      cleanedUpdateData.usage_limit = null;
    } else if (cleanedUpdateData.usage_limit) {
      cleanedUpdateData.usage_limit = parseInt(cleanedUpdateData.usage_limit);
    }
    
    // Update discount code
    const { data, error } = await supabase
      .from('discount_codes')
      .update(cleanedUpdateData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating discount code:', error);
      
      if (error.code === '23505') {
        return NextResponse.json(
          { error: 'A discount code with this code already exists' },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to update discount code' },
        { status: 500 }
      );
    }
    
    // Map database columns to frontend expected format
    const mappedData = {
      ...data,
      discount_type: data.type, // Map database 'type' to frontend 'discount_type'
      discount_value: data.value, // Map database 'value' to frontend 'discount_value'
      used_count: data.usage_count || 0 // Database column is 'usage_count', frontend expects 'used_count'
    };
    
    return NextResponse.json(mappedData);
  } catch (error) {
    console.error('Unexpected error in PUT /discount-codes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete discount code (admin only)
export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check admin status
    if (!(await isAdmin(supabase))) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }
    
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Discount code ID is required' },
        { status: 400 }
      );
    }
    
    // Delete discount code
    const { error } = await supabase
      .from('discount_codes')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error('Error deleting discount code:', error);
      return NextResponse.json(
        { error: 'Failed to delete discount code' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ message: 'Discount code deleted successfully' });
  } catch (error) {
    console.error('Unexpected error in DELETE /discount-codes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}