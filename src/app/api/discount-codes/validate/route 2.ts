import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/lib/database.types';

// POST - Validate discount code for checkout
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    const body = await request.json();
    const { code, orderTotal, customerEmail, orderItems = [] } = body;
    
    // Validate required fields
    if (!code || typeof code !== 'string') {
      return NextResponse.json(
        { error: 'Discount code is required' },
        { status: 400 }
      );
    }
    
    if (!orderTotal || typeof orderTotal !== 'number' || orderTotal <= 0) {
      return NextResponse.json(
        { error: 'Valid order total is required' },
        { status: 400 }
      );
    }
    
    // Get current user if authenticated
    const { data: { session } } = await supabase.auth.getSession();
    const userId = session?.user?.id || null;
    
    // Validate discount code using the database function
    const { data, error } = await supabase.rpc('validate_discount_code', {
      p_code: code.toUpperCase().trim(),
      p_order_total: orderTotal,
      p_user_id: userId || undefined
    });
    
    if (error) {
      console.error('Error validating discount code:', error);
      return NextResponse.json(
        { error: 'Failed to validate discount code' },
        { status: 500 }
      );
    }
    
    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'Invalid response from discount validation' },
        { status: 500 }
      );
    }
    
    const result = data[0];
    
    if (!result.is_valid) {
      return NextResponse.json(
        {
          valid: false,
          error: result.message,
          discountAmount: 0
        },
        { status: 200 }
      );
    }
    
    // Return successful validation
    return NextResponse.json({
      valid: true,
      discountId: result.discount_id,
      discountAmount: result.discount_amount,
      finalTotal: Math.max(0, orderTotal - result.discount_amount),
      savings: result.discount_amount
    });
    
  } catch (error) {
    console.error('Unexpected error in discount code validation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - Get discount code details (for public codes only)
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });

    const url = new URL(request.url);
    const code = url.searchParams.get('code');
    const orderTotal = parseFloat(url.searchParams.get('order_total') || '0');

    if (!code) {
      return NextResponse.json(
        { error_message: 'Discount code is required', is_valid: false, discount_amount: 0 },
        { status: 400 }
      );
    }

    if (orderTotal <= 0) {
      return NextResponse.json(
        { error_message: 'Valid order total is required', is_valid: false, discount_amount: 0 },
        { status: 400 }
      );
    }
    
    // Fetch basic discount code information (only public fields)
    const { data, error } = await supabase
      .from('discount_codes')
      .select(`
        code,
        description,
        type,
        value,
        minimum_order_amount,
        maximum_discount_amount,
        valid_from,
        valid_until,
        is_active
      `)
      .eq('code', code.toUpperCase().trim())
      .eq('is_active', true)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error_message: 'Discount code not found', is_valid: false, discount_amount: 0 },
          { status: 200 }
        );
      }

      console.error('Error fetching discount code:', error);
      return NextResponse.json(
        { error_message: 'Failed to fetch discount code', is_valid: false, discount_amount: 0 },
        { status: 500 }
      );
    }

    // Check if code is currently valid (date checks)
    const now = new Date();
    const validFrom = data.valid_from ? new Date(data.valid_from) : new Date();
    const validUntil = data.valid_until ? new Date(data.valid_until) : null;

    if (now < validFrom) {
      return NextResponse.json({
        error_message: 'This discount code is not yet active',
        is_valid: false,
        discount_amount: 0
      });
    }

    if (validUntil && now > validUntil) {
      return NextResponse.json({
        error_message: 'This discount code has expired',
        is_valid: false,
        discount_amount: 0
      });
    }

    // Check minimum order amount
    if (data.minimum_order_amount && orderTotal < data.minimum_order_amount) {
      return NextResponse.json({
        error_message: `Minimum order amount of €${data.minimum_order_amount} required`,
        is_valid: false,
        discount_amount: 0
      });
    }

    // Calculate discount amount
    let discountAmount = 0;
    if (data.type === 'percentage') {
      discountAmount = (orderTotal * data.value) / 100;
      // Apply maximum discount limit if set
      if (data.maximum_discount_amount && discountAmount > data.maximum_discount_amount) {
        discountAmount = data.maximum_discount_amount;
      }
    } else if (data.type === 'fixed_amount') {
      discountAmount = Math.min(data.value, orderTotal); // Don't exceed order total
    }

    // Round to 2 decimal places
    discountAmount = Math.round(discountAmount * 100) / 100;

    return NextResponse.json({
      is_valid: true,
      discount_amount: discountAmount,
      discount_type: data.type,
      discount_value: data.value,
      code: data.code,
      description: data.description
    });
    
  } catch (error) {
    console.error('Unexpected error in GET discount code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}