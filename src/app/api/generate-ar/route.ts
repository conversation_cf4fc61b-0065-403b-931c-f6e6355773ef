import { NextRequest, NextResponse } from 'next/server';
import { v2 as cloudinary } from 'cloudinary';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export async function POST(request: NextRequest) {
  // Check authentication
  const supabase = createRouteHandlerClient({ cookies });
  const { data: { session } } = await supabase.auth.getSession();
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Verify admin role
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', session.user.id)
    .single();
    
  if (!profile || profile.role !== 'admin') {
    return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 });
  }
  
  try {
    const data = await request.json();
    const { publicId } = data;
    
    if (!publicId) {
      return NextResponse.json({ error: 'Missing publicId parameter' }, { status: 400 });
    }
    
    console.log(`Generating AR experience for image: ${publicId}`);
    
    // Generate the AR experience with enhanced settings
    const arPublicId = `${publicId}_ar`;
    
    // Enhanced transformation for better AR effect
    const result = await cloudinary.uploader.explicit(publicId, {
      type: 'upload',
      eager: [
        { 
          width: 1200, 
          height: 1200, 
          crop: 'fill', 
          effect: 'cartoonify:50',
          angle: 10,
          format: 'png',
          public_id: arPublicId 
        }
      ]
    });
    
    if (!result || !result.eager || !result.eager[0]) {
      console.error('Failed to generate AR experience, invalid response:', result);
      return NextResponse.json({ error: 'Failed to generate AR experience' }, { status: 500 });
    }
    
    console.log('AR experience generated successfully:', result.eager[0]);
    
    return NextResponse.json({
      success: true,
      ar: {
        publicId: arPublicId,
        url: result.eager[0].secure_url,
        width: result.eager[0].width,
        height: result.eager[0].height
      }
    });
  } catch (error) {
    console.error('Error generating AR experience:', error);
    return NextResponse.json(
      { error: 'Failed to generate AR experience', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 