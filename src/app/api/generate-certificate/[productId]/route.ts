import { NextResponse } from 'next/server';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import PDFDocument from 'pdfkit';
import path from 'path';

export async function GET(
  request: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const { productId } = params;
    const supabase = createServerComponentClient({ cookies });

    // Fetch product details
    const { data: product, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        description,
        created_at,
        image_url,
        price,
        specifications
      `)
      .eq('id', productId)
      .single();

    if (error) throw error;
    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Extract condition from specifications if available
    const condition = product.specifications?.condition || 'Not specified';

    // Create PDF using default fonts
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50,
      info: {
        Title: `Treasure of Maimi Certificate of Authenticity - ${product.name}`,
        Author: 'Treasure of Maimi Luxury Vintage',
        Subject: 'Certificate of Authenticity',
        Keywords: 'luxury, vintage, authentication, certificate'
      }
    });

    // Set response headers
    const headers = new Headers();
    headers.set('Content-Type', 'application/pdf');
    headers.set('Content-Disposition', `attachment; filename=treasure-of-maimi-certificate-${product.id}.pdf`);

    // Create a transform stream
    const chunks: Buffer[] = [];
    doc.on('data', chunk => chunks.push(chunk));
    
    // Add luxury border
    doc.lineWidth(2)
       .rect(30, 30, doc.page.width - 60, doc.page.height - 60)
       .stroke('#171717');

    // Add decorative corners
    const cornerSize = 20;
    [[30, 30], [doc.page.width - 30, 30], [30, doc.page.height - 30], [doc.page.width - 30, doc.page.height - 30]].forEach(([x, y]) => {
      doc.lineWidth(3)
         .moveTo(x - cornerSize, y)
         .lineTo(x + cornerSize, y)
         .moveTo(x, y - cornerSize)
         .lineTo(x, y + cornerSize)
         .stroke('#171717');
    });

    // Add welcome card
    try {
      const welcomeCardPath = path.join(process.cwd(), 'public', 'images', 'welcome-card.png');
      doc.image(welcomeCardPath, {
        fit: [200, 200],
        align: 'center'
      });
      doc.moveDown();
    } catch (err) {
      console.error('Error adding welcome card:', err);
    }

    // Header
    doc.fontSize(28)
       .fillColor('#171717')
       .text('Treasure of Maimi', { align: 'center' })
       .moveDown(0.5);

    doc.fontSize(18)
       .fillColor('#666666')
       .text('Certificate of Authenticity', { align: 'center' })
       .moveDown(0.5);

    // Thank you message
    doc.fontSize(12)
       .fillColor('#666666')
       .text('Thank you for choosing Treasure of Maimi. This certificate verifies the authenticity of your luxury item.', {
         align: 'center',
         width: 400
       })
       .moveDown(2);

    // Add product image if available
    if (product.image_url) {
      try {
        const response = await fetch(product.image_url);
        const imageBuffer = await response.arrayBuffer();
        
        // Add image with border and shadow effect
        doc.image(Buffer.from(imageBuffer), {
          fit: [300, 300],
          align: 'center'
        });
        doc.moveDown(2);
      } catch (err) {
        console.error('Error adding product image:', err);
      }
    }

    // Product details in a styled table format
    const details = [
      ['Product Name', product.name],
      ['Serial Number', product.specifications?.serial_number || product.id.slice(0, 8).toUpperCase()],
      ['Issue Date', new Date(product.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })],
      ['Condition', condition],
      ['Value', new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'EUR'
      }).format(product.price)]
    ];

    details.forEach(([label, value], index) => {
      const y = doc.y;
      const color = index % 2 === 0 ? '#f5f5f5' : '#ffffff';
      
      // Background rectangle
      doc.rect(70, y - 5, doc.page.width - 140, 30)
         .fill(color);
      
      // Text
      doc.fontSize(10)
         .fillColor('#666666')
         .text(label, 80, y, { width: 150 });
      
      doc.fontSize(10)
         .fillColor('#171717')
         .text(value.toString(), 240, y, { width: doc.page.width - 300 });
      
      doc.moveDown(0.8);
    });

    // Description if available
    if (product.description) {
      doc.moveDown()
         .fontSize(10)
         .fillColor('#666666')
         .text('Description', { underline: true })
         .moveDown(0.5)
         .fillColor('#171717')
         .text(product.description, {
           align: 'justify',
           columns: 1
         })
         .moveDown(2);
    }

    // Verification section
    doc.fontSize(10)
       .fillColor('#666666')
       .text('To verify this certificate, visit:', { align: 'center' })
       .moveDown(0.3)
       .fillColor('#171717')
       .text(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/verify/${product.id}`, {
         align: 'center',
         underline: true
       });

    // Footer with timestamp and signature
    doc.moveDown(2)
       .fontSize(8)
       .fillColor('#666666')
       .text(`Generated on ${new Date().toLocaleString()}`, { align: 'center' })
       .moveDown(0.5)
       .text('This certificate is digitally generated and does not require a physical signature.', {
         align: 'center'
       });

    // Finalize PDF
    doc.end();

    // Wait for all chunks
    const pdfBuffer = Buffer.concat(chunks);

    return new NextResponse(pdfBuffer, {
      headers,
      status: 200,
    });
  } catch (error) {
    console.error('Error generating certificate:', error);
    return NextResponse.json(
      { error: 'Failed to generate certificate' },
      { status: 500 }
    );
  }
}
