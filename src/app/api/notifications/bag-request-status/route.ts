import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/lib/database.types';
import { unifiedNotificationService } from '@/lib/unified-notifications';

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check authentication and admin status
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { user_email, user_name, bag_name, old_status, new_status, notes } = body;

    if (!user_email || !bag_name || !new_status) {
      return NextResponse.json(
        { error: 'Missing required fields: user_email, bag_name, new_status' },
        { status: 400 }
      );
    }

    // Send bag request status notification
    const success = await unifiedNotificationService.sendBagRequestStatusNotification({
      user_email,
      user_name: user_name || 'User',
      bag_name,
      old_status: old_status || 'unknown',
      new_status,
      notes
    });

    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: 'Notification sent successfully' 
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to send notification' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Bag request status notification error:', error);
    return NextResponse.json(
      { error: 'Failed to send notification' },
      { status: 500 }
    );
  }
}