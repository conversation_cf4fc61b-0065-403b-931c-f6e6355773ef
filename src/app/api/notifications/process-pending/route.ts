import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { smsNotificationService } from '@/lib/sms-notifications';
import { emailNotificationService } from '@/lib/email-notifications';

export async function POST(request: NextRequest) {
  try {
    // Use service role client for admin operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    console.log('Processing pending notifications...');

    // Get all pending notifications
    const { data: pendingNotifications, error: notificationsError } = await supabase
      .from('notifications')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: true });

    if (notificationsError) {
      console.error('Error fetching pending notifications:', notificationsError);
      return NextResponse.json({ 
        error: 'Failed to fetch pending notifications' 
      }, { status: 500 });
    }

    if (!pendingNotifications || pendingNotifications.length === 0) {
      console.log('No pending notifications found');
      return NextResponse.json({ 
        success: true, 
        message: 'No pending notifications to process',
        processed: 0
      });
    }

    console.log(`Found ${pendingNotifications.length} pending notifications`);

    let processedCount = 0;
    let successCount = 0;

    // Process each notification
    for (const notification of pendingNotifications) {
      try {
        console.log(`Processing notification ${notification.id} of type ${notification.type}`);

        // Send SMS notification
        const smsSuccess = await smsNotificationService.getInstance().sendAdminNotification({
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          priority: notification.priority || 'medium'
        });

        // Send email notification
        const emailSuccess = await emailNotificationService.getInstance().sendAdminNotification({
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          priority: notification.priority || 'medium'
        });

        // Update notification status
        const updateData: any = {
          status: 'sent',
          updated_at: new Date().toISOString()
        };

        if (smsSuccess) {
          updateData.push_sent_at = new Date().toISOString();
        }

        if (emailSuccess) {
          updateData.email_sent_at = new Date().toISOString();
        }

        const { error: updateError } = await supabase
          .from('notifications')
          .update(updateData)
          .eq('id', notification.id);

        if (updateError) {
          console.error(`Error updating notification ${notification.id}:`, updateError);
        } else {
          console.log(`Successfully processed notification ${notification.id}`);
          successCount++;
        }

        processedCount++;

      } catch (error) {
        console.error(`Error processing notification ${notification.id}:`, error);
        
        // Mark as failed
        await supabase
          .from('notifications')
          .update({
            status: 'failed',
            updated_at: new Date().toISOString()
          })
          .eq('id', notification.id);

        processedCount++;
      }
    }

    console.log(`Processed ${processedCount} notifications, ${successCount} successful`);

    return NextResponse.json({ 
      success: true, 
      message: `Processed ${processedCount} notifications`,
      processed: processedCount,
      successful: successCount
    });

  } catch (error) {
    console.error('Error processing pending notifications:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// Allow GET requests to manually trigger processing
export async function GET(request: NextRequest) {
  return POST(request);
}
