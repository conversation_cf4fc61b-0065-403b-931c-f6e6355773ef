import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { unifiedNotificationService } from '@/lib/unified-notifications';

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { type, data } = body;

    if (!type) {
      return NextResponse.json({ 
        error: 'Notification type is required' 
      }, { status: 400 });
    }

    let success = false;

    // Send appropriate notification based on type
    switch (type) {
      case 'new_order':
        if (!data?.order_id) {
          return NextResponse.json({ 
            error: 'Order ID required for new order notification' 
          }, { status: 400 });
        }
        success = await unifiedNotificationService.sendNewOrderNotification(data);
        break;

      case 'order_status_change':
        if (!data?.order_id || !data?.old_status || !data?.new_status) {
          return NextResponse.json({ 
            error: 'Order ID, old status, and new status required for order status change notification' 
          }, { status: 400 });
        }
        success = await unifiedNotificationService.sendOrderStatusChangeNotification(data);
        break;

      case 'new_message':
        if (!data?.message_id || !data?.sender_name || !data?.sender_email) {
          return NextResponse.json({ 
            error: 'Message ID, sender name, and sender email required for new message notification' 
          }, { status: 400 });
        }
        success = await unifiedNotificationService.sendNewMessageNotification(data);
        break;

      case 'new_bag_request':
        if (!data?.request_id) {
          return NextResponse.json({ 
            error: 'Request ID required for bag request notification' 
          }, { status: 400 });
        }
        success = await unifiedNotificationService.sendNewBagRequestNotification(data);
        break;

      case 'low_inventory':
        if (!data?.product_id || !data?.product_name || data?.current_quantity === undefined) {
          return NextResponse.json({ 
            error: 'Product ID, product name, and current quantity required for low inventory notification' 
          }, { status: 400 });
        }
        success = await unifiedNotificationService.sendLowInventoryNotification(data);
        break;

      case 'payment_received':
        if (!data?.order_id || !data?.amount) {
          return NextResponse.json({ 
            error: 'Order ID and amount required for payment received notification' 
          }, { status: 400 });
        }
        success = await unifiedNotificationService.sendPaymentReceivedNotification(data);
        break;

      case 'refund_requested':
        if (!data?.order_id || !data?.amount) {
          return NextResponse.json({ 
            error: 'Order ID and amount required for refund request notification' 
          }, { status: 400 });
        }
        success = await unifiedNotificationService.sendRefundRequestNotification(data);
        break;

      case 'system_alert':
        if (!data?.alert_type || !data?.message) {
          return NextResponse.json({ 
            error: 'Alert type and message required for system alert notification' 
          }, { status: 400 });
        }
        success = await unifiedNotificationService.sendSystemAlertNotification(data);
        break;

      default:
        return NextResponse.json({ 
          error: 'Invalid notification type' 
        }, { status: 400 });
    }

    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: 'Notifications sent successfully through all enabled channels' 
      });
    } else {
      return NextResponse.json({ 
        error: 'Failed to send notifications' 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error sending unified notifications:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
