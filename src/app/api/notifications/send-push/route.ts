import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { pushNotificationService } from '@/lib/push-notifications';

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { type, data } = body;

    if (!type) {
      return NextResponse.json({ error: 'Notification type required' }, { status: 400 });
    }

    let success = false;

    // Send appropriate notification based on type
    switch (type) {
      case 'new_order':
        if (!data?.orderId) {
          return NextResponse.json({ error: 'Order ID required for new order notification' }, { status: 400 });
        }
        success = await pushNotificationService.getInstance().sendNewOrderNotification(data.orderId);
        break;

      case 'new_bag_request':
        if (!data?.requestId) {
          return NextResponse.json({ error: 'Request ID required for bag request notification' }, { status: 400 });
        }
        success = await pushNotificationService.getInstance().sendNewBagRequestNotification(data.requestId);
        break;

      case 'low_inventory':
        if (!data?.productId) {
          return NextResponse.json({ error: 'Product ID required for low inventory notification' }, { status: 400 });
        }
        success = await pushNotificationService.getInstance().sendLowInventoryNotification(data.productId);
        break;

      default:
        return NextResponse.json({ error: 'Invalid notification type' }, { status: 400 });
    }

    if (success) {
      return NextResponse.json({ success: true, message: 'Push notification sent successfully' });
    } else {
      return NextResponse.json({ error: 'Failed to send push notification' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error sending push notification:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
