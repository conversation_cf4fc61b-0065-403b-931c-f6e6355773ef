import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { whatsappNotificationService } from '@/lib/whatsapp-notifications';

export async function POST(request: NextRequest) {
  try {
    // Use service role client for admin operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    let user: any = null;

    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser(token);

      if (authError || !authUser) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      user = authUser;
    } else {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    console.log('User ID:', user.id);
    console.log('Profile data:', profile);
    console.log('Profile error:', profileError);

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return NextResponse.json({ error: 'Error checking admin status' }, { status: 500 });
    }

    if (!profile?.is_admin) {
      console.log('User is not admin:', profile);
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { type, title, message, data, priority, phoneNumber } = body;

    // Handle test message
    if (type === 'test' && phoneNumber) {
      const success = await whatsappNotificationService.getInstance().sendTestMessage(phoneNumber);
      
      if (success) {
        return NextResponse.json({ 
          success: true, 
          message: 'Test WhatsApp message sent successfully' 
        });
      } else {
        return NextResponse.json({ 
          error: 'Failed to send test WhatsApp message' 
        }, { status: 500 });
      }
    }

    if (!type || !title || !message) {
      return NextResponse.json({ 
        error: 'Type, title, and message are required' 
      }, { status: 400 });
    }

    // Send WhatsApp notification
    const success = await whatsappNotificationService.getInstance().sendAdminNotification({
      type,
      title,
      message,
      data,
      priority: priority || 'medium'
    });

    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: 'WhatsApp notification sent successfully' 
      });
    } else {
      return NextResponse.json({ 
        error: 'Failed to send WhatsApp notification' 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error sending WhatsApp notification:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
