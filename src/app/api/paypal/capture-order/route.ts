import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/lib/database.types';
import { unifiedNotificationService } from '@/lib/unified-notifications';

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { orderID, dbOrderId } = body;

    if (!orderID) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Update order status from 'pending' to 'processing' (payment confirmed, order can be fulfilled)
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .update({
        status: 'processing',
        payment_status: 'completed',
        payment_intent: orderID,
        status_updated_at: new Date().toISOString(),
      })
      .eq('id', dbOrderId)
      .eq('user_id', session.user.id)
      .eq('status', 'pending') // Only update if still pending (payment not confirmed)
      .select()
      .single();

    if (orderError) {
      console.error('Error updating order:', orderError);
      return NextResponse.json(
        { error: 'Failed to update order' },
        { status: 500 }
      );
    }

    // Generate order number if not exists
    let finalOrderNumber = orderData.order_number;
    if (!orderData.order_number) {
      finalOrderNumber = `ORD-${Date.now()}-${orderData.id.slice(-6).toUpperCase()}`;
      await supabase
        .from('orders')
        .update({ order_number: finalOrderNumber })
        .eq('id', orderData.id);
    }

    // Send admin notification for new PayPal order
    try {
      await unifiedNotificationService.sendNewOrderNotification({
        order_id: orderData.id,
        order_number: finalOrderNumber || orderData.id,
        total_amount: orderData.total_amount || 0,
        customer_email: orderData.customer_email || 'Unknown'
      });
      console.log('📧 New PayPal order notification sent to admin');
    } catch (notificationError) {
      console.error('❌ Error sending PayPal order notification:', notificationError);
    }

    return NextResponse.json({
      id: orderID,
      status: 'COMPLETED',
      orderData: orderData
    });

  } catch (error) {
    console.error('PayPal capture order error:', error);
    return NextResponse.json(
      { error: 'Failed to capture PayPal order' },
      { status: 500 }
    );
  }
}
