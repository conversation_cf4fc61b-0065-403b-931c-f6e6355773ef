import { NextRequest, NextResponse } from 'next/server';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// PayPal API base URL based on environment
const PAYPAL_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://api-m.paypal.com'
  : 'https://api-m.sandbox.paypal.com';

export async function POST(req: NextRequest) {
  try {
    // Get request data
    const { items, shipping, userEmail } = await req.json();
    const supabase = createServerComponentClient({ cookies });

    // Helper function defined inside the POST method to have access to supabase
    async function getUserIdFromEmail(email: string) {
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single();

      if (error) {
        console.error('Error fetching user:', error);
        return null;
      }

      return data?.id || null;
    }

    // Calculate total amount
    const subtotal = items.reduce((sum: number, item: any) => {
      return sum + item.product.price * item.quantity;
    }, 0);

    const shippingCost = shipping?.cost || 0;
    const totalAmount = subtotal + shippingCost;

    // Generate a unique order reference
    const orderRef = `REF-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // PayPal credentials
    const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
    const clientSecret = process.env.PAYPAL_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      return NextResponse.json({ error: 'PayPal configuration is missing' }, { status: 500 });
    }

    // Get access token from PayPal
    const tokenResponse = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
      },
      body: 'grant_type=client_credentials',
    });

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    if (!accessToken) {
      return NextResponse.json({ error: 'Failed to authenticate with PayPal' }, { status: 500 });
    }

    // Create order in PayPal
    const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        intent: 'CAPTURE',
        purchase_units: [
          {
            reference_id: orderRef,
            description: 'Shop Maimi Purchase',
            amount: {
              currency_code: 'EUR',
              value: totalAmount.toFixed(2),
              breakdown: {
                item_total: {
                  currency_code: 'EUR',
                  value: subtotal.toFixed(2),
                },
                shipping: {
                  currency_code: 'EUR',
                  value: shippingCost.toFixed(2),
                },
              },
            },
            items: items.map((item: any) => ({
              name: item.product.name,
              unit_amount: {
                currency_code: 'EUR',
                value: item.product.price.toFixed(2),
              },
              quantity: item.quantity,
              category: 'PHYSICAL_GOODS',
            })),
            shipping: {
              address: shipping.address,
            },
          },
        ],
        application_context: {
          brand_name: 'Shop Maimi',
          locale: 'en-US',
          landing_page: 'BILLING',
          shipping_preference: 'SET_PROVIDED_ADDRESS',
          user_action: 'PAY_NOW',
          return_url: `${req.headers.get('origin')}/order-confirmation`,
          cancel_url: `${req.headers.get('origin')}/cart`,
        },
      }),
    });

    const data = await response.json();

    if (data.error) {
      console.error('PayPal error:', data.error);
      return NextResponse.json({ error: data.error }, { status: 500 });
    }

    // Get user id if authenticated
    const userId = userEmail ? await getUserIdFromEmail(userEmail) : null;

    // Create a temporary order record in the database
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .insert({
        order_number: orderRef,
        status: 'pending',
        total_amount: totalAmount,
        subtotal: subtotal,
        shipping_amount: shippingCost,
        tax_amount: 0,
        discount_amount: 0,
        payment_method: 'paypal',
        payment_status: 'awaiting_payment',
        shipping_address: shipping?.address || null,
        user_id: userId,
        notes: JSON.stringify({ paypal_order_id: data.id }),
      })
      .select()
      .single()
      .throwOnError();

    if (orderError) {
      console.error('Database error:', orderError);
      // Return PayPal order ID even if DB storage fails
      return NextResponse.json({ id: data.id, orderId: orderRef }, { status: 200 });
    }

    // Store order items
    if (orderData) {
      const orderItems = items.map((item: any) => ({
        order_id: orderData.id,
        product_id: item.product_id,
        quantity: item.quantity,
        price: item.product.price,
        total_price: item.product.price * item.quantity,
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems)
        .throwOnError();

      if (itemsError) {
        console.error('Error storing order items:', itemsError);
      }
    }

    // Return PayPal order ID to client
    return NextResponse.json(
      {
        id: data.id,
        orderId: orderRef,
        dbOrderId: orderData?.id,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Create PayPal order error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}