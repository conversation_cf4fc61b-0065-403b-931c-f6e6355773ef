import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/lib/database.types';

interface DiscountInfo {
  code: string;
  discount: number;
  applied: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { items, shipping, userEmail, discountInfo }: {
      items: any[];
      shipping: any;
      userEmail: string;
      discountInfo?: DiscountInfo | null;
    } = body;

    // Calculate totals
    const subtotal = items.reduce((acc, item) => acc + (item.product.price * item.quantity), 0);
    const shippingCost = shipping?.cost || 0;
    const discountAmount = discountInfo?.applied ? discountInfo.discount : 0;
    const total = Math.max(0, subtotal + shippingCost - discountAmount);

    // Create PayPal order structure
    const paypalOrder = {
      intent: 'CAPTURE',
      purchase_units: [
        {
          amount: {
            currency_code: 'EUR',
            value: total.toFixed(2),
            breakdown: {
              item_total: {
                currency_code: 'EUR',
                value: subtotal.toFixed(2)
              },
              shipping: {
                currency_code: 'EUR',
                value: shippingCost.toFixed(2)
              },
              discount: discountInfo?.applied ? {
                currency_code: 'EUR',
                value: discountAmount.toFixed(2)
              } : undefined
            }
          },
          items: items.map(item => ({
            name: item.product.name,
            unit_amount: {
              currency_code: 'EUR',
              value: item.product.price.toFixed(2)
            },
            quantity: item.quantity.toString(),
            description: item.product.condition ? `Condition: ${item.product.condition}` : undefined
          }))
        }
      ]
    };

    // For now, return a mock PayPal order ID since PayPal SDK integration is not fully implemented
    const mockOrderId = `PAYPAL_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Store order in database
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .insert([
        {
          user_id: session.user.id,
          session_id: mockOrderId,
          status: 'pending',
          total_amount: total,
          payment_provider: 'paypal',
          payment_status: 'awaiting_payment',
          customer_email: userEmail,
          notes: discountInfo?.applied 
            ? `Discount applied: ${discountInfo.code} (-€${discountAmount.toFixed(2)})`
            : null,
        }
      ])
      .select()
      .single();

    if (orderError) {
      console.error('Error creating order:', orderError);
      return NextResponse.json(
        { error: 'Failed to create order' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      id: mockOrderId,
      dbOrderId: orderData.id,
      status: 'CREATED'
    });

  } catch (error) {
    console.error('PayPal create order error:', error);
    return NextResponse.json(
      { error: 'Failed to create PayPal order' },
      { status: 500 }
    );
  }
}
