import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Check authentication
  const supabase = createRouteHandlerClient({ cookies });
  const { data: { session } } = await supabase.auth.getSession();
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Verify admin role
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', session.user.id)
    .single();
    
  if (!profile || profile.role !== 'admin') {
    return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 });
  }
  
  const { id } = params;
  
  if (!id) {
    return NextResponse.json({ error: 'Media ID is required' }, { status: 400 });
  }
  
  try {
    // First, get the media item to get the Cloudinary ID
    const { data: mediaItem, error: fetchError } = await supabase
      .from('product_media')
      .select('cloudinary_id')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      console.error('Error fetching media item:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch media item' }, { status: 500 });
    }
    
    if (!mediaItem) {
      return NextResponse.json({ error: 'Media item not found' }, { status: 404 });
    }
    
    // Delete from Cloudinary if we have a cloudinary_id
    if (mediaItem.cloudinary_id) {
      try {
        await cloudinary.uploader.destroy(mediaItem.cloudinary_id);
        console.log(`Deleted image from Cloudinary: ${mediaItem.cloudinary_id}`);
      } catch (cloudinaryError) {
        console.error('Error deleting from Cloudinary:', cloudinaryError);
        // Continue with database deletion even if Cloudinary deletion fails
      }
    }
    
    // Delete from database
    const { error: deleteError } = await supabase
      .from('product_media')
      .delete()
      .eq('id', id);
    
    if (deleteError) {
      console.error('Error deleting media item from database:', deleteError);
      return NextResponse.json({ error: 'Failed to delete media item from database' }, { status: 500 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting media item:', error);
    return NextResponse.json(
      { error: 'Failed to delete media item', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 