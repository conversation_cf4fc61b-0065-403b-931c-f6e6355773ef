import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// Use environment variables for Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.NEXT_SUPABASE_SERVICE_ROLE_KEY;

// Helper function to check if the user is an admin
async function isAdmin(supabase: any) {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log('No session found in isAdmin check');
      return false;
    }
    
    // Check admin status through is_admin flag in profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return false;
    }

    return profile?.is_admin === true;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// GET product media for a product
export async function GET(request: NextRequest) {
  try {
    // Get product_id from URL
    const url = new URL(request.url);
    const productId = url.searchParams.get('product_id');
    
    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }
    
    // Validate required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create service client with admin privileges
    const serviceClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Get product media
    const { data, error } = await serviceClient
      .from('product_media')
      .select('*')
      .eq('product_id', productId)
      .order('position');
      
    if (error) {
      console.error('Error fetching product media:', error);
      return NextResponse.json({ 
        error: `Failed to fetch product media: ${error.message}`,
        details: error
      }, { status: 500 });
    }
    
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('Unexpected error in product media fetch:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch product media',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

// POST new product media
export async function POST(request: NextRequest) {
  try {
    // Check if the user is authenticated and is an admin
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const adminStatus = await isAdmin(supabase);
    if (!adminStatus) {
      return NextResponse.json(
        { error: 'Unauthorized. Only admins can manage product media.' },
        { status: 403 }
      );
    }
    
    // Validate required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create service client with admin privileges
    const serviceClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Parse request body
    const body = await request.json();
    const { product_id, url, alt_text, position, media_type, is_main } = body;
    
    if (!product_id) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }
    
    if (!url) {
      return NextResponse.json(
        { error: 'Media URL is required' },
        { status: 400 }
      );
    }
    
    // If is_main is true, update all other media for this product to is_main = false
    if (is_main) {
      const { error: updateError } = await serviceClient
        .from('product_media')
        .update({ is_main: false })
        .eq('product_id', product_id);
        
      if (updateError) {
        console.error('Error updating existing product media:', updateError);
      }
    }
    
    // Get the highest position to ensure proper ordering
    let nextPosition = position || 0;
    
    if (!position) {
      const { data: existingMedia, error: positionError } = await serviceClient
        .from('product_media')
        .select('position')
        .eq('product_id', product_id)
        .order('position', { ascending: false })
        .limit(1);
        
      if (!positionError && existingMedia && existingMedia.length > 0) {
        nextPosition = (existingMedia[0].position || 0) + 1;
      }
    }
    
    // Insert the new media
    const { data, error } = await serviceClient
      .from('product_media')
      .insert([{
        product_id,
        url,
        alt_text: alt_text || '',
        position: nextPosition,
        media_type: media_type || 'image',
        is_main: is_main || false
      }])
      .select()
      .single();
      
    if (error) {
      console.error('Error creating product media:', error);
      return NextResponse.json({ 
        error: `Failed to create product media: ${error.message}`,
        details: error
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      data,
      message: 'Product media created successfully'
    });
    
  } catch (error) {
    console.error('Unexpected error in product media creation:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create product media',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

// Handle bulk uploads
export async function PUT(request: NextRequest) {
  try {
    // Check if the user is authenticated and is an admin
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const adminStatus = await isAdmin(supabase);
    if (!adminStatus) {
      return NextResponse.json(
        { error: 'Unauthorized. Only admins can manage product media.' },
        { status: 403 }
      );
    }
    
    // Validate required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create service client with admin privileges
    const serviceClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Parse request body
    const body = await request.json();
    const { product_id, media } = body;
    
    if (!product_id) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }
    
    if (!media || !Array.isArray(media) || media.length === 0) {
      return NextResponse.json(
        { error: 'Media array is required and must not be empty' },
        { status: 400 }
      );
    }
    
    // If the first media is marked as main or if there's only one media item,
    // update all existing media to not be main
    const hasMainImage = media.some(item => item.is_main);
    
    if (hasMainImage) {
      const { error: updateError } = await serviceClient
        .from('product_media')
        .update({ is_main: false })
        .eq('product_id', product_id);
        
      if (updateError) {
        console.error('Error updating existing product media:', updateError);
      }
    }
    
    // Get the highest position to ensure proper ordering
    const { data: existingMedia, error: positionError } = await serviceClient
      .from('product_media')
      .select('position')
      .eq('product_id', product_id)
      .order('position', { ascending: false })
      .limit(1);
      
    let startPosition = 0;
    if (!positionError && existingMedia && existingMedia.length > 0) {
      startPosition = (existingMedia[0].position || 0) + 1;
    }
    
    // Prepare media items for insertion
    const mediaItems = media.map((item, index) => ({
      product_id,
      url: item.url,
      alt_text: item.alt_text || '',
      position: item.position !== undefined ? item.position : startPosition + index,
      media_type: item.media_type || 'image',
      is_main: item.is_main || false
    }));
    
    // Insert all media items
    const { data, error } = await serviceClient
      .from('product_media')
      .insert(mediaItems)
      .select();
      
    if (error) {
      console.error('Error creating product media in bulk:', error);
      return NextResponse.json({ 
        error: `Failed to create product media: ${error.message}`,
        details: error
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      data,
      message: `Successfully added ${mediaItems.length} media items`
    });
    
  } catch (error) {
    console.error('Unexpected error in bulk product media creation:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create product media in bulk',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

// DELETE product media
export async function DELETE(request: NextRequest) {
  try {
    // Check if the user is authenticated and is an admin
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const adminStatus = await isAdmin(supabase);
    if (!adminStatus) {
      return NextResponse.json(
        { error: 'Unauthorized. Only admins can delete product media.' },
        { status: 403 }
      );
    }
    
    // Validate required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create service client with admin privileges
    const serviceClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Get media ID from URL
    const url = new URL(request.url);
    let id = url.searchParams.get('id');
    
    // If not in URL, try to get from body
    if (!id) {
      const body = await request.json().catch(() => ({}));
      id = body.id;
      
      if (!id) {
        return NextResponse.json(
          { error: 'Media ID is required' },
          { status: 400 }
        );
      }
    }
    
    // Delete the media
    const { error } = await serviceClient
      .from('product_media')
      .delete()
      .eq('id', id);
      
    if (error) {
      console.error('Error deleting product media:', error);
      return NextResponse.json({ 
        error: `Failed to delete product media: ${error.message}`,
        details: error
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Product media deleted successfully'
    });
    
  } catch (error) {
    console.error('Unexpected error in product media deletion:', error);
    return NextResponse.json(
      { 
        error: 'Failed to delete product media',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
