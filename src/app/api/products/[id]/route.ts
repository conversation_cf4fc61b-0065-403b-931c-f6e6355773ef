import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// Use environment variables for Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.NEXT_SUPABASE_SERVICE_ROLE_KEY;

// Helper function to check if the user is an admin
async function isAdmin(supabase: any) {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log('No session found in isAdmin check');
      return false;
    }
    
    // Use user ID for more reliable lookup
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', session.user.id)
      .single();
    
    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return false;
    }
    
    return profile?.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * PATCH endpoint to update a product's image_url
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const productId = params.id;
    
    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }
    
    // Validate required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    // Create admin client
    const adminClient = createClient(supabaseUrl, supabaseServiceKey);
    
    // Authentication required for security - always check admin status
    const cookieStore = cookies();
    const authClient = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Check admin status
    const adminStatus = await isAdmin(authClient);
    
    if (!adminStatus) {
      return NextResponse.json({ 
        error: 'Unauthorized. Only admins can update products.',
      }, { status: 403 });
    }
    
    // Parse the request body
    const { image_url } = await request.json();
    
    if (!image_url) {
      return NextResponse.json({ error: 'image_url is required' }, { status: 400 });
    }
    
    // Update the product image
    const { data, error } = await adminClient
      .from('products')
      .update({ image_url })
      .eq('id', productId)
      .select();
      
    if (error) {
      console.error('Error updating product image:', error);
      return NextResponse.json({ 
        error: 'Failed to update product image',
        details: error.message
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Product image updated successfully',
      data
    });
  } catch (error) {
    console.error('Error in product update API:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
}
