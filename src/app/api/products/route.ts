import { NextRequest, NextResponse } from 'next/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/lib/database.types';

// Use environment variables for Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.NEXT_SUPABASE_SERVICE_ROLE_KEY;

// Helper function to check if the user is an admin
async function isAdmin(supabase: SupabaseClient<Database>) {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log('No session found in isAdmin check');
      return false;
    }
    
    console.log('Session user ID:', session.user.id);
    console.log('Session user email:', session.user.email);

    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return false;
    }
    
    console.log('User profile is_admin:', profile?.is_admin);
    return profile?.is_admin === true;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// Helper function to check if a column exists in a table
async function columnExists(supabase: SupabaseClient<Database>, tableName: string, columnName: string) {
  try {
    // Try to select a test row with the column
    const { error } = await supabase
      .from(tableName as any)
      .select(columnName)
      .limit(1);
      
    // If the error code indicates the column doesn't exist, return false
    if (error && error.code === 'PGRST204') {
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Error checking if ${columnName} exists in ${tableName}:`, error);
    return false;
  }
}

// Helper function to validate if a category exists and create it if not
async function validateCategory(supabase: SupabaseClient<Database>, categoryId: string | null, categoryName: string | null) {
  // If neither id nor name provided, return true (no category is fine)
  if (!categoryId && !categoryName) return { valid: true };
  
  try {
    // Hardcoded fallback categories for reliability
    const fallbackCategories = [
      { id: '44283ca0-d664-4f42-b1f4-9a3fb92c905f', name: 'Default', slug: 'default' },
      { id: 'b092ce24-88a7-458c-a34b-4fc61d8e72a6', name: 'Louis Vuitton', slug: 'louis-vuitton' },
      { id: 'caa9c28f-f911-4b3a-bf95-af00e0c41389', name: 'Gucci', slug: 'gucci' },
      { id: '2727105d-d03b-444b-aac0-46d66c7e8cb9', name: 'Hermès', slug: 'hermes' },
      { id: 'c47d36a8-84ce-4cf2-a443-6533c83f25f2', name: 'Chanel', slug: 'chanel' }
    ];
    
    // If category ID is provided, check if it exists
    if (categoryId) {
      // Check if slug field exists in categories table
      const hasSlug = await columnExists(supabase, 'categories', 'slug');
      
      // Select appropriate fields based on schema
      const selectFields = hasSlug ? 'id, name, slug' : 'id, name';
      
      const { data, error } = await supabase
        .from('categories')
        .select(selectFields)
        .eq('id', categoryId)
        .maybeSingle(); // Use maybeSingle() instead of single() to avoid errors
        
      if (error) {
        console.error('Error validating category by ID:', error);
      }
      
      // If found in database, return the existing category details
      if (data) {
        try {
          return { 
            valid: true, 
            id: (data as any).id, 
            name: (data as any).name, 
            // Use name as slug if slug doesn't exist in the schema
            slug: hasSlug ? (data as any).slug : (data as any).name.toLowerCase().replace(/\s+/g, '-'), 
            isNew: false 
          };
        } catch (error) {
          console.error('Error parsing category data:', error);
          return { valid: false, error: 'Invalid category data format' };
        }
      }
      
      // If not found but matches a fallback category, create it in the database
      const fallbackMatch = fallbackCategories.find(c => c.id === categoryId);
      if (fallbackMatch) {
        console.log(`Creating missing category from fallback: ${fallbackMatch.name}`);
        
        // Check if slug field exists in categories table
        const hasSlug = await columnExists(supabase, 'categories', 'slug');
        
        // Create insert object based on schema
        const categoryInsert: any = {
          id: fallbackMatch.id,
          name: fallbackMatch.name
        };
        
        // Only add slug if the field exists in the schema
        if (hasSlug && fallbackMatch.slug) {
          categoryInsert.slug = fallbackMatch.slug;
        } else if (hasSlug) {
          categoryInsert.slug = fallbackMatch.name.toLowerCase().replace(/\s+/g, '-');
        }
        
        const { data: newCategory, error: insertError } = await supabase
          .from('categories')
          .insert([categoryInsert])
          .select()
          .single();
          
        if (insertError) {
          console.error('Error creating category from fallback:', insertError);
          return { valid: false, error: `Failed to create category: ${insertError.message}` };
        }
        
        // Check if the slug field exists in the categories table
        const hasSlugInCategories = await columnExists(supabase, 'categories', 'slug');
        
        return { 
          valid: true, 
          id: newCategory.id, 
          name: newCategory.name, 
          // Use name as slug if slug doesn't exist in the schema
          slug: hasSlugInCategories ? (newCategory as any).slug : newCategory.name.toLowerCase().replace(/\s+/g, '-'),
          isNew: true 
        };
      }
      
      // If it's not in the database and not in fallbacks, it's invalid
      return { valid: false, error: `Category with ID ${categoryId} not found` };
    }
    
    // If we reach here, either the ID wasn't found or only name was provided
    // If only name was provided, check if a category with that name exists
    if (categoryName) {
      const { data, error } = await supabase
        .from('categories')
        .select('id, name')
        .ilike('name', categoryName)
        .single();
        
      if (!error && data) {
        // Found an existing category with this name
        return { valid: true, id: data.id, name: data.name, isNew: false };
      }
      
      // Create a new category
      const slug = categoryName
        .toLowerCase()
        .replace(/[^\w ]+/g, '')
        .replace(/ +/g, '-');
        
      const { data: newCategory, error: insertError } = await supabase
        .from('categories')
        .insert([{ name: categoryName, slug }])
        .select()
        .single();
        
      if (insertError) {
        console.error('Error creating new category:', insertError);
        return { valid: false, error: insertError.message };
      }
      
      console.log('Created new category:', newCategory);
      return { 
        valid: true, 
        id: newCategory.id, 
        name: newCategory.name, 
        // Use name as slug if slug doesn't exist in the schema
        slug: (newCategory as any).slug,
        isNew: true 
      };
    }
    
    return { valid: false, error: 'Invalid category' };
  } catch (error) {
    console.error('Error in category validation:', error);
    return { valid: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Create a valid slug from product name
function createSlug(name: string) {
  return name
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-');
}

// Generate a unique slug that doesn't conflict with existing products
async function generateUniqueSlug(supabase: SupabaseClient<Database>, name: string) {
  try {
    // Create base slug from product name
    let baseSlug = createSlug(name);
    console.log(`Generated base slug: "${baseSlug}"`);
    
    // Check if the slug already exists
    const { data, error } = await supabase
      .from('products')
      .select('slug')
      .eq('slug', baseSlug)
      .maybeSingle();
      
    // If no conflict found, return the base slug
    if (!data) {
      console.log(`Slug is unique, using: "${baseSlug}"`);
      return baseSlug;
    }
    
    // If we're here, the slug exists, so add a timestamp to make it unique
    const timestamp = new Date().getTime().toString().slice(-6);
    const uniqueSlug = `${baseSlug}-${timestamp}`;
    console.log(`Slug conflict detected, generating unique slug: "${uniqueSlug}"`);
    
    // Verify the new slug is unique (just to be super safe)
    const { data: checkData } = await supabase
      .from('products')
      .select('slug')
      .eq('slug', uniqueSlug)
      .maybeSingle();
      
    if (checkData) {
      // In the extremely unlikely case of a collision, add random chars
      const randomStr = Math.random().toString(36).substring(2, 7);
      const finalSlug = `${baseSlug}-${randomStr}`;
      console.log(`Additional collision detected, using final slug: "${finalSlug}"`);
      return finalSlug;
    }
    
    return uniqueSlug;
  } catch (error) {
    console.error('Error generating unique slug:', error);
    // Fallback to a safe slug with timestamp if any errors occur
    const timestamp = new Date().getTime().toString();
    const fallbackSlug = `${createSlug(name)}-${timestamp}`;
    console.log(`Error in slug generation, using fallback: "${fallbackSlug}"`);
    return fallbackSlug;
  }
}

// Ensure product has valid status
function ensureValidStatus(product: any) {
  // List of valid status values (based on our testing)
  const validStatuses = ['active', 'draft', 'published', 'archived'];
  
  // If status is missing or invalid, set it to active
  if (!product.status || !validStatuses.includes(product.status)) {
    product.status = 'active';
  }
  
  return product;
}

export async function POST(request: NextRequest) {
  try {
    // Validate required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    // Create a client using the service key for admin operations
    const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey);
    
    // Create a route handler client for checking auth with cookies
    const cookieStore = cookies();
    const authClient = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get the session and check admin status
    const { data: { session } } = await authClient.auth.getSession();
    console.log('Session in POST:', session ? { 
      user_id: session.user.id,
      email: session.user.email
    } : 'No session found');
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized. Please sign in.' }, { status: 401 });
    }

    // Check admin status with admin client for more reliable access
    const { data: profile, error: profileError } = await adminClient
      .from('profiles')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    console.log('Profile check result:', profile, profileError);

    if (profileError || profile?.is_admin !== true) {
      return NextResponse.json({
        error: 'Unauthorized. Only admins can create products.',
        details: profileError ? profileError.message : 'User is not an admin'
      }, { status: 403 });
    }
    
    // Parse the incoming product data
    const productData = await request.json();
    console.log('Received product data:', productData);

    // Ensure specifications are proper JSONB
    const specifications = productData.specifications || {};

    // Generate a slug if not provided
    const slug = productData.slug || await generateUniqueSlug(adminClient, productData.name);
    
    // Ensure product has valid status
    ensureValidStatus(productData);
    
    // Validate the category before inserting
    const categoryValidation = await validateCategory(
      adminClient, 
      productData.category, 
      productData.categoryName
    );
    
    // If category validation failed
    if (!categoryValidation.valid) {
      return NextResponse.json({ 
        error: 'Invalid category', 
        details: categoryValidation.error 
      }, { status: 400 });
    }
    
    // Use the validated category ID
    const categoryId = categoryValidation.id;
    
    // Log if a new category was created
    if (categoryValidation.isNew) {
      console.log(`Created new category '${categoryValidation.name}' with ID ${categoryId}`);
    }
    
    // Base data that will definitely work
    const insertData = {
      name: productData.name,
      slug,
      description: productData.description,
      price: parseFloat(productData.price),
      category: categoryId, // Use validated category ID
      status: productData.status,
      specifications
    };

    // Check if the image_url column exists, and if so, include it in the data
    if (productData.image_url) {
      const hasImageUrlColumn = await columnExists(adminClient, 'products', 'image_url');
      
      if (hasImageUrlColumn) {
        Object.assign(insertData, { image_url: productData.image_url });
        console.log('Including image_url in product data');
      } else {
        console.warn('Warning: image_url column does not exist in products table. Skipping this field.');
        console.warn('See /src/docs/add-image-url-column.md for instructions on adding this column.');
      }
    }

    console.log('Inserting product data:', insertData);

    // Insert into the products table using admin client 
    const { data, error } = await adminClient
      .from('products')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('Error inserting product:', error);
      console.error('Error details:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      });
      
      // Handle foreign key constraint violations 
      if (error.code === '23503' && error.details.includes('products_category_fkey')) {
        return NextResponse.json({ 
          error: 'Invalid category. The specified category does not exist.',
          code: error.code,
          details: 'You need to select a valid category or leave it empty'
        }, { status: 400 });
      }
      
      // Handle duplicate slug errors
      if (error.code === '23505' && error.details.includes('products_slug_key')) {
        console.log(`DUPLICATE SLUG DETECTED: "${slug}" already exists`);
        
        // Get current count of products to use as a unique identifier
        const { count } = await adminClient
          .from('products')
          .select('*', { count: 'exact', head: true });
          
        // Generate a truly unique slug with both timestamp and count
        const timestamp = new Date().getTime().toString().slice(-6);
        const productCount = count ? count + 1 : 1;
        const newSlug = `${slug}-${timestamp}-${productCount}`;
        
        console.log(`RETRY ATTEMPT: Trying with new unique slug: "${newSlug}"`);
        
        // Try inserting again with the new slug
        const { data: retryData, error: retryError } = await adminClient
          .from('products')
          .insert({...insertData, slug: newSlug})
          .select()
          .single();
          
        if (retryError) {
          console.error('Error on retry with new slug:', retryError);
          
          // If we still have an error, try one more time with a completely different approach
          if (retryError.code === '23505') {
            const finalSlug = `product-${Math.random().toString(36).substring(2, 10)}-${timestamp}`;
            console.log(`FINAL RETRY ATTEMPT: Using completely random slug: "${finalSlug}"`);
            
            const { data: finalData, error: finalError } = await adminClient
              .from('products')
              .insert({...insertData, slug: finalSlug})
              .select()
              .single();
              
            if (finalError) {
              console.error('Final retry failed:', finalError);
              return NextResponse.json({ 
                error: 'Failed to create product after multiple retry attempts',
                details: finalError.message,
                code: finalError.code
              }, { status: 500 });
            }
            
            console.log('FINAL RETRY SUCCESSFUL: Product inserted with random slug');
            return NextResponse.json({ success: true, product: finalData });
          }
          
          return NextResponse.json({ 
            error: 'Failed to create product with alternative slug',
            details: retryError.message,
            code: retryError.code
          }, { status: 500 });
        }
        
        console.log('RETRY SUCCESSFUL: Product inserted with new slug:', newSlug);
        return NextResponse.json({ success: true, product: retryData });
      }
      
      // If the error is related to the image_url column not existing
      if (error.code === 'PGRST204' && error.message.includes('image_url')) {
        return NextResponse.json({ 
          error: 'The image_url column is missing from the products table. Please see /src/docs/add-image-url-column.md for instructions.',
          code: error.code,
          details: 'You need to add the image_url column to your products table'
        }, { status: 500 });
      }
      
      // If we're using the anon key and get a permissions error, explain this
      let errorMessage = error.message;
      if (error.code === '42501' && !supabaseServiceKey) {
        errorMessage = 'Permission denied: Service role key is required to insert products. Please add SUPABASE_SERVICE_ROLE_KEY to your environment.';
      }
      
      return NextResponse.json({ 
        error: errorMessage,
        code: error.code,
        details: error.details
      }, { status: 500 });
    }

    console.log('Product inserted successfully:', data);
    return NextResponse.json({ success: true, product: data });
  } catch (error) {
    console.error('Error in products API:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
}

// GET handler for public product access
export async function GET(request: NextRequest) {
  try {
    // Validate required environment variables
    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
    const { searchParams } = new URL(request.url);
    
    // Extract query parameters
    const category = searchParams.get('category');
    const featured = searchParams.get('featured') === 'true';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50); // Cap at 50 items
    const search = searchParams.get('search');
    
    // Build query using the products table with category join
    let query = supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        ),
        product_media (
          id,
          url,
          alt_text,
          is_main
        )
      `)
      .eq('status', 'active') // Only show active products
      .order('created_at', { ascending: false });
    
    // Apply filters
    if (category) {
      query = query.eq('category_id', category);
    }
    if (featured) {
      query = query.eq('featured', true);
    }
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }
    
    // Apply pagination
    const start = (page - 1) * limit;
    query = query.range(start, start + limit - 1);
    
    const { data: products, error } = await query;
    
    if (error) {
      console.error('Error fetching products:', error);
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 });
    }
    
    // Get total count for pagination
    const { count: totalCount } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');
    
    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Error in GET products:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Export DELETE handler for deleting products
export async function DELETE(request: NextRequest) {
  try {
    // Access URL params
    const url = new URL(request.url);
    const productId = url.searchParams.get('id');
    
    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Authentication checks - always required for security
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const isUserAdmin = await isAdmin(supabase);
    if (!isUserAdmin) {
      return NextResponse.json({ error: 'Unauthorized. Only admins can delete products.' }, { status: 403 });
    }
    
    // Validate required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    // Create admin client for reliable access
    const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey);
    
    // First delete any associated product media
    const { data: mediaItems } = await adminClient
      .from('product_media')
      .select('id')
      .eq('product_id', productId);
      
    if (mediaItems && mediaItems.length > 0) {
      console.log(`Deleting ${mediaItems.length} media items for product ${productId}`);
      
      // Delete all media items for this product
      const { error: mediaDeleteError } = await adminClient
        .from('product_media')
        .delete()
        .eq('product_id', productId);
        
      if (mediaDeleteError) {
        console.error('Error deleting product media:', mediaDeleteError);
        // Continue with product deletion even if media deletion failed
      }
    }
    
    // Delete the product
    const { error: deleteError } = await adminClient
      .from('products')
      .delete()
      .eq('id', productId);
      
    if (deleteError) {
      console.error('Error deleting product:', deleteError);
      return NextResponse.json(
        { error: `Error deleting product: ${deleteError.message}` },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true, message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error in product DELETE handler:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
}
