import { NextRequest, NextResponse } from 'next/server';

// Get Resend API key from environment variables
const RESEND_API_KEY = process.env.RESEND_API_KEY;
const FROM_EMAIL = '<EMAIL>'; // Replace with your verified domain in Resend

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const { order_number, email, tracking_number, carrier, tracking_url } = await request.json();
    
    // Validate required fields
    if (!order_number || !email || !tracking_number) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Create email HTML content
    const body = `
      <p>Hi there,</p>
      <p>Your order <strong>${order_number}</strong> has been shipped!</p>
      <p>Carrier: ${carrier || '—'}</p>
      <p>Tracking Number: <strong>${tracking_number}</strong></p>
      ${
        tracking_url
          ? `<p><a href="${tracking_url}" target="_blank">Track your shipment</a></p>`
          : ''
      }
      <p>Thank you for shopping with Treasures of Maimi!</p>
    `;
    
    // Check if API key is configured
    if (!RESEND_API_KEY) {
      return NextResponse.json(
        { success: false, error: 'Email service not configured' },
        { status: 500 }
      );
    }

    // Send email using Resend API
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: FROM_EMAIL,
        to: email,
        subject: `Your order ${order_number} is on the way!`,
        html: body,
      }),
    });
    
    // Handle Resend API response
    if (!response.ok) {
      const errorData = await response.json();
      console.error('Resend API error:', errorData);
      return NextResponse.json(
        { success: false, error: errorData },
        { status: 500 }
      );
    }
    
    const responseData = await response.json();
    
    return NextResponse.json({ 
      success: true, 
      message: 'Email sent successfully',
      id: responseData.id 
    });
  } catch (error) {
    console.error('Error sending tracking email:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
