import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/lib/database.types';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

interface CartItem {
  id: string;
  quantity: number;
  product: {
    id: string;
    name: string;
    price: number;
    condition?: string;
    main_image_url?: string;
  };
}

interface ShippingInfo {
  cost: number;
  description?: string;
  estimatedDays?: string;
}

interface DiscountInfo {
  code: string;
  discount: number;
  applied: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { items, customerEmail, shippingInfo, discountInfo, retryPayment, orderId }: {
      items: CartItem[];
      customerEmail: string;
      shippingInfo: ShippingInfo;
      discountInfo?: DiscountInfo | null;
      retryPayment?: boolean;
      orderId?: string;
    } = body;

    // Handle retry payment for existing order
    if (retryPayment && orderId) {
      // Get the existing order details
      const { data: existingOrder, error: orderError } = await supabase
        .from('orders')
        .select(`
          id,
          total_amount,
          customer_email,
          notes,
          order_items(
            quantity,
            price,
            product_id,
            product:products(
              id,
              name,
              condition_id
            )
          )
        `)
        .eq('id', orderId)
        .eq('user_id', session.user.id)
        .in('status', ['pending', 'cancelled']) // Allow retry for both pending and cancelled orders
        .single();

      if (orderError || !existingOrder) {
        return NextResponse.json(
          { error: 'Order not found or access denied' },
          { status: 404 }
        );
      }

      // Validate that order has items
      if (!existingOrder.order_items || existingOrder.order_items.length === 0) {
        return NextResponse.json(
          { error: 'No items found in order' },
          { status: 400 }
        );
      }

      // Create line items from existing order
      const lineItems: Stripe.Checkout.SessionCreateParams.LineItem[] = existingOrder.order_items.map(item => ({
        price_data: {
          currency: 'eur',
          product_data: {
            name: item.product?.name || 'Product',
            description: 'Retry payment for previous order',
          },
          unit_amount: Math.round(item.price * 100), // Convert to cents
        },
        quantity: item.quantity,
      }));

      // Create new Stripe checkout session for retry
      const session_params: Stripe.Checkout.SessionCreateParams = {
        payment_method_types: ['card'],
        line_items: lineItems,
        mode: 'payment',
        customer_email: existingOrder.customer_email || undefined,
        success_url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://treasuresofmaimi.com'}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://treasuresofmaimi.com'}/checkout/incomplete?session_id={CHECKOUT_SESSION_ID}`,
        metadata: {
          user_id: session.user.id,
          order_id: existingOrder.id,
          customer_email: existingOrder.customer_email,
          retry_payment: 'true',
        },
        shipping_address_collection: {
          allowed_countries: ['ES', 'FR', 'DE', 'IT', 'GB', 'US', 'CA', 'AU', 'JP'],
        },
        billing_address_collection: 'required',
      };

      const stripeSession = await stripe.checkout.sessions.create(session_params);

      // Update order status back to pending and awaiting payment
      const { error: updateError } = await supabase
        .from('orders')
        .update({
          session_id: stripeSession.id,
          status: 'pending',
          payment_status: 'awaiting_payment',
          updated_at: new Date().toISOString(),
          status_updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (updateError) {
        console.error('Error updating order for retry:', updateError);
        return NextResponse.json(
          { error: 'Failed to update order for retry' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        url: stripeSession.url,
        sessionId: stripeSession.id,
        orderId: existingOrder.id,
      });
    }

    // Validate required fields for new orders
    if (!items || items.length === 0) {
      return NextResponse.json(
        { error: 'Cart items are required' },
        { status: 400 }
      );
    }

    if (!customerEmail) {
      return NextResponse.json(
        { error: 'Customer email is required' },
        { status: 400 }
      );
    }

    // Calculate totals
    const subtotal = items.reduce((acc, item) => acc + (item.product.price * item.quantity), 0);
    const shippingCost = shippingInfo?.cost || 0;
    const discountAmount = discountInfo?.applied ? discountInfo.discount : 0;
    const total = Math.max(0, subtotal + shippingCost - discountAmount);

    // Create line items for Stripe
    const lineItems: Stripe.Checkout.SessionCreateParams.LineItem[] = items.map(item => ({
      price_data: {
        currency: 'eur',
        product_data: {
          name: item.product.name,
          description: item.product.condition ? `Condition: ${item.product.condition}` : undefined,
          images: item.product.main_image_url ? [item.product.main_image_url] : undefined,
        },
        unit_amount: Math.round(item.product.price * 100), // Convert to cents
      },
      quantity: item.quantity,
    }));

    // Add shipping as a line item if there's a cost
    if (shippingCost > 0) {
      lineItems.push({
        price_data: {
          currency: 'eur',
          product_data: {
            name: 'Shipping',
            description: shippingInfo.description || 'Standard shipping',
          },
          unit_amount: Math.round(shippingCost * 100), // Convert to cents
        },
        quantity: 1,
      });
    }

    // Handle discounts differently - Stripe doesn't allow negative line items
    // We'll create a dynamic discount directly in the session

    // First create order with 'processing' status (payment not yet confirmed)
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .insert([
        {
          user_id: session.user.id,
          status: 'pending',
          total_amount: total,
          payment_provider: 'stripe',
          payment_status: 'awaiting_payment',
          customer_email: customerEmail,
          notes: discountInfo?.applied
            ? `Discount applied: ${discountInfo.code} (-€${discountAmount.toFixed(2)})`
            : null,
        }
      ])
      .select()
      .single();

    if (orderError) {
      console.error('Error creating order:', orderError);
      return NextResponse.json(
        { error: 'Failed to create order' },
        { status: 500 }
      );
    }

    // Create Stripe checkout session with order ID in metadata
    const session_params: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      customer_email: customerEmail,
      success_url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://treasuresofmaimi.com'}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://treasuresofmaimi.com'}/checkout/incomplete?session_id={CHECKOUT_SESSION_ID}`,
      metadata: {
        user_id: session.user.id,
        order_id: orderData.id,
        customer_email: customerEmail,
        discount_code: discountInfo?.code || '',
        discount_amount: discountAmount.toString(),
      },
      shipping_address_collection: {
        allowed_countries: ['ES', 'FR', 'DE', 'IT', 'GB', 'US', 'CA', 'AU', 'JP'],
      },
      billing_address_collection: 'required',
    };

    // Add discount via Stripe coupons if discount is applied
    if (discountInfo?.applied && discountAmount > 0) {
      try {
        // Create a one-time discount coupon
        const coupon = await stripe.coupons.create({
          amount_off: Math.round(discountAmount * 100), // Convert to cents
          currency: 'eur',
          duration: 'once',
          name: `Discount: ${discountInfo.code}`,
          metadata: {
            original_code: discountInfo.code,
            created_for_session: 'true'
          }
        });

        session_params.discounts = [{
          coupon: coupon.id
        }];

        console.log('Created Stripe coupon for discount:', coupon.id);
      } catch (couponError) {
        console.error('Error creating Stripe coupon for discount:', couponError);
        // Continue without the coupon - the discount is already calculated in the total
      }
    }

    // Add discount information to metadata for display purposes
    // (The discount is already calculated in the total above)
    if (discountInfo?.applied && discountAmount > 0) {
      session_params.metadata!.discount_applied = 'true';
      session_params.metadata!.original_subtotal = subtotal.toString();
    }

    const stripeSession = await stripe.checkout.sessions.create(session_params);

    // Update order with Stripe session ID
    const { error: updateError } = await supabase
      .from('orders')
      .update({ session_id: stripeSession.id })
      .eq('id', orderData.id);

    if (updateError) {
      console.error('Error updating order with session ID:', updateError);
      return NextResponse.json(
        { error: 'Failed to update order' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      url: stripeSession.url,
      sessionId: stripeSession.id,
      orderId: orderData?.id,
    });

  } catch (error) {
    console.error('Stripe checkout error:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      type: error instanceof Error ? error.constructor.name : typeof error
    });
    return NextResponse.json(
      {
        error: 'Failed to create checkout session',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
