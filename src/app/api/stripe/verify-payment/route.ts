import { NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

export async function GET(request: Request) {
  // Get the session_id and order_id from the URL
  const url = new URL(request.url);
  const sessionId = url.searchParams.get('session_id');
  const orderId = url.searchParams.get('order_id');

  // Validate parameters
  if (!sessionId) {
    return NextResponse.json({ error: 'Missing session_id parameter' }, { status: 400 });
  }

  if (!orderId) {
    return NextResponse.json({ error: 'Missing order_id parameter' }, { status: 400 });
  }

  try {
    // Retrieve the session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    console.log('Stripe session payment_status:', session.payment_status);
    console.log('Stripe session ID:', sessionId);

    // Get the Supabase client
    const supabase = createServerComponentClient({ cookies });

    // Check if the session is paid
    if (session.payment_status === 'paid') {
      // Update the order status in the database if it's not already paid
      const { data, error } = await supabase
        .from('orders')
        .update({
          status: 'completed',
          payment_status: 'completed',
          payment_intent: session.payment_intent as string,
          status_updated_at: new Date().toISOString(),
          session_id: sessionId
        })
        .eq('id', orderId)
        .in('status', ['pending', 'created']) // Update if still pending or created
        .select('id, status, payment_status');

      if (error) {
        console.error('Error updating order status:', JSON.stringify(error, null, 2));
        return NextResponse.json({ error: 'Error updating order status' }, { status: 500 });
      }

      // If no rows were updated, the order might already be paid
      if (data && data.length === 0) {
        // Check if the order exists and is already paid
        const { data: existingOrder, error: fetchError } = await supabase
          .from('orders')
          .select('id, status, payment_status')
          .eq('id', orderId)
          .single();

        if (fetchError) {
          console.error('Error fetching order:', fetchError);
          return NextResponse.json({ error: 'Error fetching order' }, { status: 500 });
        }

        if (!existingOrder) {
          return NextResponse.json({ error: 'Order not found' }, { status: 404 });
        }

        // If the order exists but is already paid, that's fine
        if (existingOrder.status === 'paid' || existingOrder.payment_status === 'completed') {
          return NextResponse.json({ 
            success: true, 
            message: 'Order already marked as paid',
            order: existingOrder
          });
        }
      }

      console.log('Payment verified successfully for order:', orderId);

      return NextResponse.json({ 
        success: true, 
        message: 'Payment verified and order updated',
        session_id: sessionId,
        order_id: orderId
      });
    } else {
      // The payment is not complete
      return NextResponse.json({ 
        success: false, 
        message: 'Payment not complete', 
        payment_status: session.payment_status 
      }, { status: 402 });
    }
  } catch (error: any) {
    console.error('Error verifying payment:', error);
    return NextResponse.json({ 
      error: 'Error verifying payment', 
      message: error.message 
    }, { status: 500 });
  }
}
