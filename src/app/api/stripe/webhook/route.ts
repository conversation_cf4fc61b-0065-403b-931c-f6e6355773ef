import { headers } from 'next/headers';
import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import * as Sentry from '@sentry/nextjs';
import { unifiedNotificationService } from '@/lib/unified-notifications';

// Validate Stripe key at module level
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY environment variable is required');
}

// Initialize Stripe 
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-02-24.acacia',
});

export async function POST(request: Request) {
  return Sentry.withServerActionInstrumentation('stripe-webhook', async () => {
    try {
      // Runtime environment variable checks
      const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
      const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

      if (!stripeSecretKey) {
        console.error('❌ STRIPE_SECRET_KEY is not defined');
        return NextResponse.json(
          { error: 'Server configuration error' },
          { status: 500 }
        );
      }

      if (!stripeWebhookSecret) {
        console.error('❌ STRIPE_WEBHOOK_SECRET is not defined');
        return NextResponse.json(
          { error: 'Server configuration error' },
          { status: 500 }
        );
      }

      // Initialize Stripe with the actual key
      const stripeClient = new Stripe(stripeSecretKey, {
        apiVersion: '2025-02-24.acacia',
      });

      const body = await request.text();
      const signature = headers().get('stripe-signature');

      if (!signature) {
        console.error('⚠️ No Stripe signature found in webhook request');
        return NextResponse.json(
          { error: 'No signature provided' },
          { status: 400 }
        );
      }

      let event: Stripe.Event;

      try {
        // Verify the webhook signature
        event = stripeClient.webhooks.constructEvent(
          body,
          signature,
          stripeWebhookSecret
        );
      } catch (err: any) {
        console.error('⚠️ Webhook signature verification failed:', err.message);
        return NextResponse.json(
          { error: `Webhook Error: ${err.message}` },
          { status: 400 }
        );
      }

      // Get Supabase client
      const supabase = createServerComponentClient({ cookies });

      // Handle specific event types
      switch (event.type) {
        case 'checkout.session.completed': {
          const session = event.data.object as Stripe.Checkout.Session;
          console.log('💰 Payment successful for session:', session.id);

          // Extract order details from metadata
          const orderId = session.metadata?.order_id;
          const customerEmail = session.metadata?.customer_email;

          console.log('🔍 Debug: Session metadata:', session.metadata);
          console.log('🔍 Debug: Order ID:', orderId);

          if (!orderId) {
            console.error('❌ No order ID found in session metadata');
            throw new Error('No order ID found in session metadata');
          }

          // Calculate total amount paid (including shipping)
          const amountTotal = session.amount_total ? session.amount_total / 100 : 0;

          // Format shipping address from Stripe session
          const shippingAddress = session.shipping_details ? {
            name: session.shipping_details.name,
            address: {
              street: session.shipping_details.address?.line1,
              street2: session.shipping_details.address?.line2,
              city: session.shipping_details.address?.city,
              state: session.shipping_details.address?.state,
              postal_code: session.shipping_details.address?.postal_code,
              country: session.shipping_details.address?.country,
            }
          } : null;

          console.log('📦 Shipping details:', shippingAddress);

          // Update order status from 'pending' to 'processing' (payment confirmed, order can be fulfilled)
          console.log('🔄 Attempting to update order:', orderId);
          const { data: updateResult, error: updateError } = await supabase
            .from('orders')
            .update({
              status: 'processing',
              payment_status: 'completed',
              payment_intent: session.payment_intent as string,
              total_amount: amountTotal,
              updated_at: new Date().toISOString(),
              status_updated_at: new Date().toISOString(),
              customer_email: customerEmail || session.customer_details?.email
              // shipping_address: shippingAddress, // Commented out until column is added
              // payment_method: session.payment_method_types?.[0] || 'card' // Commented out until column is added
            })
            .eq('id', orderId)
            .eq('status', 'pending') // Only update if still pending (payment not confirmed)
            .select();

          if (updateError) {
            console.error('❌ Error updating order status:', updateError);
            throw updateError;
          }

          console.log('✅ Order update result:', updateResult);
          console.log('✅ Orders updated:', updateResult?.length || 0);

          // Save shipping address to shipping_addresses table for future use
          if (shippingAddress && session.customer_details?.email) {
            try {
              const { data: userData } = await supabase
                .from('profiles')
                .select('id')
                .eq('email', session.customer_details.email)
                .single();

              if (userData?.id) {
                await supabase
                  .from('shipping_addresses')
                  .insert({
                    user_id: userData.id,
                    name: shippingAddress.name,
                    street: shippingAddress.address.street,
                    city: shippingAddress.address.city,
                    state: shippingAddress.address.state,
                    postal_code: shippingAddress.address.postal_code,
                    country: shippingAddress.address.country,
                    created_at: new Date().toISOString()
                  })
                  .select()
                  .single();
              }
            } catch (addressError) {
              // Log but don't fail the webhook on address save error
              console.error('📮 Error saving shipping address:', addressError);
            }
          }

          console.log('✅ Order status updated successfully:', orderId);

          // Send admin notification for new order
          try {
            await unifiedNotificationService.sendNewOrderNotification({
              order_id: orderId,
              order_number: orderId, // You might want to generate a human-readable order number
              total_amount: amountTotal,
              customer_email: customerEmail || session.customer_details?.email || 'Unknown'
            });
            console.log('📧 New order notification sent to admin');
          } catch (notificationError) {
            console.error('❌ Error sending new order notification:', notificationError);
          }

          // Optional: Send confirmation email with shipping details
          try {
            // Add your email sending logic here
            // await sendOrderConfirmationEmail(customerEmail, orderId, shippingAddress);
          } catch (emailError) {
            // Log but don't fail the webhook on email error
            console.error('📧 Error sending confirmation email:', emailError);
          }

          break;
        }

        case 'checkout.session.expired': {
          const session = event.data.object as Stripe.Checkout.Session;
          console.log('⏰ Checkout session expired:', session.id);

          const orderId = session.metadata?.order_id;
          if (orderId) {
            // Update order status from 'pending' to 'cancelled' (session expired/incomplete)
            const { error: updateError } = await supabase
              .from('orders')
              .update({
                status: 'cancelled',
                payment_status: 'incomplete',
                updated_at: new Date().toISOString(),
                status_updated_at: new Date().toISOString()
              })
              .eq('id', orderId)
              .eq('status', 'pending'); // Only update if still pending

            if (updateError) {
              console.error('❌ Error updating expired order:', updateError);
              throw updateError;
            }

            console.log('✅ Order marked as expired:', orderId);
          }
          break;
        }

        case 'payment_intent.payment_failed': {
          const paymentIntent = event.data.object as Stripe.PaymentIntent;
          console.log('❌ Payment failed for intent:', paymentIntent.id);

          // Find order by payment intent
          const { data: orders, error: findError } = await supabase
            .from('orders')
            .select('id')
            .eq('payment_intent', paymentIntent.id)
            .single();

          if (findError) {
            console.error('❌ Error finding order for failed payment:', findError);
            // Don't throw here as this might be expected for some payment flows
          }

          if (orders?.id) {
            // Update order status from 'pending' to 'cancelled' (payment failed)
            const { error: updateError } = await supabase
              .from('orders')
              .update({
                status: 'cancelled',
                payment_status: 'failed',
                updated_at: new Date().toISOString(),
                status_updated_at: new Date().toISOString()
              })
              .eq('id', orders.id)
              .eq('status', 'pending'); // Only update if still pending

            if (updateError) {
              console.error('❌ Error updating failed order:', updateError);
              throw updateError;
            }

            console.log('✅ Order marked as failed:', orders.id);
          }
          break;
        }

        // Add more event types as needed
        default:
          console.log(`🤔 Unhandled event type: ${event.type}`);
      }

      return NextResponse.json({ received: true });
    } catch (err: any) {
      Sentry.captureException(err);
      console.error('❌ Error processing webhook:', err);
      return NextResponse.json(
        { 
          error: 'Webhook handler failed',
          message: err.message 
        },
        { status: 500 }
      );
    }
  });
}
