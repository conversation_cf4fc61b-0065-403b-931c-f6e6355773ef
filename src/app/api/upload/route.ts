import { NextRequest, NextResponse } from 'next/server';
import { v2 as cloudinary } from 'cloudinary';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// Define types for image upload results
type UploadResult = {
  image: {
    publicId: string;
    url: string;
    width: number;
    height: number;
  };
  model_3d?: {
    publicId: string;
    url: string;
    width: number;
    height: number;
  };
  ar?: {
    publicId: string;
    url: string;
    width: number;
    height: number;
  };
};

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export async function POST(request: NextRequest) {
  // Check authentication
  const supabase = createRouteHandlerClient({ cookies });
  const { data: { session } } = await supabase.auth.getSession();
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Verify admin role
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', session.user.id)
    .single();
    
  if (!profile || profile.role !== 'admin') {
    return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 });
  }
  
  try {
    const data = await request.json();
    const { images, generate3D = false, generateAR = false, folder = 'products' } = data;
    
    if (!images || !Array.isArray(images) || images.length === 0) {
      return NextResponse.json({ error: 'No images provided' }, { status: 400 });
    }
    
    console.log(`Processing ${images.length} images with options: 3D=${generate3D}, AR=${generateAR}`);
    
    // Process each image
    const results: UploadResult[] = [];
    
    for (const imageData of images) {
      try {
        // Upload the base image to Cloudinary
        const uploadResult = await cloudinary.uploader.upload(imageData, {
          folder,
          transformation: [
            { quality: 'auto', fetch_format: 'auto' }
          ]
        });
        
        console.log(`Uploaded image: ${uploadResult.public_id}`);
        
        if (!uploadResult || !uploadResult.secure_url) {
          console.error('Invalid upload result from Cloudinary:', uploadResult);
          continue; // Skip this image if upload failed
        }
        
        const result: UploadResult = {
          image: {
            publicId: uploadResult.public_id,
            url: uploadResult.secure_url,
            width: uploadResult.width,
            height: uploadResult.height
          }
        };
        
        // Generate 3D model if requested
        if (generate3D) {
          try {
            const model3dResponse = await fetch(new URL('/api/generate-3d', request.url).toString(), {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Cookie': request.headers.get('cookie') || '' // Pass authentication cookies
              },
              body: JSON.stringify({ publicId: uploadResult.public_id })
            });
            
            if (model3dResponse.ok) {
              const model3dData = await model3dResponse.json();
              if (model3dData.success && model3dData.model_3d && model3dData.model_3d.url) {
                result.model_3d = model3dData.model_3d;
                console.log(`Generated 3D model: ${model3dData.model_3d.publicId}`);
              } else {
                console.warn('3D model generation succeeded but returned invalid data:', model3dData);
              }
            } else {
              console.error('Failed to generate 3D model:', await model3dResponse.text());
            }
          } catch (error) {
            console.error('Error generating 3D model:', error);
          }
        }
        
        // Generate AR experience if requested
        if (generateAR) {
          try {
            const arResponse = await fetch(new URL('/api/generate-ar', request.url).toString(), {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Cookie': request.headers.get('cookie') || '' // Pass authentication cookies
              },
              body: JSON.stringify({ publicId: uploadResult.public_id })
            });
            
            if (arResponse.ok) {
              const arData = await arResponse.json();
              if (arData.success && arData.ar && arData.ar.url) {
                result.ar = arData.ar;
                console.log(`Generated AR experience: ${arData.ar.publicId}`);
              } else {
                console.warn('AR generation succeeded but returned invalid data:', arData);
              }
            } else {
              console.error('Failed to generate AR experience:', await arResponse.text());
            }
          } catch (error) {
            console.error('Error generating AR experience:', error);
          }
        }
        
        results.push(result);
      } catch (imageError) {
        console.error('Error processing individual image:', imageError);
        // Continue with other images even if one fails
      }
    }
    
    if (results.length === 0) {
      return NextResponse.json({ error: 'No images were processed successfully' }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      results
    });
  } catch (error) {
    console.error('Error processing upload:', error);
    return NextResponse.json(
      { error: 'Failed to process upload', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
