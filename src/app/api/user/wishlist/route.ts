import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(req: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    console.log('User Wishlist API: GET request received');
    
    // Get the current session
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.error('User Wishlist API: Authentication error', authError);
      return NextResponse.json(
        { error: 'Authentication error', details: authError }, 
        { status: 401 }
      );
    }
    
    if (!session) {
      console.error('User Wishlist API: No session found');
      return NextResponse.json(
        { error: 'Authentication required - no session found' }, 
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    console.log('User Wishlist API: Authenticated user ID:', userId);
    
    // Get wishlist items with product details, using user ID from session
    const { data: wishlistItems, error: fetchError } = await supabase
      .from('wishlist')
      .select(`
        id,
        product_id,
        created_at,
        user_id,
        products:product_id (
          id,
          name,
          price,
          slug,
          category,
          product_media (
            id,
            url,
            position
          )
        )
      `)
      .eq('user_id', userId);
      
    if (fetchError) {
      console.error('User Wishlist API: Error fetching wishlist items', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch wishlist items', details: fetchError }, 
        { status: 500 }
      );
    }
    
    console.log(`User Wishlist API: Found ${wishlistItems?.length || 0} items for user ${userId}`);
    
    // Process the items to ensure they have the expected structure
    const processedItems = wishlistItems?.map(item => ({
      id: item.id,
      product_id: item.product_id,
      user_id: item.user_id,
      created_at: item.created_at,
      product: {
        id: Array.isArray(item.products) ? item.products[0]?.id : null,
        name: Array.isArray(item.products) ? item.products[0]?.name : null,
        price: Array.isArray(item.products) ? item.products[0]?.price : null,
        image_url: Array.isArray(item.products) ? item.products[0]?.product_media?.[0]?.url : null,
        slug: Array.isArray(item.products) ? item.products[0]?.slug : null
      }
    })) || [];
    
    return NextResponse.json({
      userId,
      count: processedItems.length,
      items: processedItems
    }, { status: 200 });
    
  } catch (error) {
    console.error('User Wishlist API: Unexpected error', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error }, 
      { status: 500 }
    );
  }
} 