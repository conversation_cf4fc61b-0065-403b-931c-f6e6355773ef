import { NextResponse } from 'next/server';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(
  request: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const { productId } = params;
    const supabase = createServerComponentClient({ cookies });

    // Fetch product details
    const { data: product, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        description,
        condition,
        created_at,
        serial_number
      `)
      .eq('id', productId)
      .single();

    if (error) throw error;

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      verified: true,
      product: {
        id: product.id,
        name: product.name,
        description: product.description,
        condition: product.condition,
        issueDate: product.created_at,
        serialNumber: product.serial_number || product.id.slice(0, 8).toUpperCase()
      }
    });
  } catch (error) {
    console.error('Error verifying product:', error);
    return NextResponse.json(
      { error: 'Failed to verify product' },
      { status: 500 }
    );
  }
}
