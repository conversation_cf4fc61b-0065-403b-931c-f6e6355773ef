import { NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

// This is your Stripe webhook secret for testing your endpoint locally
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Helper function to update product quantities
async function updateProductQuantities(supabase: any, orderId: string) {
  try {
    // Get order items
    const { data: orderItems, error: itemsError } = await supabase
      .from('order_items')
      .select('product_id, quantity')
      .eq('order_id', orderId);

    if (itemsError) {
      console.error('Error fetching order items:', itemsError);
      return;
    }

    // Update each product's quantity securely without raw SQL
    for (const item of orderItems) {
      // First get the current product data
      const { data: currentProduct, error: fetchError } = await supabase
        .from('products')
        .select('quantity, status')
        .eq('id', item.product_id)
        .single();

      if (fetchError) {
        console.error(`Error fetching product ${item.product_id}:`, fetchError);
        continue;
      }

      if (!currentProduct) {
        console.error(`Product ${item.product_id} not found`);
        continue;
      }

      // Calculate new quantity safely
      const newQuantity = Math.max(0, currentProduct.quantity - item.quantity);
      const newStatus = newQuantity <= 0 ? 'sold_out' : currentProduct.status;

      // Update with calculated values
      const { error: updateError } = await supabase
        .from('products')
        .update({ 
          quantity: newQuantity,
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.product_id);

      if (updateError) {
        console.error(`Error updating product ${item.product_id} quantity:`, updateError);
      } else {
        console.log(`Successfully updated product ${item.product_id}: quantity ${currentProduct.quantity} -> ${newQuantity}`);
      }
    }
  } catch (error) {
    console.error('Error in updateProductQuantities:', error);
  }
}

export async function POST(request: Request) {
  const body = await request.text();
  const sig = request.headers.get('stripe-signature') as string;
  
  let event: Stripe.Event;
  
  try {
    // Verify the event came from Stripe
    if (!endpointSecret) {
      console.warn('Stripe webhook secret not set. Skipping signature verification.');
      event = JSON.parse(body) as Stripe.Event;
    } else {
      event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
    }
  } catch (err: any) {
    console.error(`Webhook Error: ${err.message}`);
    return NextResponse.json({ error: `Webhook Error: ${err.message}` }, { status: 400 });
  }
  
  // Get the Supabase client
  const supabase = createServerComponentClient({ cookies });
  
  // Handle the event
  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        
        // Update the order in the database
        if (session.metadata?.orderId) {
          // First try to get the user ID from metadata
          let userId = session.metadata?.user_id;

          // If no user_id in metadata, try to get it from customer email
          if (!userId && session.customer_details?.email) {
            console.log('Attempting to find user by email:', session.customer_details.email);
            const { data: userData } = await supabase
              .from('profiles')
              .select('id')
              .eq('email', session.customer_details.email)
              .single();
            
            if (userData?.id) {
              console.log('Found user ID from email:', userData.id);
              userId = userData.id;
            } else {
              console.warn('No user found for email:', session.customer_details.email);
            }
          }

          if (!userId) {
            console.warn('Could not determine user_id from either metadata or email');
          }
          
          const { error } = await supabase
            .from('orders')
            .update({ 
              status: 'paid',
              payment_status: 'completed',
              payment_intent: session.payment_intent as string,
              user_id: userId,  // Will be the found user ID or null
              updated_at: new Date().toISOString()
            })
            .eq('id', session.metadata.orderId);
          
          if (error) {
            console.error('Error updating order status:', error);
            return NextResponse.json({ error: 'Error updating order status' }, { status: 500 });
          }
          
          // Update product quantities
          await updateProductQuantities(supabase, session.metadata.orderId);
          
          console.log(`Order ${session.metadata.orderId} marked as paid for user ${userId}`);
        } else {
          // No orderId in metadata, create a new order
          // First try to get the user ID from metadata
          let userId = session.metadata?.user_id;

          // If no user_id in metadata, try to get it from customer email
          if (!userId && session.customer_details?.email) {
            console.log('Attempting to find user by email:', session.customer_details.email);
            const { data: userData } = await supabase
              .from('profiles')
              .select('id')
              .eq('email', session.customer_details.email)
              .single();
            
            if (userData?.id) {
              console.log('Found user ID from email:', userData.id);
              userId = userData.id;
            } else {
              console.warn('No user found for email:', session.customer_details.email);
            }
          }

          const { data: newOrder, error: insertError } = await supabase
            .from('orders')
            .insert({
              user_id: userId || null,
              payment_intent: session.payment_intent as string,
              total_amount: session.amount_total ? session.amount_total / 100 : 0, // Convert from cents to euros
              payment_provider: 'stripe',
              status: 'paid',
              payment_status: 'completed',
              created_at: new Date().toISOString(),
            })
            .select()
            .single();
        
          if (insertError) {
            console.error('Error inserting new order:', insertError);
            return NextResponse.json({ error: 'Error inserting new order' }, { status: 500 });
          }

          // Get line items from the session
          const lineItems = await stripe.checkout.sessions.listLineItems(session.id, {
            expand: ['data.price.product']
          });
          
          // Check if line items are empty
          if (lineItems.data.length === 0) {
            console.warn('[Webhook] No line items returned from Stripe for session:', session.id);
          }

          // Insert order items
          if (lineItems.data.length > 0) {
            // Filter out shipping line item and get product line items
            const productLineItems = lineItems.data.filter(item => {
              const product = item.price?.product as Stripe.Product;
              return product?.metadata?.product_id; // Only include items with our product ID
            });

            console.log(`[Webhook] Found ${productLineItems.length} product line items`);

            const orderItems = productLineItems.map(item => {
              const stripeProduct = item.price?.product as Stripe.Product;
              const productId = stripeProduct?.metadata?.product_id;
              
              console.log(`[Webhook] Processing line item:`, {
                stripeProductId: stripeProduct?.id,
                internalProductId: productId,
                name: stripeProduct?.name,
                quantity: item.quantity,
                price: item.price?.unit_amount
              });
              
              if (!productId) {
                console.error('[Webhook] No product_id found in metadata for item:', stripeProduct?.id);
                return null;
              }
              
              return {
                order_id: newOrder.id,
                product_id: productId,
                quantity: item.quantity || 1,
                price: (item.price?.unit_amount || 0) / 100, // Convert from cents to euros
                created_at: new Date().toISOString()
              };
            }).filter(Boolean); // Remove any null items

            console.log('[Webhook] Order items to insert:', JSON.stringify(orderItems, null, 2));

            if (orderItems.length > 0) {
              const { error: itemsError } = await supabase
                .from('order_items')
                .insert(orderItems);

              if (itemsError) {
                console.error('Error inserting order items:', itemsError);
                // Don't return error response here, as the order was already created
              } else {
                console.log(`Successfully inserted ${orderItems.length} items for order ${newOrder.id}`);
                
                // Log the inserted items for verification
                const { data: insertedItems, error: fetchError } = await supabase
                  .from('order_items')
                  .select('*')
                  .eq('order_id', newOrder.id);
                  
                if (fetchError) {
                  console.error('Error fetching inserted items:', fetchError);
                } else {
                  console.log('Inserted items:', JSON.stringify(insertedItems, null, 2));
                }
              }

              // Update product quantities
              await updateProductQuantities(supabase, newOrder.id);
            } else {
              console.error('[Webhook] No valid order items to insert after filtering');
            }
          }
        
          console.log(`Inserted new order with ID ${newOrder.id} for user ${userId || 'unknown'}`);
        }
        break;
      }
      
      case 'checkout.session.expired': {
        const session = event.data.object as Stripe.Checkout.Session;
        
        // Update the order in the database
        if (session.metadata?.orderId) {
          const { error } = await supabase
            .from('orders')
            .update({ 
              status: 'cancelled',
              payment_status: 'failed',
              updated_at: new Date().toISOString()
            })
            .eq('id', session.metadata.orderId);
          
          if (error) {
            console.error('Error updating order status:', error);
            return NextResponse.json({ error: 'Error updating order status' }, { status: 500 });
          }
          
          console.log(`Order ${session.metadata.orderId} marked as cancelled (expired)`);
        }
        break;
      }
      
      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        
        // Find the order by payment intent
        const { data: order, error: findError } = await supabase
          .from('orders')
          .select('id')
          .eq('payment_intent', paymentIntent.id)
          .single();
          
        if (findError) {
          console.error('Error finding order:', findError);
          break;
        }
        
        if (order) {
          const { error: updateError } = await supabase
            .from('orders')
            .update({
              status: 'failed',
              payment_status: 'failed',
              updated_at: new Date().toISOString()
            })
            .eq('id', order.id);
            
          if (updateError) {
            console.error('Error updating order status:', updateError);
            return NextResponse.json({ error: 'Error updating order status' }, { status: 500 });
          }
          
          console.log(`Order ${order.id} marked as failed`);
        }
        break;
      }
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
    
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json({ error: 'Error processing webhook' }, { status: 500 });
  }
}
