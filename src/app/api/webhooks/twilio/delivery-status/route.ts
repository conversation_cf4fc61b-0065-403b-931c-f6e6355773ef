import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role for webhook operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    console.log('📞 Twilio Delivery Status Webhook received');

    // Parse the form data from Twilio
    const formData = await request.formData();
    const webhookData: Record<string, string> = {};
    
    for (const [key, value] of formData.entries()) {
      webhookData[key] = value.toString();
    }

    console.log('📊 Delivery Status Data:', webhookData);

    // Extract important fields from Twilio webhook
    const {
      MessageSid,
      MessageStatus,
      To,
      From,
      ErrorCode,
      ErrorMessage,
      SmsSid,
      SmsStatus,
      AccountSid
    } = webhookData;

    // Validate that this is from our Twilio account
    if (AccountSid !== process.env.TWILIO_ACCOUNT_SID) {
      console.error('❌ Invalid Twilio Account SID');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Log the delivery status
    const status = MessageStatus || SmsStatus;
    const messageId = MessageSid || SmsSid;

    console.log(`📱 Message ${messageId} to ${To}: ${status}`);

    // Store delivery status in database (optional - for tracking)
    try {
      const { error: logError } = await supabase
        .from('notification_logs')
        .insert({
          type: 'sms_delivery_status',
          recipient: To,
          status: status,
          message_id: messageId,
          error_code: ErrorCode || null,
          error_message: ErrorMessage || null,
          metadata: {
            from: From,
            twilio_data: webhookData
          },
          created_at: new Date().toISOString()
        });

      if (logError) {
        console.error('Error logging delivery status:', logError);
      }
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Don't fail the webhook for database errors
    }

    // Handle different delivery statuses
    switch (status) {
      case 'delivered':
        console.log('✅ Message delivered successfully');
        break;
      case 'failed':
        console.log('❌ Message delivery failed:', ErrorMessage);
        break;
      case 'undelivered':
        console.log('⚠️ Message undelivered:', ErrorMessage);
        break;
      case 'sent':
        console.log('📤 Message sent to carrier');
        break;
      case 'queued':
        console.log('⏳ Message queued for delivery');
        break;
      default:
        console.log(`📋 Message status: ${status}`);
    }

    // Respond to Twilio with success
    return NextResponse.json({ 
      success: true, 
      message: 'Delivery status processed',
      status: status,
      messageId: messageId
    });

  } catch (error) {
    console.error('❌ Error processing Twilio delivery status webhook:', error);
    
    // Return success to Twilio even on errors to prevent retries
    // Log the error but don't fail the webhook
    return NextResponse.json({ 
      success: true, 
      message: 'Webhook received but processing failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// Handle GET requests for webhook verification
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    message: 'Twilio Delivery Status Webhook Endpoint',
    status: 'active',
    timestamp: new Date().toISOString()
  });
}
