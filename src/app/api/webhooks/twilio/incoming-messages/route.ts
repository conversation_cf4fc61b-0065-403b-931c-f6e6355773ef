import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role for webhook operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    console.log('📞 Twilio Incoming Message Webhook received');

    // Parse the form data from Twilio
    const formData = await request.formData();
    const webhookData: Record<string, string> = {};
    
    for (const [key, value] of formData.entries()) {
      webhookData[key] = value.toString();
    }

    console.log('📨 Incoming Message Data:', webhookData);

    // Extract important fields from Twilio webhook
    const {
      MessageSid,
      From,
      To,
      Body,
      NumMedia,
      AccountSid,
      SmsSid,
      SmsMessageSid
    } = webhookData;

    // Validate that this is from our Twilio account
    if (AccountSid !== process.env.TWILIO_ACCOUNT_SID) {
      console.error('❌ Invalid Twilio Account SID');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const messageId = MessageSid || SmsSid || SmsMessageSid;
    const messageBody = Body || '';
    const mediaCount = parseInt(NumMedia || '0');

    console.log(`📱 Incoming message from ${From} to ${To}: "${messageBody}"`);

    // Store incoming message in database
    try {
      const { error: logError } = await supabase
        .from('notification_logs')
        .insert({
          type: 'incoming_message',
          recipient: To,
          sender: From,
          message: messageBody,
          message_id: messageId,
          status: 'received',
          metadata: {
            media_count: mediaCount,
            twilio_data: webhookData
          },
          created_at: new Date().toISOString()
        });

      if (logError) {
        console.error('Error logging incoming message:', logError);
      }
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Don't fail the webhook for database errors
    }

    // Check if this is a customer service inquiry
    const isCustomerInquiry = messageBody.toLowerCase().includes('order') || 
                             messageBody.toLowerCase().includes('help') ||
                             messageBody.toLowerCase().includes('support') ||
                             messageBody.toLowerCase().includes('question');

    if (isCustomerInquiry) {
      console.log('🎯 Detected customer service inquiry');
      
      // Create a notification for admin about the customer inquiry
      try {
        // Get admin users to send notifications to
        const { data: adminUsers } = await supabase
          .from('profiles')
          .select('id')
          .eq('is_admin', true);

        if (adminUsers && adminUsers.length > 0) {
          // Create notifications for all admin users
          const notifications = adminUsers.map(admin => ({
            user_id: admin.id,
            type: 'new_message',
            title: 'New Customer Message',
            message: `Customer inquiry from ${From}: "${messageBody}"`,
            data: {
              phone_number: From,
              message: messageBody,
              message_id: messageId,
              source: 'sms'
            },
            priority: 'high'
          }));

          const { error: notificationError } = await supabase
            .from('notifications')
            .insert(notifications);

          if (notificationError) {
            console.error('Error creating customer inquiry notifications:', notificationError);
          } else {
            console.log(`✅ Created customer inquiry notifications for ${adminUsers.length} admin(s)`);
          }
        } else {
          console.log('⚠️ No admin users found to notify');
        }
      } catch (error) {
        console.error('Error handling customer inquiry:', error);
      }
    }

    // Auto-reply with basic information (optional)
    let responseMessage = '';
    
    if (messageBody.toLowerCase().includes('hours') || messageBody.toLowerCase().includes('open')) {
      responseMessage = `🕒 Our business hours are Monday-Friday 9AM-6PM. For immediate assistance, please visit our website at treasuresofmaimi.com or email us.`;
    } else if (messageBody.toLowerCase().includes('order') && messageBody.toLowerCase().includes('status')) {
      responseMessage = `📦 To check your order status, please visit treasuresofmaimi.com and log into your account. For urgent order inquiries, please email us with your order number.`;
    } else if (isCustomerInquiry) {
      responseMessage = `👋 Thank you for contacting Treasures of Maimi! We've received your message and will respond as soon as possible. For immediate assistance, visit treasuresofmaimi.com`;
    }

    // Prepare TwiML response
    let twimlResponse = '<?xml version="1.0" encoding="UTF-8"?><Response>';
    
    if (responseMessage) {
      twimlResponse += `<Message>${responseMessage}</Message>`;
      console.log(`🤖 Auto-reply sent: "${responseMessage}"`);
    }
    
    twimlResponse += '</Response>';

    // Return TwiML response to Twilio
    return new NextResponse(twimlResponse, {
      status: 200,
      headers: {
        'Content-Type': 'text/xml',
      },
    });

  } catch (error) {
    console.error('❌ Error processing Twilio incoming message webhook:', error);
    
    // Return empty TwiML response on error
    const errorResponse = '<?xml version="1.0" encoding="UTF-8"?><Response></Response>';
    return new NextResponse(errorResponse, {
      status: 200,
      headers: {
        'Content-Type': 'text/xml',
      },
    });
  }
}

// Handle GET requests for webhook verification
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    message: 'Twilio Incoming Messages Webhook Endpoint',
    status: 'active',
    timestamp: new Date().toISOString()
  });
}
