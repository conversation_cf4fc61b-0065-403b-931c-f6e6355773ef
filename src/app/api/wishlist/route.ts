import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    // For wishlist operations, everything is handled client-side
    // Just return success
    return NextResponse.json(
      { success: true, message: 'Operation successful' }, 
      { status: 200 }
    );
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' }, 
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    // For wishlist operations, everything is handled client-side
    // Just return success
    return NextResponse.json(
      { success: true, message: 'Operation successful' }, 
      { status: 200 }
    );
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' }, 
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // For wishlist operations, everything is handled client-side
    // Just return empty array
    return NextResponse.json(
      { items: [] }, 
      { status: 200 }
    );
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' }, 
      { status: 500 }
    );
  }
} 