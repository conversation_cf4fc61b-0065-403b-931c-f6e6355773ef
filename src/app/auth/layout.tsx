'use client';

import { ReactNode } from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function AuthLayout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-sm p-6 sm:p-8">
        {/* Logo */}
        <div className="text-center mb-6">
          <Link href="/">
            <Image 
              src="/images/logo.png" 
              alt="Treasures of Maimi Logo" 
              width={120} 
              height={40}
              className="mx-auto"
              priority
            />
          </Link>
        </div>
        
        {children}
      </div>
    </div>
  );
}
