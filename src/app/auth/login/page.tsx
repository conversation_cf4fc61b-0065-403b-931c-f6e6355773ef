'use client';

import { useState, FormEvent, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { signIn, AuthRateLimitError } from '@/lib/auth';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/components/AuthProvider';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasRedirected, setHasRedirected] = useState(false);
  const [forceRender, setForceRender] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const { session, isLoading: isAuthLoading } = useAuth();
  
  console.log('[LoginPage] isAuthLoading:', isAuthLoading);
  console.log('[LoginPage] session:', session);
  console.log('[LoginPage] forceRender:', forceRender);
  console.log('[LoginPage] hasRedirected:', hasRedirected);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isAuthLoading) {
        console.log('[LoginPage] Auth still loading after 2.5s, forcing render');
        setForceRender(true);
      }
    }, 2500);
    return () => clearTimeout(timer);
  }, [isAuthLoading]);
  
  const initialRedirect = searchParams.get('redirect') || '/';
  const redirectRef = useRef(initialRedirect);
  console.log('[LoginPage] Redirect target:', redirectRef.current);

  useEffect(() => {
    if (!isAuthLoading && session && !hasRedirected) {
      const redirectTarget = redirectRef.current;
      console.log('[LoginPage] Session available, redirecting to:', redirectTarget);
      setHasRedirected(true);
      router.replace(redirectTarget);
      router.refresh();
    }
  }, [session, isAuthLoading, hasRedirected, router]);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      console.log('[LoginPage] Attempting sign in...');
      await signIn(email, password);
      console.log('[LoginPage] Sign in successful');
      toast.success('Login successful!');
      router.refresh();
    } catch (error: any) {
      console.error('[LoginPage] Sign in error:', error);
      
      // Handle rate limit errors specifically
      if (error instanceof AuthRateLimitError) {
        setError(error.message);
        toast.error(error.message);
      } else if (error.message?.includes('rate limit') || error.message?.includes('Too Many Requests')) {
        setError('Too many login attempts. Please wait a few minutes and try again.');
        toast.error('Too many attempts. Please wait before retrying.');
      } else if (error.message?.includes('Invalid login credentials')) {
        setError('Invalid email or password. Please check your credentials.');
        toast.error('Invalid credentials. Please try again.');
      } else {
        setError(error.message || 'Failed to login.');
        toast.error('Login failed. Please check your credentials.');
      }
      setIsLoading(false);
    }
  };

  if (isAuthLoading && !forceRender) {
    console.log('[LoginPage] Showing loading spinner (isAuthLoading && !forceRender)');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black mx-auto mb-4"></div>
          <p>Loading auth...</p>
        </div>
      </div>
    );
  }

  if (session && !hasRedirected) {
    console.log('[LoginPage] Showing redirect spinner');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black mx-auto mb-4"></div>
          <p>Redirecting...</p>
        </div>
      </div>
    );
  }

  console.log('[LoginPage] Rendering login form');
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="max-w-md w-full mx-4 bg-white p-8 rounded-lg shadow-sm">
        <h1 className="text-2xl font-semibold text-center mb-6">Sign In</h1>

        {error && (
          <div className="bg-red-50 text-red-600 p-3 rounded mb-4 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-black"
              disabled={isLoading}
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-black"
              disabled={isLoading}
            />
          </div>

          <div className="text-right">
            <Link href="/auth/reset-password" className="text-sm text-gray-600 hover:underline">
              Forgot password?
            </Link>
          </div>

          <button
            type="submit"
            className="w-full bg-black text-white py-2 rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign In'}
          </button>
        </form>


        <div className="mt-6 text-center text-sm">
          Don't have an account?{' '}
          <Link href="/auth/register" className="text-black font-medium hover:underline">
            Create account
          </Link>
        </div>
      </div>
    </div>
  );
}
