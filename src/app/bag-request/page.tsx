'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { getUser } from '@/lib/auth';
import { addWardrobeItem } from '@/lib/wardrobe';
import toast from 'react-hot-toast';
import { TablesInsert } from '@/lib/database.types';
import { User } from '@supabase/supabase-js';

type WardrobeItemInput = {
  name: string;
  category: string;
  color: string;
  brand: string | null;
  notes: string | null;
  priority: string;
  status: string;
  user_id: string;
  image_url: string | null;
};

export default function BagRequestPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [newWardrobeItem, setNewWardrobeItem] = useState<WardrobeItemInput>({
    name: '',
    category: 'handbags',
    color: '#000000',
    brand: null,
    notes: null,
    priority: 'medium',
    status: 'pending',
    user_id: '',
    image_url: null
  });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    async function fetchUser() {
      const userData = await getUser();
      if (!userData) {
        router.push('/auth/register?redirect=/bag-request');
      } else {
        setUser(userData);
        setNewWardrobeItem((prev) => ({
          ...prev,
          user_id: userData.id,
        }));

        const urlParams = new URLSearchParams(window.location.search);
        const redirectMessage = urlParams.get('redirect_message');
        if (redirectMessage) {
          setSuccessMessage(decodeURIComponent(redirectMessage));
        }
      }
    }
    fetchUser();
  }, [router]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
      handleImageUpload(file);
    }
  };

  const handleImageUpload = async (file: File) => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('upload_preset', 'bag-requests');
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload image');
      }
      
      const data = await response.json();
      toast.success('Image uploaded successfully!');
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!user) return;

    setSaving(true);
    setSuccessMessage(null);
    try {
      const imageUrl = previewUrl || '';

      await addWardrobeItem(user.id, {
        ...newWardrobeItem,
        image_url: imageUrl,
        status: 'pending'
      });

      setSuccessMessage('🎉 Bag request submitted successfully! Our team will review it.');

      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }

      setTimeout(() => {
        router.push('/account');
      }, 4000);
    } catch (error) {
      console.error('Error submitting bag request:', error);
      toast.error('Failed to submit request.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col justify-center bg-white p-4">
      <div className="max-w-md w-full mx-auto">
        {successMessage && (
          <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded text-center">
            <p className="font-medium">🎉 {successMessage}</p>
            <p className="text-sm mt-2">You can track your bag request in your account dashboard.</p>
          </div>
        )}
        <h1 className="text-3xl font-bold mb-6 text-center">📦 Request Your Dream Bag</h1>
        <p className="text-center text-gray-600 mb-6">
          Welcome! Kindly fill in the details below to request a bag. Our team will review it shortly.
        </p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <input
            type="text"
            placeholder="Bag Name / Description"
            required
            value={newWardrobeItem.name}
            onChange={(e) => setNewWardrobeItem({ ...newWardrobeItem, name: e.target.value })}
            className="w-full p-2 border rounded-md"
          />
          <select
            value={newWardrobeItem.category}
            onChange={(e) => setNewWardrobeItem({ ...newWardrobeItem, category: e.target.value })}
            className="w-full p-2 border rounded-md"
          >
            <option value="handbags">Handbags</option>
            <option value="clutches">Clutches</option>
            <option value="totes">Totes</option>
            <option value="crossbody">Crossbody</option>
            <option value="backpacks">Backpacks</option>
            <option value="wallets">Wallets</option>
            <option value="other">Other</option>
          </select>
          <div className="flex items-center space-x-4">
            <input
              type="color"
              value={newWardrobeItem.color}
              onChange={(e) => setNewWardrobeItem({ ...newWardrobeItem, color: e.target.value })}
              className="w-16 h-10 p-0 border-0 rounded"
            />
            <span className="text-sm text-gray-600">{newWardrobeItem.color}</span>
          </div>
          <input
            type="text"
            placeholder="Brand"
            value={newWardrobeItem.brand ?? ''}
            onChange={(e) => setNewWardrobeItem({ ...newWardrobeItem, brand: e.target.value || null })}
            className="w-full p-2 border rounded-md"
          />
          <textarea
            placeholder="Notes"
            value={newWardrobeItem.notes ?? ''}
            onChange={(e) => setNewWardrobeItem({ ...newWardrobeItem, notes: e.target.value || null })}
            className="w-full p-2 border rounded-md"
            rows={3}
          />
          <select
            value={newWardrobeItem.priority}
            onChange={(e) => setNewWardrobeItem({ ...newWardrobeItem, priority: e.target.value })}
            className="w-full p-2 border rounded-md"
          >
            <option value="low">Low - No Rush</option>
            <option value="medium">Medium - Within a Month</option>
            <option value="high">High - As Soon as Possible</option>
          </select>
          
          {/* Image Upload Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                disabled={uploading}
              >
                {uploading ? 'Uploading...' : 'Choose Image'}
              </button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
                accept="image/*"
              />
              <span className="text-sm text-gray-500">
                {selectedFile ? selectedFile.name : 'No file chosen'}
              </span>
            </div>
            
            {/* Image Preview */}
            {previewUrl && (
              <div className="relative w-full aspect-square rounded-md overflow-hidden">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="object-cover w-full h-full"
                />
              </div>
            )}
          </div>
          
          <button
            type="submit"
            disabled={saving || uploading}
            className="w-full bg-black text-white py-3 text-lg font-semibold rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {saving ? 'Submitting...' : uploading ? 'Uploading Image...' : 'Submit Request'}
          </button>
        </form>
      </div>
    </div>
  );
}
