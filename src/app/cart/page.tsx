'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import toast from 'react-hot-toast';
import { calculateShipping, ShippingInfo, getDefaultShippingInfo } from '@/lib/shipping';
import { customToast } from '@/components/ui/CustomToast';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getCloudinaryUrl, getProductImageUrl } from '@/lib/image-utils';

// Define localStorage key for guest cart
const GUEST_CART_KEY = 'shop-maimi-guest-cart';

// Define cart item type locally to remove dependency on user-data
interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  condition?: string;
  main_image_url?: string;
  category?: {
    name: string;
  };
}

interface CartItem {
  id: string;
  product_id: string;
  quantity: number;
  added_at: string;
  product: Product;
}

// Helper function to dispatch storage change event
const dispatchStorageEvent = () => {
  // This will trigger our custom event listener in the Header component
  window.dispatchEvent(new Event('localStorageChange'));
};

export default function Cart() {
  const router = useRouter();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [removingItem, setRemovingItem] = useState<string | null>(null);
  const [updatingQuantity, setUpdatingQuantity] = useState<string | null>(null);
  const [couponCode, setCouponCode] = useState('');
  const [couponApplied, setCouponApplied] = useState(false);
  const [couponDiscount, setCouponDiscount] = useState(0);
  const [couponError, setCouponError] = useState('');
  const [shippingLocation, setShippingLocation] = useState({
    country: 'ES',  // Default to Spain
    city: '',
    postalCode: ''
  });
  const [shippingInfo, setShippingInfo] = useState<ShippingInfo>(getDefaultShippingInfo());
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);
  const supabase = createClientComponentClient();
  
  console.log("[DEBUG] Cart: Component initialized");
  
  // Handle hydration completion
  useEffect(() => {
    setIsHydrated(true);
  }, []);
  
  // Get cart items from localStorage
  useEffect(() => {
    const fetchCart = async () => {
      try {
        setLoading(true);
        console.log("[DEBUG] Cart: Fetching cart items from localStorage");
        
        // Wait for hydration to complete
        if (!isHydrated) {
          console.log("[DEBUG] Cart: Waiting for hydration");
          return;
        }

        // Safely check for window and localStorage availability
        if (typeof window === 'undefined') {
          console.log("[DEBUG] Cart: Window not available yet");
          return;
        }

        // Wrap localStorage access in try-catch
        let guestCartItems = [];
        try {
          const storedCart = localStorage.getItem(GUEST_CART_KEY);
          if (storedCart) {
            try {
              guestCartItems = JSON.parse(storedCart);
              console.log("[DEBUG] Cart: Retrieved items -", guestCartItems.length);
            } catch (parseError) {
              console.error('[DEBUG] Cart: Error parsing cart data:', parseError);
              // Invalid JSON in storage, clear it
              localStorage.removeItem(GUEST_CART_KEY);
            }
          }
        } catch (storageError) {
          console.error('[DEBUG] Cart: Error accessing localStorage:', storageError);
          // Fallback to empty cart on error
          guestCartItems = [];
        }

        setCartItems(guestCartItems);
      } catch (error: any) {
        console.error('[DEBUG] Cart: Error loading cart:', error);
        toast.error(error.message || 'Failed to load cart items');
      } finally {
        setLoading(false);
      }
    };
    
    fetchCart();
  }, [isHydrated]);
  
  // Helper function to get guest cart items from localStorage
  const getGuestCartItems = (): CartItem[] => {
    if (typeof window === 'undefined') {
      console.log("[DEBUG] Cart: Window not available");
      return [];
    }

    try {
      const storedCart = localStorage.getItem(GUEST_CART_KEY);
      if (!storedCart) {
        console.log("[DEBUG] Cart: No items in localStorage");
        return [];
      }
      
      const parsedItems = JSON.parse(storedCart);
      console.log("[DEBUG] Cart: Parsed items from localStorage -", parsedItems.length);
      return parsedItems;
    } catch (error) {
      console.error('[DEBUG] Cart: Error getting guest cart from localStorage:', error);
      return [];
    }
  };
  
  // Helper function to save guest cart to localStorage
  const saveGuestCart = (items: CartItem[]): boolean => {
    if (typeof window === 'undefined') {
      console.log("[DEBUG] Cart: Window not available for saving");
      return false;
    }

    try {
      console.log("[DEBUG] Cart: Saving items to localStorage -", items.length);
      const safeItems = items.map(item => ({
        ...item,
        product: {
          ...item.product,
          price: Number(item.product.price) || 0,
          quantity: Number(item.quantity) || 0
        }
      }));
      
      localStorage.setItem(GUEST_CART_KEY, JSON.stringify(safeItems));
      
      // Safely dispatch storage event
      try {
        window.dispatchEvent(new Event('localStorageChange'));
      } catch (eventError) {
        console.error('[DEBUG] Cart: Error dispatching storage event:', eventError);
      }
      return true;
    } catch (error) {
      console.error('[DEBUG] Cart: Error saving guest cart to localStorage:', error);
      return false;
    }
  };
  
  // Calculate totals with safe fallbacks
  const subtotal = useMemo(() => {
    if (!cartItems?.length) return 0;
    return cartItems.reduce((acc, item) => {
      const price = Number(item.product?.price) || 0;
      const quantity = Number(item.quantity) || 0;
      return acc + (price * quantity);
    }, 0);
  }, [cartItems]);

  // Update shipping info with safe fallbacks
  useEffect(() => {
    if (!isHydrated) return;

    const { country, city, postalCode } = shippingLocation;
    const shipping = calculateShipping(subtotal || 0, country, city, postalCode);

    setShippingInfo((prev) => {
      // Avoid unnecessary update
      if (
        prev.cost !== shipping.cost ||
        prev.currency !== shipping.currency ||
        prev.estimatedDays !== shipping.estimatedDays ||
        prev.description !== shipping.description
      ) {
        return shipping;
      }
      return prev;
    });
  }, [subtotal, shippingLocation, isHydrated]);
  
  // Safe total calculation with fallbacks
  const total = useMemo(() => {
    const baseTotal = (subtotal || 0) + (shippingInfo?.cost || 0);
    return Math.max(0, baseTotal - (couponDiscount || 0));
  }, [subtotal, shippingInfo?.cost, couponDiscount]);
  
  // Handle quantity change
  const handleQuantityChange = async (cartItemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    // Find the item we're updating
    const itemToUpdate = cartItems.find(item => item.id === cartItemId);
    if (!itemToUpdate) return;
    
    // Find the corresponding product in hardcoded products
    const productId = itemToUpdate.product_id;
    
    try {
      setUpdatingQuantity(cartItemId);
      console.log("[DEBUG] Cart: Updating quantity -", { cartItemId, newQuantity });
      
      // Get product data to check available quantity
      // For now, we'll implement a hard limit until we have full product data
      // In a real implementation, we would fetch this from the API
      const hardcodedQuantity = 5; // Default maximum per product
      
      // Check if the new quantity exceeds the available quantity
      if (newQuantity > hardcodedQuantity) {
        customToast.error(
          <div>
            <p className="font-medium">Limited Stock</p>
            <p className="text-xs mt-1">Sorry, we only have {hardcodedQuantity} of this item in stock.</p>
          </div>
        );
        setUpdatingQuantity(null);
        return;
      }
      
      // Store the original items for rollback if needed
      const originalItems = [...cartItems];
      
      // Optimistic UI update
      setCartItems(prevItems => 
        prevItems.map(item => 
          item.id === cartItemId 
            ? { ...item, quantity: newQuantity } 
            : item
        )
      );
      
      // Show a subtle toast notification for feedback
      const toastId = `quantity-update-${cartItemId}`;
      toast.loading('Updating quantity...', { id: toastId, duration: 2000 });
      
      // Update in localStorage
      const updatedItems = cartItems.map(item => 
        item.id === cartItemId 
          ? { ...item, quantity: newQuantity } 
          : item
      );
      
      // Save to localStorage
      const saveSuccess = saveGuestCart(updatedItems);
      
      if (saveSuccess) {
        toast.dismiss(toastId);
        customToast.success('Quantity updated');
      } else {
        // If saving fails, roll back to original state
        setCartItems(originalItems);
        toast.dismiss(toastId);
        customToast.error('Failed to update quantity');
      }
      
      setUpdatingQuantity(null);
    } catch (error) {
      console.error('[DEBUG] Cart: Error updating quantity:', error);
      toast.error('Failed to update quantity');
      setUpdatingQuantity(null);
    }
  };
  
  // Handle remove item from cart
  const handleRemoveItem = async (cartItemId: string) => {
    try {
      setRemovingItem(cartItemId);
      console.log("[DEBUG] Cart: Removing item -", cartItemId);
      
      // Store the item being removed for potential rollback
      const itemToRemove = cartItems.find(item => item.id === cartItemId);
      if (!itemToRemove) return;
      
      // Optimistic UI update with animation
      setCartItems(prevItems => prevItems.filter(item => item.id !== cartItemId));
      
      // Show a subtle toast notification for feedback
      const toastId = `remove-item-${cartItemId}`;
      toast.loading('Removing item...', { id: toastId, duration: 2000 });
      
      // Remove from localStorage
      const updatedItems = cartItems.filter(item => item.id !== cartItemId);
      const saveResult = saveGuestCart(updatedItems);
      
      if (saveResult) {
        toast.dismiss(toastId);
        customToast.success('Item removed from cart');
      } else {
        // If saving to localStorage fails, restore the item
        setCartItems(prevItems => [...prevItems, itemToRemove]);
        toast.dismiss(toastId);
        customToast.error('Failed to remove item');
      }
    } catch (error: any) {
      console.error('[DEBUG] Cart: Error removing item:', error);
      
      // Restore the item on error
      const itemToRestore = cartItems.find(item => item.id === cartItemId);
      if (itemToRestore) {
        setCartItems(prevItems => [...prevItems, itemToRestore]);
      }
      
      toast.error(error.message || 'Failed to remove item');
    } finally {
      setRemovingItem(null);
    }
  };
  
  // Handle coupon code application
  const handleApplyCoupon = async () => {
    try {
      if (!couponCode) {
        setCouponError('Please enter a coupon code');
        return;
      }

      setCouponError('');

      // Validate discount code using the API
      const response = await fetch(`/api/discount-codes/validate?code=${encodeURIComponent(couponCode)}&order_total=${subtotal}`);
      const data = await response.json();

      if (response.ok && data.is_valid) {
        // Apply the discount
        setCouponDiscount(data.discount_amount);
        setCouponApplied(true);
        customToast.success(`Coupon code applied successfully! €${data.discount_amount.toFixed(2)} discount added.`);
      } else {
        // Handle validation errors
        const errorMessage = data.error_message || 'Invalid coupon code. Please try again.';
        setCouponError(errorMessage);
        customToast.error(errorMessage);
      }
    } catch (error: any) {
      console.error('Error applying coupon:', error);
      setCouponError('Failed to validate coupon code. Please try again.');
      customToast.error('Failed to validate coupon code. Please try again.');
    }
  };

  // Handle coupon removal
  const handleRemoveCoupon = () => {
    setCouponCode('');
    setCouponApplied(false);
    setCouponDiscount(0);
    setCouponError('');
    customToast.success('Coupon removed successfully.');
  };

  // Check authentication status on mount
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setIsAuthenticated(!!session);
    };
    checkAuth();
  }, [supabase]);
  
  // Proceed to checkout with safe storage handling
  const handleCheckout = () => {
    if (!cartItems?.length) {
      toast.error('Your cart is empty');
      return;
    }
 
    // Validate shipping info
    if (!shippingLocation.country || (shippingLocation.country === 'ES' && (!shippingLocation.city || !shippingLocation.postalCode))) {
      toast.error('Please provide complete shipping details');
      return;
    }
 
    try {
      const cartSaved = saveGuestCart(cartItems);
      if (!cartSaved) {
        toast.error('Unable to save cart data. Please try again.');
        return;
      }
 
      // Store the guest cart in sessionStorage safely
      if (typeof window !== 'undefined' && window.sessionStorage) {
        try {
          const cartJSON = JSON.stringify(cartItems);
          sessionStorage.setItem('pendingCart', cartJSON);
          console.log('[Cart] Saved pendingCart to sessionStorage');

          // Store discount information for checkout
          if (couponApplied) {
            const discountData = {
              code: couponCode,
              discount: couponDiscount,
              applied: true
            };
            sessionStorage.setItem('checkout_discount', JSON.stringify(discountData));
            console.log('[Cart] Saved discount data to sessionStorage');
          } else {
            sessionStorage.removeItem('checkout_discount');
          }
        } catch (err) {
          console.error('[Cart] Failed to access sessionStorage:', err);
          toast.error('Something went wrong saving your cart. Please try again.');
          return;
        }
      }
 
      if (!isAuthenticated) {
        console.log('[Cart] User not authenticated, redirecting to login with checkout redirect');
        router.replace('/auth/login?redirect=/checkout');
        return;
      }
 
      console.log('[Cart] User authenticated, proceeding to checkout');
      router.push('/checkout');
    } catch (error) {
      console.error('[Cart] Error proceeding to checkout:', error);
      toast.error('Something went wrong while proceeding to checkout. Please try again.');
    }
  };
  
  // Show loading state while not hydrated or loading
  if (!isHydrated || loading) {
    return (
      <div className="py-20 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900 mb-4"></div>
        <p>Loading your cart...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-12 bg-[#f8f8f8]">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-8">
          <h1 className="text-3xl font-heading tracking-wider text-[#171717]">Shopping Cart</h1>
          <p className="mt-2 text-gray-600">Review your items and proceed to checkout</p>
        </div>
        
        {cartItems.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            <h2 className="text-xl font-heading mb-2">Your cart is empty</h2>
            <p className="text-gray-600 mb-6">Looks like you haven't added any products to your cart yet</p>
            <Link 
              href="/products" 
              className="px-6 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] transition-colors"
            >
              Continue Shopping
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <ul className="divide-y divide-gray-100">
                  <AnimatePresence>
                    {cartItems.map((item) => (
                      <motion.li 
                        key={item.id}
                        initial={{ opacity: 1 }}
                        exit={{ opacity: 0, height: 0, overflow: 'hidden' }}
                        transition={{ duration: 0.3 }}
                        className="p-4 sm:p-6"
                      >
                        <div className="flex flex-col sm:flex-row">
                          {/* Product Image */}
                          <div className="flex-shrink-0 w-full sm:w-24 h-24 mb-4 sm:mb-0">
                            <Link href={`/products/${item.product.slug}`}>
                              <div className="relative h-full w-full bg-gray-100 rounded-md overflow-hidden">
                                {item.product.main_image_url && (
                                  <Image
                                    src={getCloudinaryUrl(item.product.main_image_url)}
                                    alt={item.product.name}
                                    width={80}
                                    height={80}
                                    className="object-cover h-full w-full"
                                  />
                                )}
                              </div>
                            </Link>
                          </div>
                          
                          {/* Product Details */}
                          <div className="flex-1 sm:ml-6">
                            <div className="flex flex-col sm:flex-row sm:justify-between">
                              <div>
                                <Link 
                                  href={`/products/${item.product.slug}`}
                                  className="text-lg font-medium text-[#171717] hover:underline"
                                >
                                  {item.product.name}
                                </Link>
                                <div className="mt-1 flex items-center text-sm text-gray-600">
                                  <span className="mr-2">{item.product.category?.name}</span>
                                  <span className="h-1 w-1 bg-gray-500 rounded-full mx-1"></span>
                                  <span>Condition: {item.product.condition}</span>
                                </div>
                                <div className="mt-1 text-lg font-medium text-[#171717]">
                                  ${item.product.price.toLocaleString()}
                                </div>
                              </div>
                              
                              <div className="mt-4 sm:mt-0 flex items-center">
                                {/* Quantity Selector */}
                                <div className="flex items-center">
                                  <div className="flex border border-gray-300 rounded-md">
                                    <button
                                      onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                      disabled={item.quantity <= 1 || updatingQuantity === item.id}
                                      className="px-2 py-1 text-gray-600 hover:bg-gray-50 disabled:opacity-50"
                                      aria-label="Decrease quantity"
                                    >
                                      -
                                    </button>
                                    <div className="w-10 flex items-center justify-center text-sm">
                                      {updatingQuantity === item.id ? (
                                        <span className="animate-pulse">...</span>
                                      ) : (
                                        item.quantity
                                      )}
                                    </div>
                                    <button
                                      onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                      disabled={updatingQuantity === item.id}
                                      className="px-2 py-1 text-gray-600 hover:bg-gray-50 disabled:opacity-50"
                                      aria-label="Increase quantity"
                                    >
                                      +
                                    </button>
                                  </div>
                                  
                                  {item.quantity > 1 && (
                                    <button
                                      onClick={() => handleQuantityChange(item.id, 1)}
                                      className="ml-2 text-xs text-blue-600 hover:text-blue-800 underline"
                                    >
                                      Reset to 1
                                    </button>
                                  )}
                                </div>
                                
                                {/* Remove Button */}
                                <button
                                  onClick={() => handleRemoveItem(item.id)}
                                  disabled={removingItem === item.id}
                                  className="ml-4 text-sm text-red-600 hover:text-red-800 disabled:opacity-50"
                                >
                                  {removingItem === item.id ? 'Removing...' : 'Remove'}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.li>
                    ))}
                  </AnimatePresence>
                </ul>
              </div>
              
              <div className="mt-6 flex justify-between">
                <Link 
                  href="/products" 
                  className="text-sm text-[#171717] hover:underline flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  Continue Shopping
                </Link>
              </div>
            </div>
            
            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-heading text-[#171717] mb-4">Order Summary</h2>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">€{subtotal.toLocaleString()}</span>
                    </div>
                    
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Shipping</span>
                      <span className="font-medium">€{shippingInfo.cost.toFixed(2)}</span>
                    </div>
                    
                    {couponApplied && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Discount</span>
                        <span>-€{couponDiscount.toFixed(2)}</span>
                      </div>
                    )}
                    
                    <div className="flex justify-between border-t border-gray-200 pt-2 mt-2">
                      <span className="font-semibold">Total</span>
                      <span className="font-semibold">€{total.toLocaleString()}</span>
                    </div>
                  </div>
                  
                  {/* Shipping Calculator */}
                  <div className="mt-8 border-t pt-6">
                    <h3 className="text-base font-medium mb-4">Estimate Shipping</h3>
                    <div className="space-y-4">
                      {/* Country Selector */}
                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Country</label>
                        <select
                          value={shippingLocation.country}
                          onChange={(e) => setShippingLocation({
                            ...shippingLocation,
                            country: e.target.value
                          })}
                          className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                        >
                          <option value="ES">Spain</option>
                          <option value="FR">France</option>
                          <option value="DE">Germany</option>
                          <option value="IT">Italy</option>
                          <option value="GB">United Kingdom</option>
                          <option value="US">United States</option>
                          <option value="CA">Canada</option>
                          <option value="AU">Australia</option>
                          <option value="JP">Japan</option>
                          <option value="OTHER">Other Countries</option>
                        </select>
                      </div>
                      
                      {/* City & Postal Code (only if Spain is selected) */}
                      {shippingLocation.country === 'ES' && (
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-sm text-gray-700 mb-1">City</label>
                            <input
                              type="text"
                              value={shippingLocation.city}
                              onChange={(e) => setShippingLocation({
                                ...shippingLocation,
                                city: e.target.value
                              })}
                              placeholder="Valencia"
                              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                            />
                          </div>
                          <div>
                            <label className="block text-sm text-gray-700 mb-1">Postal Code</label>
                            <input
                              type="text"
                              value={shippingLocation.postalCode}
                              onChange={(e) => setShippingLocation({
                                ...shippingLocation,
                                postalCode: e.target.value
                              })}
                              placeholder="46001"
                              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                            />
                          </div>
                        </div>
                      )}
                      
                      {/* Shipping Estimate Display */}
                      <div className="mt-3 p-3 bg-gray-50 rounded-md">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="font-medium">Shipping:</span>
                          <span>€{shippingInfo.cost.toFixed(2)}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {shippingInfo.description}
                          {shippingInfo.estimatedDays && (
                            <span className="block mt-1">Estimated delivery: {shippingInfo.estimatedDays} business days</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Coupon Code */}
                  <div className="pt-2">
                    {!couponApplied ? (
                      <div className="flex space-x-2">
                        <input
                          type="text"
                          value={couponCode}
                          onChange={e => {
                            setCouponCode(e.target.value.toUpperCase());
                            setCouponError(''); // Clear error when typing
                          }}
                          placeholder="Enter coupon code"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-200 text-sm"
                          onKeyPress={e => {
                            if (e.key === 'Enter') {
                              handleApplyCoupon();
                            }
                          }}
                        />
                        <button
                          onClick={handleApplyCoupon}
                          disabled={!couponCode}
                          className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 hover:bg-gray-100 text-sm disabled:opacity-50"
                        >
                          Apply
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-green-800">{couponCode}</span>
                          <span className="text-xs text-green-600">-€{couponDiscount.toFixed(2)}</span>
                        </div>
                        <button
                          onClick={handleRemoveCoupon}
                          className="text-xs text-green-600 hover:text-green-800 underline"
                        >
                          Remove
                        </button>
                      </div>
                    )}
                    {couponError && (
                      <p className="mt-1 text-xs text-red-600">{couponError}</p>
                    )}
                  </div>
                  
                  {/* Checkout Button */}
                  <button
                    onClick={handleCheckout}
                    disabled={cartItems.length === 0}
                    className="w-full mt-4 px-6 py-3 bg-[#171717] text-white rounded-md hover:bg-[#333333] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Proceed to Checkout
                  </button>
                  
                  {/* Payment Methods */}
                  <div className="mt-4 flex justify-center gap-4 items-center">
                    <Image src="https://upload.wikimedia.org/wikipedia/commons/0/04/Visa.svg" alt="Visa" height={28} width={48} />
                    <Image src="https://upload.wikimedia.org/wikipedia/commons/2/2a/Mastercard-logo.svg" alt="Mastercard" height={28} width={48} />
                    <Image src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg" alt="PayPal" height={28} width={64} />
                    <Image src="https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg" alt="Apple Pay" height={32} width={32} />
                  </div>
                  
                  <p className="text-xs text-gray-500 text-center mt-4">
                    Secure payment processing. All transactions are encrypted.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
