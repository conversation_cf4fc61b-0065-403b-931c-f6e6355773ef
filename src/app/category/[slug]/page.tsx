'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { formatCurrency } from '@/lib/formatters';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/database.types';
import { getCloudinaryUrl, getProductImageUrl } from '@/lib/image-utils';

// Define product specifications type
interface ProductSpecifications {
  [key: string]: string;
}

// Define product media item type
interface MediaItem {
  id: string;
  url: string;
  product_id: string;
  created_at: string | null;
}

// Define product type
interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  sale_price?: number | null;
  description?: string | null;
  category_id?: string | null;
  collection_id?: string | null;
  condition_id?: string | null;
  brand?: string | null;
  model?: string | null;
  quantity?: number | null;
  specifications?: ProductSpecifications;
  mediaItems?: MediaItem[];
  image_url?: string;
  status?: string;
}

// Define category type
interface Category {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  parent_id: string | null;
  image_url: string | null;
}

interface Params {
  params: {
    slug: string;
  };
}

export default function CategoryPage({ params }: Params) {
  const [products, setProducts] = useState<Product[]>([]);
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);
  const [sortOption, setSortOption] = useState<string>('newest');
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchCategoryAndProducts();
  }, [params.slug]);

  useEffect(() => {
    if (category) {
      sortProducts();
    }
  }, [sortOption, category]);

  const fetchCategoryAndProducts = async () => {
    setLoading(true);
    try {
      // Initialize Supabase client
      const supabase = createClientComponentClient<Database>();

      // Fetch category by slug
      const { data: categoryData, error: categoryError } = await supabase
        .from('categories')
        .select('*')
        .ilike('slug', params.slug.toLowerCase())
        .single();

      if (categoryError || !categoryData) {
        router.push('/products');
        return;
      }

      // Set category state
      const categoryObj: Category = {
        id: categoryData.id,
        name: categoryData.name,
        slug: categoryData.slug || '',
        description: categoryData.description,
        parent_id: categoryData.parent_id,
        image_url: categoryData.image_url
      };
      setCategory(categoryObj);

      // Fetch products in this category
      const { data: productsData, error: productError } = await supabase
        .from('products')
        .select(`
          *,
          mediaItems:product_media(*)
        `)
        .eq('category_id', categoryData.id);

      if (productError || !productsData) {
        setProducts([]);
      } else {
        // Map products with their details
        const productsWithDetails = productsData.map((product: any) => {
          // Parse specifications from JSON if available
          const specifications = product.specifications ? 
            (typeof product.specifications === 'string' ? 
              JSON.parse(product.specifications) : product.specifications) : {};
          
          // Get media items
          const mediaItems = product.mediaItems || [];
          
          return {
            ...product,
            specifications,
            mediaItems,
            image_url: getProductImageUrl(mediaItems),
            category_id: categoryData.id,
            status: product.status,
          } as Product;
        });

        setProducts(productsWithDetails);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error processing category data:', err);
      setError('Failed to load products. Please try again later.');
      setLoading(false);
    }
  };

  const sortProducts = () => {
    const sortedProducts = [...products];
    
    switch (sortOption) {
      case 'price-low':
        sortedProducts.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        sortedProducts.sort((a, b) => b.price - a.price);
        break;
      case 'name-asc':
        sortedProducts.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'name-desc':
        sortedProducts.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case 'newest':
      default:
        // Keep the default order
        break;
    }
    
    setProducts(sortedProducts);
  };

  // Helper function to check if a product is sold out
  const isProductSoldOut = (product: Product) => {
    return product.status === 'sold_out' || product.quantity === 0;
  };

  if (error) {
    return (
      <div className="min-h-screen pt-32 pb-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <h2 className="text-2xl font-heading tracking-wider text-[#171717] mb-4">Error</h2>
            <p className="text-[#666666]">{error}</p>
            <Link href="/products" className="mt-6 inline-block px-6 py-3 bg-[#171717] text-white font-body tracking-wider">
              Browse All Products
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen pt-32 pb-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {loading ? (
          <div className="text-center py-12">
            <div className="w-12 h-12 border-4 border-[#171717] border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="mt-4 font-body text-[#666666]">Loading products...</p>
          </div>
        ) : (
          <>
            <div className="text-center mb-16">
              <motion.h1 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-4xl font-heading tracking-[0.2em] text-[#171717] mb-4"
              >
                {category?.name}
              </motion.h1>
              {category?.description && (
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-lg font-body italic text-[#666666] max-w-3xl mx-auto"
                >
                  {category.description}
                </motion.p>
              )}
            </div>

            <div className="flex justify-end mb-8">
              <div className="relative">
                <select
                  value={sortOption}
                  onChange={(e) => setSortOption(e.target.value)}
                  className="appearance-none bg-white border border-[#e5e5e5] px-4 py-2 pr-8 font-body text-sm tracking-wider focus:outline-none focus:ring-2 focus:ring-[#171717]"
                >
                  <option value="newest">Newest</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="name-asc">Name: A to Z</option>
                  <option value="name-desc">Name: Z to A</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-[#171717]">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
              </div>
            </div>

            {products.length > 0 ? (
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              >
                {products.map((product) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="group relative"
                  >
                    <Link href={`/products/${product.slug}`}>
                      <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
                        {product.image_url ? (
                          <Image
                            src={product.image_url}
                            alt={product.name}
                            fill
                            className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
                          />
                        ) : (
                          <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                            No image
                          </div>
                        )}
                        {isProductSoldOut(product) && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                            <span className="text-white font-medium px-4 py-2 bg-black bg-opacity-75 rounded-md">
                              Sold Out
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="mt-4 space-y-1">
                        <h3 className="text-sm font-medium text-[#171717] group-hover:text-[#333333]">
                          {product.name}
                        </h3>
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-[#171717]">
                            {formatCurrency(product.price)}
                          </p>
                          {isProductSoldOut(product) && (
                            <span className="text-xs font-medium text-red-600">
                              Sold Out
                            </span>
                          )}
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              <div className="text-center py-12">
                <p className="font-body text-[#666666]">No products found in this category.</p>
                <Link href="/products" className="mt-6 inline-block px-6 py-3 bg-[#171717] text-white font-body tracking-wider">
                  Browse All Products
                </Link>
              </div>
            )}
          </>
        )}
      </div>
    </main>
  );
}
