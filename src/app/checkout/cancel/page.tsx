'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { XCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/database.types';

export default function CancelPage() {
  const [orderUpdated, setOrderUpdated] = useState(false);
  const searchParams = useSearchParams();
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    // Check if there's a session_id in the URL and redirect to the new incomplete page
    const sessionId = searchParams.get('session_id');

    if (sessionId) {
      // Redirect to the new incomplete payment page
      window.location.href = `/checkout/incomplete?session_id=${sessionId}`;
      return;
    }

    // If no session_id, this is a generic cancel (show this page)
  }, [searchParams]);
  return (
    <motion.div 
      className="max-w-4xl mx-auto px-4 py-12"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="bg-white shadow-md rounded-lg p-8 mb-8">
        <div className="flex flex-col items-center text-center mb-8">
          <XCircle className="w-16 h-16 text-red-500 mb-4" />
          <h1 className="text-3xl font-bold mb-2">Payment Cancelled</h1>
          <p className="text-gray-600 mb-4">
            Your payment was cancelled. If you encountered an issue, please try again or contact support.
            Your cart remains unchanged.
          </p>
        </div>
        
        <div className="mt-8 flex justify-center flex-wrap gap-4">
          <Link
            href="/cart"
            className="px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors"
          >
            Return to Cart
          </Link>
          <Link
            href="/account?tab=orders"
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            View Orders
          </Link>
          <Link
            href="/"
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Continue Shopping
          </Link>
          <Link
            href="/contact"
            className="px-6 py-2 border border-red-500 text-red-500 rounded-md hover:bg-red-50 transition-colors"
          >
            Contact Support
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
