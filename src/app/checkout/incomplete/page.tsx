'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { AlertTriangle, Clock, ShoppingBag, CreditCard } from 'lucide-react';
import { motion } from 'framer-motion';
import { useSearchParams, useRouter } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/database.types';

export default function IncompletePaymentPage() {
  const [orderData, setOrderData] = useState<any>(null);
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15 minutes in seconds
  const [loading, setLoading] = useState(true);
  const searchParams = useSearchParams();
  const router = useRouter();
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    const updateIncompleteOrder = async () => {
      const sessionId = searchParams.get('session_id');

      try {
        let orders = null;

        if (sessionId) {
          // Find the order associated with this session
          const { data, error: findError } = await supabase
            .from('orders')
            .select(`
              id,
              total_amount,
              order_number,
              created_at,
              status,
              order_items(
                quantity,
                price,
                product:products(name)
              )
            `)
            .eq('session_id', sessionId)
            .eq('status', 'pending')
            .single();

          if (!findError) {
            orders = data;
          }
        }

        // If no order found by session_id, try to find the most recent pending order for this user
        if (!orders) {
          // Get current user
          const { data: { session } } = await supabase.auth.getSession();

          if (session?.user?.id) {
            const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000).toISOString();

            const { data, error: findError } = await supabase
              .from('orders')
              .select(`
                id,
                total_amount,
                order_number,
                created_at,
                status,
                order_items(
                  quantity,
                  price,
                  product:products(name)
                )
              `)
              .eq('user_id', session.user.id)
              .eq('status', 'pending')
              .gte('created_at', thirtyMinutesAgo)
              .order('created_at', { ascending: false })
              .limit(1)
              .single();

            if (!findError) {
              orders = data;
            }
          }
        }

        if (orders) {
          setOrderData(orders);

          // Update order status to cancelled with incomplete payment
          const { error: updateError } = await supabase
            .from('orders')
            .update({
              status: 'cancelled',
              payment_status: 'incomplete',
              updated_at: new Date().toISOString(),
              status_updated_at: new Date().toISOString()
            })
            .eq('id', orders.id);

          // Order status updated silently
        }
      } catch (error) {
        // Handle error silently
      }

      setLoading(false);
    };

    updateIncompleteOrder();
  }, [searchParams, supabase]);

  // Countdown timer effect
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCompletePayment = async () => {
    if (!orderData) {
      console.log('No order data, redirecting to checkout');
      window.location.href = '/checkout';
      return;
    }

    try {
      setLoading(true);
      console.log('Attempting to create new payment session for order:', orderData.id);

      // Create a new Stripe session for this order
      const response = await fetch('/api/stripe/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: orderData.id,
          retryPayment: true
        }),
      });

      console.log('Stripe API response status:', response.status);
      const data = await response.json();
      console.log('Stripe API response data:', data);

      if (data.url) {
        console.log('Redirecting to Stripe checkout:', data.url);
        // Redirect to Stripe checkout
        window.location.href = data.url;
      } else {
        console.error('No checkout URL received:', data);
        throw new Error(data.error || 'Failed to create payment session');
      }
    } catch (error) {
      console.error('Payment retry failed, redirecting to checkout:', error);
      // Use window.location.href for more reliable redirect
      window.location.href = '/checkout';
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-12 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading...</p>
      </div>
    );
  }

  return (
    <motion.div 
      className="max-w-4xl mx-auto px-4 py-12"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="bg-gradient-to-br from-red-50 to-orange-50 border border-red-200 rounded-lg p-8 mb-8">
        <div className="flex flex-col items-center text-center mb-8">
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <AlertTriangle className="w-20 h-20 text-red-500 mb-4" />
          </motion.div>
          
          <h1 className="text-4xl font-bold mb-4 text-gray-900">
            Oops! Your Payment Wasn't Completed
          </h1>
          
          <p className="text-lg text-gray-700 mb-6 max-w-2xl">
            We noticed you didn't complete your purchase. Your beautiful items are still waiting for you, 
            but they won't be reserved much longer...
          </p>

          {orderData && (
            <div className="bg-white rounded-lg p-6 shadow-sm border mb-6 max-w-md w-full">
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm text-gray-500">Order #{orderData.order_number}</span>
                <span className="text-lg font-bold">€{Number(orderData.total_amount).toFixed(2)}</span>
              </div>
              
              <div className="space-y-2">
                {orderData.order_items?.slice(0, 2).map((item: any, index: number) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                      <ShoppingBag className="w-6 h-6 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{item.product?.name}</p>
                      <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                    </div>
                  </div>
                ))}
                {orderData.order_items?.length > 2 && (
                  <p className="text-xs text-gray-500 text-center">
                    +{orderData.order_items.length - 2} more items
                  </p>
                )}
              </div>
            </div>
          )}

          <div className="bg-yellow-100 border border-yellow-300 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <Clock className="w-5 h-5 text-yellow-600" />
              <span className="font-semibold text-yellow-800">Limited Time Offer Expiring!</span>
            </div>
            <p className="text-yellow-700 text-sm">
              Complete your purchase in the next <strong>{formatTime(timeLeft)}</strong> to secure these items
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 text-sm">
            <div className="bg-white p-4 rounded-lg border">
              <ShoppingBag className="w-8 h-8 text-blue-500 mx-auto mb-2" />
              <p className="font-medium">Items Reserved</p>
              <p className="text-gray-600">Your items are temporarily held</p>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <CreditCard className="w-8 h-8 text-green-500 mx-auto mb-2" />
              <p className="font-medium">Secure Payment</p>
              <p className="text-gray-600">256-bit SSL encryption</p>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <Clock className="w-8 h-8 text-orange-500 mx-auto mb-2" />
              <p className="font-medium">Fast Shipping</p>
              <p className="text-gray-600">Express delivery available</p>
            </div>
          </div>
        </div>
        
        <div className="flex justify-center flex-wrap gap-4">
          <motion.button
            onClick={handleCompletePayment}
            className="px-8 py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors font-semibold text-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Complete My Purchase Now
          </motion.button>
          
          <Link 
            href="/account?tab=orders"
            className="px-6 py-3 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            View My Orders
          </Link>
          
          <Link 
            href="/"
            className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Continue Shopping
          </Link>
        </div>

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            Need help? <Link href="/contact" className="text-blue-600 hover:underline">Contact our support team</Link>
          </p>
        </div>
      </div>

      {/* Social proof section */}
      <div className="bg-white rounded-lg p-6 shadow-sm border">
        <h3 className="text-lg font-semibold mb-4 text-center">Don't Miss Out!</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Over 1,000 happy customers this month</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>Free returns within 30 days</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span>Authentic luxury items guaranteed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
            <span>Limited stock on popular items</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
