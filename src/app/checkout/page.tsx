'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import { calculateShipping, ShippingInfo, getDefaultShippingInfo } from '@/lib/shipping';
import { customToast } from '@/components/ui/CustomToast';
import PayPalCheckoutButton from '@/components/paypal/PayPalCheckoutButton';
import { useAuth } from '@/components/AuthProvider';
import StripeCheckoutButton from '@/components/stripe/StripeCheckoutButton';
import { CreditCard } from 'lucide-react';
import { getCloudinaryUrl } from '@/lib/image-utils';

interface CartItem {
  id: string;
  product_id: string;
  user_id: string;
  quantity: number;
  added_at: string;
  product: {
    id: string;
    name: string;
    slug: string;
    price: number;
    condition: string;
    main_image_url: string;
    category: {
      name: string;
    };
  };
}

interface Address {
  id: string;
  name?: string | null;
  street?: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  postal_code: string | null;
  is_default: boolean | null;
  user_id?: string | null;
  created_at?: string | null;
}

export default function Checkout() {
  const supabase = createClientComponentClient();
  const router = useRouter();
  const { session, isLoading: isAuthLoading } = useAuth();



  // Check for cancelled payment parameter and pending orders
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('cancelled') === 'true') {
        router.replace('/checkout/cancel');
        return;
      }
    }
  }, [router]);

  // TEMPORARILY DISABLED: Check for pending incomplete orders when user returns to checkout
  // This was causing redirect loops - users couldn't escape the incomplete payment page
  // TODO: Re-enable with better logic that doesn't trap users
  /*
  useEffect(() => {
    const checkForPendingOrders = async () => {
      if (!session?.user?.id) return;

      try {
        // Check if user has any recent pending orders (within last 30 minutes)
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000).toISOString();

        const { data: pendingOrders, error } = await supabase
          .from('orders')
          .select('id, session_id, created_at')
          .eq('user_id', session.user.id)
          .eq('status', 'pending')
          .gte('created_at', thirtyMinutesAgo)
          .order('created_at', { ascending: false })
          .limit(1);

        if (error) {
          console.error('Error checking pending orders:', error);
          return;
        }

        if (pendingOrders && pendingOrders.length > 0) {
          const pendingOrder = pendingOrders[0];

          // Redirect to incomplete page with the session_id if available
          const redirectUrl = pendingOrder.session_id
            ? `/checkout/incomplete?session_id=${pendingOrder.session_id}`
            : `/checkout/incomplete`;

          router.replace(redirectUrl);
        }
      } catch (error) {
        console.error('Error checking for pending orders:', error);
      }
    };

    // Only check after a short delay to avoid interfering with normal checkout flow
    const timer = setTimeout(checkForPendingOrders, 1000);
    return () => clearTimeout(timer);
  }, [session?.user?.id, router, supabase]);
  */
  
  // State management
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [newAddress, setNewAddress] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'paypal' | 'stripe'>('paypal');
  
  // Shipping address state
  const [street, setStreet] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [country, setCountry] = useState('');
  const [postalCode, setPostalCode] = useState('');
  
  const [checkoutStep, setCheckoutStep] = useState(0);
  const [isCheckoutValid, setIsCheckoutValid] = useState(false);

  // Discount state
  const [discountInfo, setDiscountInfo] = useState<{
    code: string;
    discount: number;
    applied: boolean;
  } | null>(null);
  
  // Animation variants for checkout steps
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24
      }
    }
  };
  
  // Checkout steps
  const steps = [
    { id: "cart", label: "Cart Review" },
    { id: "shipping", label: "Shipping" },
    { id: "payment", label: "Payment" },
    { id: "confirmation", label: "Confirmation" }
  ];
  
  // Add shipping state
  const [shippingInfo, setShippingInfo] = useState<ShippingInfo>(getDefaultShippingInfo());
  
  // Handle authentication and data loading
  useEffect(() => {
    if (isAuthLoading) return;

    let mounted = true;

    const handleAuth = async () => {
      // Force refresh session to make sure we have the latest state
      const {
        data: { session: refreshedSession },
      } = await supabase.auth.getSession();

      if (!refreshedSession) {
        try {
          // Safely handle storage access
          if (typeof window !== 'undefined') {
            try {
              const localCart = localStorage.getItem('cart');
              if (localCart) {
                sessionStorage.setItem('pendingCart', localCart);
              }
            } catch (storageError) {
              console.error('[Checkout] Error accessing storage:', storageError);
            }
          }
        } catch (error) {
          console.error('[Checkout] Error saving cart:', error);
        }
        router.push(`/auth/login?redirect=${encodeURIComponent('/checkout')}`);
        return;
      }

      if (mounted) {
        // Safely handle storage access
        let pendingCart = null;
        if (typeof window !== 'undefined') {
          try {
            pendingCart = sessionStorage.getItem('pendingCart');
          } catch (storageError) {
            console.error('[Checkout] Error accessing sessionStorage:', storageError);
          }
        }

        if (pendingCart) {
          await migrateCartToDatabase(pendingCart);
        } else {
          await fetchCheckoutData();
        }
      }
    };

    handleAuth();

    return () => {
      mounted = false;
    };
  }, [isAuthLoading]);

  // Migrate cart items from localStorage to database
  const migrateCartToDatabase = async (pendingCart: string) => {
    if (!session) return;

    try {
      setLoading(true);
      const parsedCart = JSON.parse(pendingCart);

      // First, clear existing cart items
      await supabase
        .from('cart_items')
        .delete()
        .eq('user_id', session.user.id);

      // Insert new cart items
      const itemsToInsert = parsedCart.map((item: any) => ({
        product_id: item.product_id,
        user_id: session.user.id,
        quantity: item.quantity
      }));

      // Insert items to database
      if (itemsToInsert.length > 0) {
        const { error: insertError } = await supabase
          .from('cart_items')
          .insert(itemsToInsert);

        if (insertError) {
          throw insertError;
        }
      }

      // Safely clear temporary storage
      if (typeof window !== 'undefined') {
        try {
          sessionStorage.removeItem('pendingCart');
          localStorage.removeItem('cart');
          localStorage.removeItem('shop-maimi-guest-cart');
        } catch (storageError) {
          console.error('[Checkout] Error clearing storage:', storageError);
        }
      }

      // Fetch updated cart data
      await fetchCheckoutData();

      // Load discount information from sessionStorage
      loadDiscountInfo();
    } catch (error) {
      console.error('[Checkout] Error migrating cart:', error);
      customToast.error('Failed to migrate cart items. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Load discount information from sessionStorage
  const loadDiscountInfo = () => {
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        const discountData = sessionStorage.getItem('checkout_discount');
        if (discountData) {
          const parsedDiscount = JSON.parse(discountData);
          setDiscountInfo(parsedDiscount);
        }
      } catch (error) {
        console.error('[Checkout] Error loading discount info:', error);
      }
    }
  };

  // Fetch checkout data
  const fetchCheckoutData = async () => {
    if (!session) return;

    try {
      setLoading(true);

      // Fetch cart items with product details
      const { data: cartData, error: cartError } = await supabase
        .from('cart_items')
        .select(`
          id,
          product_id,
          user_id,
          quantity,
          added_at,
          product:products(
            id,
            name,
            slug,
            price,
            category:categories(name),
            condition_id,
            condition:product_conditions!inner(value, label)
          )
        `)
        .eq('user_id', session?.user?.id);

      if (cartError) throw cartError;

      // Fetch media for all products in one query - try main image first, fallback to any image
      const productIds = cartData?.map(item => item.product_id).filter(Boolean) || [];
      
      // First try to get main images
      const { data: mainMediaData, error: mainMediaError } = await supabase
        .from('product_media')
        .select('product_id, url, is_main, position')
        .in('product_id', productIds)
        .eq('is_main', true);

      // If no main images or error, get any images ordered by position
      const { data: fallbackMediaData, error: fallbackMediaError } = await supabase
        .from('product_media')
        .select('product_id, url, is_main, position')
        .in('product_id', productIds)
        .order('position', { ascending: true });

      // Create a map of product_id to media_url with fallback logic
      const mediaMap = new Map();
      
      // First, populate with main images
      if (mainMediaData) {
        mainMediaData.forEach(media => {
          if (media.url) {
            mediaMap.set(media.product_id, media.url);
          }
        });
      }
      
      // Fill in missing images with any available image
      if (fallbackMediaData) {
        fallbackMediaData.forEach(media => {
          if (media.url && !mediaMap.has(media.product_id)) {
            mediaMap.set(media.product_id, media.url);
          }
        });
      }

      // Format cart items with media
      const formattedCartItems = (cartData || []).map(item => {
        const productData = Array.isArray(item.product) ? item.product[0] : item.product;
        const categoryData = Array.isArray(productData?.category) 
          ? (productData.category[0] || { name: 'Uncategorized' }) 
          : (productData?.category || { name: 'Uncategorized' });
        const condition = Array.isArray(productData?.condition)
          ? (productData.condition[0] as { label: string })?.label || 'Unknown'
          : (productData?.condition as { label: string })?.label || 'Unknown';

        const main_image_url = mediaMap.get(item.product_id) || '';

        return {
          ...item,
          product: {
            ...productData,
            condition,
            category: categoryData,
            main_image_url
          }
        };
      });

      setCartItems(formattedCartItems);

      // Fetch shipping addresses
      const { data: addressData, error: addressError } = await supabase
        .from('shipping_addresses')
        .select('*')
        .eq('user_id', session.user.id)
        .order('is_default', { ascending: false });

      if (addressError) throw addressError;

      setAddresses(addressData || []);

      // Set default address if available
      if (addressData && addressData.length > 0) {
        const defaultAddress = addressData.find(addr => addr.is_default);
        setSelectedAddressId(defaultAddress ? defaultAddress.id : addressData[0].id);
      } else {
        setNewAddress(true);
      }

      // Load discount information
      loadDiscountInfo();
    } catch (error) {
      console.error('Error fetching checkout data:', error);
      customToast.error('Failed to load checkout information. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Validate checkout fields
  const validateCheckout = (): boolean => {
    if (newAddress) {
      return Boolean(street && city && state && country && postalCode);
    }
    return Boolean(selectedAddressId);
  };

  // Update checkout validation
  useEffect(() => {
    setIsCheckoutValid(validateCheckout());
  }, [street, city, state, country, postalCode, selectedAddressId, newAddress]);
  
  // Calculate totals
  const subtotal = cartItems.reduce((acc, item) => acc + (item.product.price * item.quantity), 0);
  
  // Update shipping info when address changes
  useEffect(() => {
    if (selectedAddressId) {
      const selectedAddress = addresses.find(addr => addr.id === selectedAddressId);
      if (selectedAddress && selectedAddress.country && selectedAddress.city && selectedAddress.postal_code) {
        const shipping = calculateShipping(Number(subtotal), selectedAddress.country, selectedAddress.city, selectedAddress.postal_code);

        setShippingInfo((prev) => {
          if (
            prev.cost !== shipping.cost ||
            prev.currency !== shipping.currency ||
            prev.estimatedDays !== shipping.estimatedDays ||
            prev.description !== shipping.description
          ) {
            return shipping;
          }
          return prev;
        });
      }
    } else if (newAddress && country) {
      // Calculate shipping for new address being entered
      const shipping = calculateShipping(Number(subtotal), country, city, postalCode);

      setShippingInfo((prev) => {
        if (
          prev.cost !== shipping.cost ||
          prev.currency !== shipping.currency ||
          prev.estimatedDays !== shipping.estimatedDays ||
          prev.description !== shipping.description
        ) {
          return shipping;
        }
        return prev;
      });
    }
  }, [selectedAddressId, addresses, subtotal, newAddress, country, city, postalCode]);
  
  const total = Math.max(0, subtotal + shippingInfo.cost - (discountInfo?.discount || 0));
  
  // Handle new address submission
  const handleAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsProcessing(true);
      
      if (!session) {
        router.push('/auth/login?redirect=/checkout');
        return;
      }
      
      // Create new address
      const { data: newAddressData, error: addressError } = await supabase
        .from('shipping_addresses')
        .insert([
          {
            user_id: session.user.id,
            name: session.user.email?.split('@')[0] || 'Customer',
            street: street,
            city,
            state,
            country,
            postal_code: postalCode,
            is_default: addresses.length === 0 // Make default if first address
          }
        ])
        .select()
        .single();
      
      if (addressError) throw addressError;
      
      // Update addresses list
      setAddresses(prev => [...prev, newAddressData]);
      setSelectedAddressId(newAddressData.id);
      setNewAddress(false);
      
      customToast.success('Address saved successfully');
    } catch (error) {
      console.error('Error saving address:', error);
      customToast.error('Failed to save address. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Handle PayPal order success
  const handlePayPalSuccess = async (orderData: any) => {
    try {
      setProcessingStatus('processing');
      
      if (!session) {
        router.push('/auth/login?redirect=/checkout');
        return;
      }

      // Clear cart after successful order
      await supabase
        .from('cart_items')
        .delete()
        .eq('user_id', session.user.id);

      // Clear discount information from sessionStorage
      if (typeof window !== 'undefined' && window.sessionStorage) {
        try {
          sessionStorage.removeItem('checkout_discount');
        } catch (error) {
          console.error('Error clearing discount from sessionStorage:', error);
        }
      }

      // Redirect to success page
      router.push('/checkout/success');
    } catch (error) {
      console.error('Error processing order:', error);
      setProcessingStatus('error');
      setErrorMessage('Failed to process order. Please try again.');
    }
  };
  
  // Login prompt modal
  const LoginPrompt = () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl p-6 max-w-md w-full">
        <h2 className="text-xl font-heading text-[#171717] mb-4">Sign in to Continue</h2>
        <p className="text-gray-600 mb-6">
          Please sign in to complete your purchase.
        </p>
        <div className="flex flex-col gap-3">
          <Link
            href="/login?redirect=checkout"
            className="w-full px-4 py-2 bg-[#171717] text-white rounded-md text-center hover:bg-[#2a2a2a] transition-colors"
          >
            Sign In
          </Link>
        </div>
      </div>
    </div>
  );
  
  // Handle payment method change
  const handlePaymentMethodChange = (method: 'paypal' | 'stripe') => {
    setSelectedPaymentMethod(method);
  };

  // Show loading state while checking auth
  if (isAuthLoading) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
      </div>
    );
  }

  // Show loading state while fetching checkout data
  if (loading) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading checkout information...</p>
        </div>
      </div>
    );
  }
  
  if (processingStatus === 'processing' || processingStatus === 'success' || processingStatus === 'error') {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="max-w-md w-full p-8 bg-white rounded-xl shadow-sm text-center">
          {processingStatus === 'processing' && (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717] mx-auto mb-6"></div>
              <h2 className="text-xl font-heading mb-2">Processing Your Order</h2>
              <p className="text-gray-600">Please wait while we process your payment with PayPal...</p>
            </>
          )}
          
          {processingStatus === 'success' && (
            <>
              <div className="w-16 h-16 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-xl font-heading mb-2">Order Successful!</h2>
              <p className="text-gray-600">Your order has been placed successfully. Redirecting to confirmation...</p>
            </>
          )}
          
          {processingStatus === 'error' && (
            <>
              <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h2 className="text-xl font-heading mb-2">Payment Failed</h2>
              <p className="text-red-600 mb-4">{errorMessage}</p>
              <button
                onClick={() => {
                  setProcessingStatus('idle');
                  setIsProcessing(false);
                }}
                className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333]"
              >
                Try Again
              </button>
            </>
          )}
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen pt-20 pb-12 bg-[#f8f8f8]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-3xl font-heading tracking-wider text-[#171717] mb-6">Checkout</h1>
        
        {/* Checkout Progress */}
        <div className="mb-8">
          <div className="flex mb-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div 
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border-2 ${
                    checkoutStep === index 
                      ? 'border-[#171717] bg-[#171717] text-white' 
                      : checkoutStep > index 
                        ? 'border-emerald-500 bg-emerald-500 text-white'
                        : 'border-gray-300 text-gray-500'
                  }`}
                >
                  {checkoutStep > index ? (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  ) : (
                    index + 1
                  )}
                </div>
                
                <div className={`hidden sm:block ml-2 text-sm font-medium ${
                  checkoutStep >= index ? 'text-[#171717]' : 'text-gray-500'
                }`}>
                  {step.label}
                </div>
                
                {index < steps.length - 1 && (
                  <div className="flex-1 border-t-2 border-gray-300 mx-2 max-w-[50px]"></div>
                )}
              </div>
            ))}
          </div>
          
          {/* Progress bar */}
          <div className="w-full bg-gray-200 rounded-full h-1.5 mb-4">
            <motion.div 
              className="bg-[#171717] h-1.5 rounded-full" 
              initial={{ width: 0 }}
              animate={{ width: `${(checkoutStep / (steps.length - 1)) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <motion.div
            className="lg:col-span-2 space-y-8"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Shipping Address */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-heading text-[#171717]">Shipping Address</h2>
                {!newAddress && addresses.length > 0 && (
                  <button
                    onClick={() => setNewAddress(true)}
                    className="text-sm text-[#171717] hover:underline"
                  >
                    Add New Address
                  </button>
                )}
              </div>
              
              {errorMessage && (
                <div className="mb-6 p-3 bg-red-50 text-red-600 rounded-md">
                  {errorMessage}
                </div>
              )}
              
              {newAddress ? (
                <form onSubmit={handleAddressSubmit}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Address Line 1*
                      </label>
                      <input
                        type="text"
                        value={street}
                        onChange={(e) => setStreet(e.target.value)}
                        required
                        placeholder="Street address, P.O. box, company name, c/o"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-200"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        City*
                      </label>
                      <input
                        type="text"
                        value={city}
                        onChange={(e) => setCity(e.target.value)}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-200"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        State/Province*
                      </label>
                      <input
                        type="text"
                        value={state}
                        onChange={(e) => setState(e.target.value)}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-200"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Country*
                      </label>
                      <input
                        type="text"
                        value={country}
                        onChange={(e) => setCountry(e.target.value)}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-200"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Postal Code*
                      </label>
                      <input
                        type="text"
                        value={postalCode}
                        onChange={(e) => setPostalCode(e.target.value)}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-200"
                      />
                    </div>
                  </div>
                  
                  <div className="mt-6 flex space-x-3">
                    <button
                      type="submit"
                      className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333]"
                    >
                      Save Address
                    </button>
                    <button
                      type="button"
                      onClick={() => setNewAddress(false)}
                      className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              ) : addresses.length === 0 ? (
                <div className="text-center py-8 border border-dashed border-gray-300 rounded-md">
                  <svg className="w-12 h-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <p className="text-gray-500 mb-4">You don't have any saved addresses</p>
                  <button
                    onClick={() => setNewAddress(true)}
                    className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333]"
                  >
                    Add Address
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {addresses.map((address) => (
                    <div
                      key={address.id}
                      className={`border rounded-md p-4 cursor-pointer transition-colors overflow-hidden ${
                        selectedAddressId === address.id
                          ? 'border-[#171717] bg-gray-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedAddressId(address.id)}
                    >
                      <div className="flex justify-between mb-2">
                        <h3 className="font-medium truncate pr-2">
                          {address.name?.includes('@')
                            ? address.name.split('@')[0]
                            : address.name || 'Address'
                          }
                        </h3>
                        {address.is_default && (
                          <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded flex-shrink-0">
                            Default
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{address.street}</p>
                      <p className="text-sm text-gray-600">
                        {address.city}, {address.state} {address.postal_code}
                      </p>
                      <p className="text-sm text-gray-600">{address.country}</p>
                      
                      <div className="mt-3 flex items-center">
                        <input
                          type="radio"
                          id={`address-${address.id}`}
                          name="shipping-address"
                          checked={selectedAddressId === address.id}
                          onChange={() => setSelectedAddressId(address.id)}
                          className="h-4 w-4 text-[#171717] border-gray-300 focus:ring-[#171717]"
                        />
                        <label htmlFor={`address-${address.id}`} className="ml-2 text-sm text-gray-700">
                          Ship to this address
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Payment Method Section */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-heading text-[#171717] mb-6">Payment Method</h2>
              
              <div className="flex gap-4 mb-6">
                <button
                  onClick={() => handlePaymentMethodChange('paypal')}
                  className={`flex-1 p-4 border rounded-md ${selectedPaymentMethod === 'paypal' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-white'}`}
                >
                  <div className="flex items-center justify-center">
                    <Image src="https://www.paypalobjects.com/webstatic/icon/pp258.png" alt="PayPal" width={80} height={30} style={{ height: 'auto', width: 'auto' }} />
                  </div>
                  <p className="text-sm text-center mt-2">Pay with PayPal</p>
                </button>
                <button
                  onClick={() => handlePaymentMethodChange('stripe')}
                  className={`flex-1 p-4 border rounded-md ${selectedPaymentMethod === 'stripe' ? 'border-gray-700 bg-gray-50' : 'border-gray-300 bg-white'}`}
                >
                  <div className="flex items-center justify-center">
                    <CreditCard className="w-8 h-8 text-gray-700" />
                  </div>
                  <p className="text-sm text-center mt-2">Pay with Card</p>
                </button>
              </div>
              
              {selectedPaymentMethod === 'paypal' && isCheckoutValid && process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID && (
                <PayPalCheckoutButton
                  items={cartItems}
                  shippingInfo={shippingInfo}
                  userEmail={session?.user?.email}
                  discountInfo={discountInfo}
                  onSuccess={handlePayPalSuccess}
                  disabled={!isCheckoutValid}
                />
              )}
              
              {selectedPaymentMethod === 'stripe' && isCheckoutValid && (
                <div className="mt-4">
                  <StripeCheckoutButton
                    items={cartItems}
                    customerEmail={session?.user?.email || ''}
                    shippingInfo={shippingInfo}
                    discountInfo={discountInfo}
                    disabled={!isCheckoutValid}
                    onError={() => {
                      return customToast.error('Failed to process payment. Please try again.');
                    }}
                  />
                </div>
              )}
              
              <p className="text-xs text-gray-500 text-center mt-6">
                By placing your order, you agree to our Terms of Service and Privacy Policy
              </p>
            </div>
          </motion.div>
          
          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-6 sticky top-24">
              <h2 className="text-xl font-heading text-[#171717] mb-4">Order Summary</h2>
              
              <div className="max-h-64 overflow-y-auto mb-4">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex py-3 border-b border-gray-100">
                    <div className="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-md overflow-hidden relative">
                      {item.product.main_image_url ? (
                        <Image
                          src={getCloudinaryUrl(item.product.main_image_url)}
                          alt={item.product.name}
                          width={64}
                          height={64}
                          className="object-cover w-full h-full"
                          sizes="64px"
                          onError={(e) => {
                            // Replace with a placeholder on error
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full bg-gray-200">
                          <span className="text-gray-400 text-xs">No image</span>
                        </div>
                      )}
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-sm font-medium text-[#171717] line-clamp-1">{item.product.name}</h3>
                      <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                      <p className="text-sm font-medium">€{item.product.price.toLocaleString()}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">€{subtotal.toLocaleString()}</span>
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">€{shippingInfo.cost.toLocaleString()}</span>
                </div>

                {discountInfo?.applied && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount ({discountInfo.code})</span>
                    <span>-€{discountInfo.discount.toFixed(2)}</span>
                  </div>
                )}

                <div className="flex justify-between pt-2 mt-2 border-t border-gray-200">
                  <span className="font-semibold">Total</span>
                  <span className="font-semibold">€{total.toLocaleString()}</span>
                </div>
              </div>
              
              {!validateCheckout() && (
                <button
                  onClick={() => validateCheckout()}
                  className="w-full mt-6 px-6 py-3 bg-[#0070ba] text-white rounded-md hover:bg-[#003087] disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                >
                  <Image src="https://www.paypalobjects.com/webstatic/icon/pp258.png" alt="PayPal" width={60} height={25} sizes="(max-width: 768px) 100vw, 33vw" className="mr-2" style={{ height: 'auto', width: 'auto' }} />
                  <span>Checkout</span>
                </button>
              )}
              
              <p className="text-xs text-gray-500 text-center mt-4">
                100% secure payment processing with PayPal
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 