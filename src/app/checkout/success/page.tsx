'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { CheckCircle } from 'lucide-react';

export default function SuccessPage() {
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error'>('pending');
  
  const searchParams = useSearchParams();
  const router = useRouter();
  const sessionId = searchParams.get('session_id');
  const orderId = searchParams.get('order_id');
  const supabase = createClientComponentClient();
  
  useEffect(() => {
    // Log the query parameters for debugging
    console.log('Success page loaded with params:', { 
      sessionId, 
      orderId,
      url: window.location.href
    });
    
    async function fetchOrderDetails() {
      if (!orderId) {
        setError('No order ID found');
        setLoading(false);
        return;
      }
      
      try {
        // First, verify the payment status with Stripe
        if (sessionId) {
          try {
            const verifyResponse = await fetch(`/api/stripe/verify-payment?session_id=${sessionId}&order_id=${orderId}`);
            if (!verifyResponse.ok) {
              console.warn('Payment verification warning:', await verifyResponse.text());
              // Continue anyway, as the webhook might have already processed this
            } else {
              console.log('Payment verified successfully');
              setVerificationStatus('success');
            }
          } catch (verifyError) {
            console.error('Error verifying payment:', verifyError);
            // Continue anyway, as the webhook might have already processed this
          }
        }
        
        // Now fetch the order details
        const { data, error } = await supabase
          .from('orders')
          .select('*, order_items(*, product:products(name))')
          .eq('id', orderId)
          .limit(1)
          .single();
        
        if (error) throw error;
        
        if (data) {
          setOrderDetails(data);

          // Safely clear cart items from storage
          if (typeof window !== 'undefined') {
            try {
              localStorage.removeItem('shop-maimi-guest-cart');
              localStorage.removeItem('cart');
              
              // Dispatch storage event to update cart count in header
              window.dispatchEvent(new Event('storage'));
            } catch (storageError) {
              console.error('[Success] Error clearing storage:', storageError);
            }
          }
          
          // Clear cart items for the user from database
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session && data.user_id) {
            await supabase
              .from('cart_items')
              .delete()
              .eq('user_id', data.user_id);
          }

          // Redirect to clean URL without query params after a short delay
          setTimeout(() => {
            router.replace('/checkout/success');
          }, 3000);
        } else {
          setError('Order not found');
        }
      } catch (err) {
        console.error('Error fetching order details:', err);
        setError('Failed to load order details');
        router.push('/account');
      } finally {
        setLoading(false);
      }
    }
    
    if (sessionId && orderId) {
      fetchOrderDetails();
    }
  }, [orderId, sessionId, supabase, router]);
  
  return (
    <div className="max-w-4xl mx-auto px-4 py-12">
      <div className="bg-white rounded-lg shadow-md p-6">
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Verifying your order...</p>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 mb-6">
            <p>{error}</p>
            <Link href="/" className="text-sm underline mt-2 block">Go back home</Link>
          </div>
        ) : orderDetails ? (
          <div className="border-t border-gray-200 pt-6">
            <div className="flex items-center justify-center mb-6">
              <CheckCircle className="h-16 w-16 text-green-500 mr-3" />
              <h1 className="text-2xl font-bold text-gray-800">Order Confirmed!</h1>
            </div>
            
            <p className="text-center text-gray-600 mb-8">
              Thank you for your purchase. Your order has been confirmed and will be shipped soon.
            </p>
            
            <div className="border-t border-b border-gray-200 py-4 mb-6">
              <h2 className="font-semibold text-lg mb-4">Order Summary</h2>
              <div className="space-y-3">
                {orderDetails.order_items?.map((item: any) => (
                  <div key={item.id} className="flex justify-between">
                    <div>
                      <span className="font-medium">{item.product?.name}</span>
                      <span className="text-gray-500 ml-2">x{item.quantity}</span>
                    </div>
                    <span>€{(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="flex justify-between font-semibold text-lg mb-6">
              <span>Total</span>
              <span>€{orderDetails.total_amount?.toFixed(2)}</span>
            </div>
            
            <div className="flex justify-center space-x-4">
              <Link
                href="/account"
                className="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                View My Account
              </Link>
              <Link
                href="/"
                className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Continue Shopping
              </Link>
            </div>
          </div>
        ) : (
          <div className="text-center text-red-500">
            <p>No order information found</p>
            <Link href="/" className="text-sm underline mt-2 block">Go back home</Link>
          </div>
        )}
      </div>
    </div>
  );
}
