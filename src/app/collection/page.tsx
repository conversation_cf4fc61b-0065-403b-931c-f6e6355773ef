'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import type { Database } from '@/lib/database.types';
import { getCloudinaryUrl, getProductImageUrl } from '@/lib/image-utils';

// Define types using the database schema
type ProductRow = Database['public']['Tables']['products']['Row'];
type CategoryRow = Database['public']['Tables']['categories']['Row'];

// Define a MediaItem type that matches the database schema
interface MediaItem {
  id: string;
  product_id: string | null;
  url: string;
  type?: string;
  position?: number | null;
  alt?: string | null;
  created_at?: string | null;
  is_main?: boolean | null;
}

// Define Collection interface based on the categories table
interface Collection {
  id: string;
  name: string;
  slug: string | null;
  description: string | null;
  image_url: string | null;
  parent_id?: string | null;
  products_count?: number;
}

// Create a type that extends ProductRow but with proper specifications handling
interface Product extends Omit<ProductRow, 'specifications'> {
  image_url?: string;
  mediaItems?: MediaItem[];
  specifications?: {
    condition?: string;
    [key: string]: any;
  } | null;
  category?: { name: string } | null;
}

interface CategoryLink {
  title: string;
  image: string;
  link: string;
}

export default function CollectionPage() {
  const supabase = createClientComponentClient<Database>();
  const [collections, setCollections] = useState<Collection[]>([]);
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [error, setError] = useState('');
  const [activeCollection, setActiveCollection] = useState<string | null>(null);
  const [collectionProducts, setCollectionProducts] = useState<Product[]>([]);
  const [categoryLinks, setCategoryLinks] = useState<CategoryLink[]>([]);

  useEffect(() => {
    fetchCollections();
  }, []);

  const fetchCollections = async () => {
    try {
      setLoading(true);
      setError('');

      // Check if collections table exists in the database
      const { data: collectionsData, error: collectionsError } = await supabase
        .from('collections')
        .select('*');

      if (collectionsError || !collectionsData || collectionsData.length === 0) {
        setError('No collections found.');
        setLoading(false);
        return;
      }

      // Format the collections data to match our Collection interface
      const formattedCollections: Collection[] = collectionsData.map(cat => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        description: cat.description,
        image_url: cat.image_url,
        products_count: 0 // We'll update this if needed
      }));

      setCollections(formattedCollections);

      if (formattedCollections.length > 0) {
        setActiveCollection(formattedCollections[0].id);
        fetchCollectionProducts(formattedCollections[0].id);
      }

      // Fetch category links
      const { data: categoriesData } = await supabase
        .from('categories')
        .select('id, name, slug, image_url');

      if (categoriesData) {
        const categoryLinksWithFallback = await Promise.all(
          categoriesData.map(async (cat) => {
            const { data: productData } = await supabase
              .from('products')
              .select('id')
              .eq('category_id', cat.id)
              .limit(1);

            if (!productData || productData.length === 0) return null;

            const productId = productData[0].id;

            const { data: mediaData } = await supabase
              .from('product_media')
              .select('url')
              .eq('product_id', productId)
              .order('created_at', { ascending: true });

            const fallbackImage = mediaData?.[1]?.url
              ? getCloudinaryUrl(mediaData[1].url)
              : '';

            if (!fallbackImage) return null;

            return {
              title: cat.name,
              image: fallbackImage,
              link: `/category/${cat.slug}`
            };
          })
        );

        setCategoryLinks(categoryLinksWithFallback.filter(Boolean) as CategoryLink[]);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching collections:', error);
      setError('Failed to load collections.');
      setLoading(false);
    }
  };

  // Fetch products for a specific collection
  const fetchCollectionProducts = async (collectionId: string) => {
    try {
      setLoadingProducts(true);
      const { data: productsData, error: productError } = await supabase
        .from('products')
        .select(`
          *,
          product_media:product_media(*),
          category:categories(name)
        `)
        .eq('collection_id', collectionId);

      if (productError || !productsData) {
        setCollectionProducts([]);
        setLoadingProducts(false);
        return;
      }

      const productsWithDetails = productsData.map(p => {
        const specifications = p.specifications || {};
        const mediaItems = p.product_media || [];
        
        // Convert mediaItems to the expected Database type format
        const typedMediaItems: Database['public']['Tables']['product_media']['Row'][] = 
          mediaItems.map(item => ({
            id: item.id,
            product_id: item.product_id,
            url: item.url,
            type: item.type || 'image',
            position: item.position || null,
            alt: item.alt || null,
            created_at: item.created_at || null,
            is_main: item.is_main || null
          }));
        
        return {
          ...p,
          specifications,
          mediaItems: typedMediaItems,
          image_url: getProductImageUrl(typedMediaItems),
          category: p.category,
        } as Product;
      });

      setCollectionProducts(productsWithDetails);
      setLoadingProducts(false);
    } catch (err) {
      console.error('Error fetching collection products:', err);
      setLoadingProducts(false);
    }
  };

  // Handle collection change
  const handleCollectionChange = (collectionId: string) => {
    setActiveCollection(collectionId);
    fetchCollectionProducts(collectionId);
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="max-w-md w-full p-8 bg-white rounded-xl shadow-sm text-center">
          <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 className="text-xl font-heading mb-2">Error Loading Collections</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link 
            href="/products" 
            className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333]"
          >
            Shop Now
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-12 bg-[#f8f8f8]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="relative h-80 md:h-96 w-full mb-12 rounded-xl overflow-hidden">
          <Image
            src="/images/luxury-collection.JPG"
            alt="Luxury Collections"
            fill
            className="object-cover object-center"
            priority
          />
          <div className="absolute inset-0 bg-black/30 flex flex-col items-center justify-center text-center p-6">
            <h1 className="text-4xl md:text-5xl font-heading text-white tracking-wider mb-4">
              Our Collections
            </h1>
            <p className="text-lg text-white max-w-2xl">
              Explore our curated collections of luxury items, each carefully selected to offer you the finest quality and style.
            </p>
          </div>
        </div>
        
        {/* Collections Navigation */}
        <div className="mb-12">
          <div className="flex flex-wrap gap-4 justify-center">
            {collections.map((collection) => (
              <button
                key={collection.id}
                onClick={() => handleCollectionChange(collection.id)}
                className={`px-6 py-3 rounded-full text-sm font-medium transition-colors ${
                  activeCollection === collection.id
                    ? 'bg-[#171717] text-white'
                    : 'bg-white border border-gray-200 text-gray-800 hover:bg-gray-50'
                }`}
              >
                {collection.name} ({collection.products_count})
              </button>
            ))}
          </div>
        </div>
        
        {/* Active Collection Header */}
        {activeCollection && (
          <div className="mb-8 text-center">
            <motion.div
              key={activeCollection}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-3xl font-heading text-[#171717] mb-2">
                {collections.find(c => c.id === activeCollection)?.name}
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                {collections.find(c => c.id === activeCollection)?.description}
              </p>
            </motion.div>
          </div>
        )}
        
        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {loadingProducts ? (
            // Loading skeleton for products
            Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="bg-white rounded-xl overflow-hidden shadow-sm">
                <div className="w-full h-64 bg-gray-200 animate-pulse"></div>
                <div className="p-4 space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-6 w-1/3 bg-gray-200 rounded animate-pulse mt-2"></div>
                </div>
              </div>
            ))
          ) : collectionProducts.length > 0 ? (
            collectionProducts.map((product) => (
              <motion.div 
                key={product.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
                className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow"
              >
                <Link href={`/products/${product.slug}`} className="block">
                  <div className="relative h-64 w-full bg-gray-100">
                    {product.image_url ? (
                      <Image
                        src={product.image_url}
                        alt={product.name}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <span className="text-gray-400">No image</span>
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <div className="text-xs text-gray-500 mb-1">{product.category?.name || 'Uncategorized'}</div>
                    <h3 className="font-medium text-[#171717] line-clamp-1">{product.name}</h3>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="font-medium text-lg">${product.price.toLocaleString()}</span>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))
          ) : (
            <div className="col-span-full py-12 text-center">
              <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 12H4M8 16l-4-4m0 0l4-4m-4 4h16" />
              </svg>
              <h3 className="text-xl font-medium text-gray-900 mb-1">No products found</h3>
              <p className="text-gray-500">There are no products in this collection yet.</p>
            </div>
          )}
        </div>
        
        {/* Pagination/Load More (if needed for more than 40 products) */}
        {collectionProducts.length >= 40 && (
          <div className="mt-12 text-center">
            <button
              className="px-6 py-3 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Load More Products
            </button>
          </div>
        )}
        
        {/* Collection Highlights */}
        <div className="mt-24 mb-12">
          <h2 className="text-2xl font-heading text-center text-[#171717] mb-12">
            Explore Our Categories
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {categoryLinks.length > 0 ? (
              categoryLinks.map((category, index) => (
                <Link key={index} href={category.link} className="block group">
                  <div className="relative h-80 w-full rounded-xl overflow-hidden">
                    {category.image && (
                      <Image
                        src={category.image}
                        alt={category.title}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-105"
                      />
                    )}
                    <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors">
                      <div className="absolute bottom-0 left-0 right-0 p-6">
                        <h3 className="text-xl font-medium text-white">{category.title}</h3>
                        <p className="text-white/80 mt-1 group-hover:text-white">Shop Now</p>
                      </div>
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              <div className="col-span-full py-12 text-center">
                <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 12H4M8 16l-4-4m0 0l4-4m-4 4h16" />
                </svg>
                <h3 className="text-xl font-medium text-gray-900 mb-1">No categories found</h3>
                <p className="text-gray-500">There are no categories in the database.</p>
              </div>
            )}
          </div>
        </div>
        
        {/* Newsletter / Join CTA */}
        <div className="mt-24 bg-[#171717] text-white rounded-xl p-8 md:p-12">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-heading mb-4">
              Join Our Collector's Circle
            </h2>
            <p className="mb-6 text-gray-300">
              Subscribe to receive early access to new collections, exclusive offers, and curated content from our luxury experts.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Your email address"
                className="flex-1 px-4 py-3 rounded-md bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30"
              />
              <button className="px-6 py-3 bg-white text-[#171717] rounded-md font-medium hover:bg-gray-100 transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}