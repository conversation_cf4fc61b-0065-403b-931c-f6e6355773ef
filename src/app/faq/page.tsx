'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
}

const faqs: FAQItem[] = [
  {
    question: "How do you ensure the authenticity of your items?",
    answer: "Each piece in our collection undergoes a rigorous authentication process. Our team carefully examines every detail, from materials and craftsmanship to serial numbers and brand markings. We provide detailed authenticity certificates with each purchase."
  },
  {
    question: "What is your shipping policy?",
    answer: "We offer worldwide shipping from our location in Valencia, Spain. Orders are carefully packaged and typically ship within 1-2 business days. Delivery times vary by location, and tracking information is provided for all shipments."
  },
  {
    question: "Can I return or exchange an item?",
    answer: "Yes, we accept returns within 14 days of delivery. Items must be in their original condition with all tags attached. Please note that due to the unique nature of our pieces, some items may be final sale, which will be clearly indicated in the product description."
  },
  {
    question: "How do you determine the condition of vintage items?",
    answer: "We thoroughly inspect each item and provide detailed condition reports. Our condition ratings range from 'Excellent' to 'Good,' with any imperfections clearly documented in the product description and photos."
  },
  {
    question: "Do you offer personal shopping services?",
    answer: "Yes! If you're looking for a specific vintage piece, we offer a personalized sourcing service. Contact us with your requirements, and our team will help find the perfect item for your collection."
  },
  {
    question: "How do I care for vintage luxury items?",
    answer: "Each item comes with specific care instructions. Generally, we recommend professional cleaning for vintage pieces. Store items in a cool, dry place away from direct sunlight, and use appropriate storage bags or boxes."
  },
  {
    question: "Do you ship internationally?",
    answer: "Yes, we ship worldwide. International orders may be subject to import duties and taxes, which are the responsibility of the customer. We'll provide estimated delivery times during checkout."
  },
  {
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards, PayPal, and bank transfers. All payments are processed securely through our payment partners to ensure your financial information is protected."
  }
];

export default function FAQPage() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  return (
    <div className="min-h-screen bg-[#f8f8f8] pt-32 pb-24">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-4xl md:text-5xl font-heading tracking-wider text-[#171717] mb-8 text-center">
            Frequently Asked Questions
          </h1>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-sm overflow-hidden"
              >
                <button
                  onClick={() => setOpenIndex(openIndex === index ? null : index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-300"
                >
                  <span className="font-heading text-lg text-[#171717]">
                    {faq.question}
                  </span>
                  <ChevronDown
                    className={`w-5 h-5 text-[#171717] transition-transform duration-300 ${
                      openIndex === index ? 'transform rotate-180' : ''
                    }`}
                  />
                </button>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="px-6 pb-4"
                  >
                    <p className="font-body text-[#666666] leading-relaxed pt-2">
                      {faq.answer}
                    </p>
                  </motion.div>
                )}
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <p className="text-[#666666] mb-4">
              Still have questions? We're here to help!
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-block px-8 py-3 bg-[#171717] text-white font-body tracking-wide text-sm hover:bg-[#333333] transition-colors duration-300 rounded-md"
            >
              Contact Us
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
