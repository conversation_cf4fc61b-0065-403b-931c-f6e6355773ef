@import url('https://fonts.googleapis.com/css2?family=Allerta+Stencil&family=ABeeZee:ital@0;1&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 23, 23, 23;
  --background-rgb: 245, 245, 245;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family: 'ABeeZee', system-ui, sans-serif;
}

.font-heading {
  font-family: 'Allerta Stencil', system-ui, sans-serif;
}

.font-body {
  font-family: 'ABee<PERSON>ee', system-ui, sans-serif;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f5f5f5;
}

::-webkit-scrollbar-thumb {
  background: #171717;
}

::-webkit-scrollbar-thumb:hover {
  background: #333333;
}

/* Focus Styles */
*:focus-visible {
  outline: 2px solid #171717;
  outline-offset: 2px;
}

/* Hover Effects */
.hover-underline {
  position: relative;
}

.hover-underline::after {
  content: '';
  position: absolute;
  width: 0;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.hover-underline:hover::after {
  width: 100%;
}

/* Image Loading */
.image-loading {
  position: relative;
  overflow: hidden;
}

.image-loading::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Button Styles */
.btn-primary {
  @apply inline-block px-12 py-4 bg-[#171717] text-white font-body tracking-[0.15em] text-sm hover:bg-[#333333] transition-all duration-300;
}

.btn-secondary {
  @apply inline-block px-12 py-4 bg-white/10 backdrop-blur-sm text-white border border-white/30 font-body tracking-[0.15em] text-sm hover:bg-white/20 transition-all duration-300;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  @apply font-heading tracking-[0.2em];
}

.text-display {
  @apply text-4xl md:text-6xl font-heading tracking-[0.2em];
}

.text-heading {
  @apply text-3xl font-heading tracking-[0.2em];
}

.text-subheading {
  @apply text-xl font-body italic;
}

/* Container */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Section Spacing */
.section-py {
  @apply py-24;
}

.section-mt {
  @apply mt-24;
}

.section-mb {
  @apply mb-24;
}
