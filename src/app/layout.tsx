import './globals.css'
import { Toaster } from 'react-hot-toast';
// Temporarily using system fonts to avoid Google Fonts build issues
// import { Allerta_Stencil, ABee<PERSON>ee } from 'next/font/google';
import LayoutWrapper from '@/components/LayoutWrapper';
import PayPalProvider from '@/components/paypal/PayPalProvider';
import WishlistProvider from '@/context/WishlistContext';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { errorLogger } from '@/lib/error-logger';
import StructuredData from '@/components/StructuredData';
import Script from 'next/script';

// Temporarily disabled Google Fonts to fix build issues
// const allerta = Allerta_Stencil({
//   weight: ['400'],
//   subsets: ['latin'],
//   variable: '--font-allerta',
//   display: 'swap',
// });

// const abeezee = ABeeZee({
//   weight: ['400'],
//   subsets: ['latin'],
//   variable: '--font-abeezee',
//   display: 'swap',
// });

export const metadata = {
  title: {
    default: 'Treasures of Maimi - Luxury Vintage Fashion Boutique',
    template: '%s | Treasures of Maimi'
  },
  description: 'Discover curated vintage luxury fashion at Treasures of Maimi. Shop authenticated pre-loved designer pieces from Louis Vuitton, Chanel, Gucci, Hermès & more. Each item comes with a certificate of authenticity. Sustainable luxury fashion with AI-powered recommendations and worldwide shipping from Miami.',
  applicationName: 'Treasures of Maimi',
  referrer: 'origin-when-cross-origin',
  keywords: [
    'luxury vintage fashion',
    'authenticated designer bags',
    'pre-loved luxury items',
    'sustainable fashion boutique',
    'vintage designer handbags',
    'luxury consignment Miami',
    'circular fashion economy',
    'AI recommended vintage',
    'curated luxury pieces',
    'authentic vintage designer',
    'Louis Vuitton vintage bags',
    'Chanel vintage accessories',
    'Gucci vintage collection',
    'Hermès vintage items',
    'Fendi vintage pieces',
    'designer resale platform',
    'luxury authentication service',
    'vintage fashion investment',
    'sustainable luxury shopping',
    'eco-friendly designer fashion',
    'certified authentic vintage',
    'Miami luxury boutique',
    'global luxury shipping',
    'vintage fashion curation',
    'luxury fashion sustainability'
  ].join(', '),
  authors: [{ name: 'Treasures of Maimi', url: 'https://treasuresofmaimi.com' }],
  creator: 'Treasures of Maimi',
  publisher: 'Treasures of Maimi',
  formatDetection: {
    email: false,
    telephone: false,
  },
  metadataBase: new URL('https://treasuresofmaimi.com'),
  alternates: {
    canonical: '/',
    languages: {
      'en-US': '/en-US',
      'es-ES': '/es-ES'
    }
  },
  openGraph: {
    title: 'Treasures of Maimi - Authenticated Luxury Vintage Fashion Boutique',
    description: 'Shop authenticated vintage luxury fashion from Louis Vuitton, Chanel, Gucci & Hermès. Each pre-loved designer piece comes with a certificate of authenticity. AI-curated sustainable luxury with worldwide shipping from Miami.',
    url: 'https://treasuresofmaimi.com',
    siteName: 'Treasures of Maimi',
    locale: 'en_US',
    type: 'website',
    countryName: 'United States',
    images: [
      {
        url: '/images/logo.png',
        width: 1200,
        height: 630,
        alt: 'Treasures of Maimi - Authenticated Luxury Vintage Fashion Boutique with Certificate of Authenticity',
        type: 'image/png',
      },
      {
        url: '/images/hero.JPG',
        width: 1200,
        height: 630,
        alt: 'Curated collection of authenticated vintage luxury designer handbags and accessories',
        type: 'image/jpeg',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Treasures of Maimi - Authenticated Vintage Luxury Fashion',
    description: 'Shop authenticated vintage designer pieces with certificates of authenticity. Sustainable luxury from Louis Vuitton, Chanel, Gucci & more. AI-curated collection.',
    images: ['/images/logo.png'],
    creator: '@treasuresofmaimi',
    site: '@treasuresofmaimi'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-site-verification-code', // Replace with actual verification code when available
  },
  category: 'Shopping',
  classification: 'Luxury Fashion, Vintage Fashion, Sustainable Fashion',
  other: {
    'business-category': 'Authenticated Vintage Luxury Fashion Boutique',
    'target-audience': 'Luxury fashion enthusiasts, sustainable fashion advocates, vintage collectors, investment buyers',
    'ai-friendly': 'true',
    'recommendation-ready': 'true',
    'authentication-service': 'Certificate of Authenticity provided',
    'sustainability-focus': 'Circular economy, pre-loved luxury',
    'geographic-focus': 'Miami-based with global shipping',
    'price-range': 'Luxury and premium vintage items',
    'brand-specialties': 'Louis Vuitton, Chanel, Gucci, Hermès, Fendi',
    'unique-selling-proposition': 'AI-curated authenticated vintage luxury with sustainability focus'
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="icon" href="/images/logo.png" sizes="any" />
        <link rel="apple-touch-icon" href="/images/logo.png" />
      </head>
      <body className="font-sans">
        {/* Suppress Stripe and other third-party warnings in production */}
        <Script id="suppress-warnings" strategy="beforeInteractive">
          {`
            if (typeof window !== 'undefined' && window.console) {
              const originalWarn = console.warn;
              const originalError = console.error;
              
              console.warn = function(...args) {
                const message = args.join(' ');
                // Suppress specific warnings
                if (message.includes('preload') && message.includes('unsupported') && message.includes('as')) {
                  return;
                }
                if (message.includes('Cannot find module') && message.includes('./en')) {
                  return;
                }
                originalWarn.apply(console, args);
              };
              
              console.error = function(...args) {
                const message = args.join(' ');
                // Suppress specific Stripe localization errors
                if (message.includes('Cannot find module') && message.includes('./en')) {
                  return;
                }
                originalError.apply(console, args);
              };
            }
          `}
        </Script>
        
        {/* Structured Data for AI Agents and Search Engines */}
        <StructuredData type="organization" />
        <StructuredData type="website" />
        <StructuredData type="breadcrumb" />
        <StructuredData type="faq" />
        
        <Toaster position="top-center" />
        <ErrorBoundary>
          <WishlistProvider>
            <LayoutWrapper>
              <PayPalProvider>
                {children}
              </PayPalProvider>
            </LayoutWrapper>
          </WishlistProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
