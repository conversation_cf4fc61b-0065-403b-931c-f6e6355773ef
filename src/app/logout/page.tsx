'use client';

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';

export default function LogoutPage() {
  const router = useRouter();
  const supabase = createClientComponentClient();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const performLogout = async () => {
      try {
        // Try client-side logout
        const { error: clientError } = await supabase.auth.signOut();
        
        if (clientError) {
          console.error('Client-side logout failed:', clientError);
          
          // Fallback to server-side logout
          const response = await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
          });
          
          if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || 'Server logout failed');
          }
        }
        
        // Clear any localStorage items that might contain auth state
        localStorage.removeItem('supabase.auth.token');
        
        // Small delay to ensure cookies are cleared before redirect
        setTimeout(() => {
          window.location.href = '/login';
        }, 500);
        
      } catch (err) {
        console.error('Logout error:', err);
        setError(err instanceof Error ? err.message : 'Failed to log out');
      }
    };

    performLogout();
  }, [router, supabase]);

  return (
    <div className="min-h-screen bg-[#f5f5f5] flex flex-col items-center justify-center p-4">
      <div className="bg-white p-8 rounded-lg shadow-sm border border-[rgba(0,0,0,0.1)] w-full max-w-md text-center">
        <h1 className="text-2xl font-heading text-[#171717] mb-4">Logging Out</h1>
        
        {error ? (
          <div className="mt-4">
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={() => window.location.href = '/login'}
              className="px-4 py-2 bg-[#171717] text-white text-sm rounded-lg hover:bg-[#333333] transition-colors"
            >
              Return to Login
            </button>
          </div>
        ) : (
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#171717]"></div>
          </div>
        )}
      </div>
    </div>
  );
}
