'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import type { Database } from '@/lib/database.types';
import { getCloudinaryUrl, getProductImageUrl } from '@/lib/image-utils';

// Define types using the database schema
type ProductRow = Database['public']['Tables']['products']['Row'];
type CategoryRow = Database['public']['Tables']['categories']['Row'];

// Define a MediaItem type that matches the database schema
interface MediaItem {
  id: string;
  product_id: string | null;
  url: string;
  type?: string;
  position?: number | null;
  alt?: string | null;
  created_at?: string | null;
  is_main?: boolean | null;
}

// Create a type that extends ProductRow but with proper specifications handling
interface Product extends Omit<ProductRow, 'specifications'> {
  image_url?: string;
  mediaItems?: MediaItem[];
  specifications?: {
    condition?: string;
    [key: string]: any;
  } | null;
  category?: { name: string } | null;
}

// Add this helper function near the top of the file, after the type definitions
const isProductSoldOut = (product: Product) => {
  return product.status === 'sold_out' || !product.quantity || product.quantity <= 0;
};

export default function NewArrivalsPage() {
  const supabase = createClientComponentClient<Database>();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchNewArrivals();
  }, []);

  const fetchNewArrivals = async () => {
    try {
      setLoading(true);
      setError('');

      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          product_media:product_media(id, product_id, url, type, position, alt, created_at, is_main),
          category:categories(name)
        `)
        .order('created_at', { ascending: false })
        .limit(12);

      if (error) {
        console.error('Error loading new arrivals:', error);
        setProducts([]);
        setError('Failed to load new arrivals. Please try again later.');
      } else {
        // Process the products data
        if (data) {
          try {
            // Map the data to include specifications and media items
            const productsWithDetails = data.map(product => {
              const specifications = product.specifications || {};
              const mediaItems = product.product_media || [];
              
              // Convert mediaItems to the expected Database type format
              const typedMediaItems: Database['public']['Tables']['product_media']['Row'][] = 
                mediaItems.map(item => ({
                  id: item.id,
                  product_id: item.product_id,
                  url: item.url,
                  type: item.type || 'image',
                  position: item.position || null,
                  alt: item.alt || null,
                  created_at: item.created_at || null,
                  is_main: item.is_main || null
                }));
              
              return {
                ...product,
                specifications,
                mediaItems: typedMediaItems,
                image_url: getProductImageUrl(typedMediaItems),
                category: product.category,
              } as Product;
            });
            
            setProducts(productsWithDetails);
          } catch (err) {
            console.error('Error processing products:', err);
            setProducts([]);
          }
        }
      }
    } catch (err) {
      console.error('Error loading new arrivals:', err);
      setError('Failed to load new arrivals. Please try again later.');
    }
    setLoading(false);
  };

  const getCategoryName = (product: Product): string => {
    return product.category?.name || 'Uncategorized';
  };
  
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const getConditionText = (product: Product): string => {
    if (!product.specifications || !product.specifications.condition) return '';
    
    const condition = product.specifications.condition;
    const conditionMap: Record<string, string> = {
      'new': 'New with tags',
      'like_new': 'Like new',
      'good': 'Good condition',
      'fair': 'Fair condition',
      'poor': 'Poor condition'
    };
    
    return conditionMap[condition.toLowerCase()] || condition;
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="max-w-md w-full p-8 bg-white rounded-xl shadow-sm text-center">
          <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 className="text-xl font-heading mb-2">Error Loading New Arrivals</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link 
            href="/products" 
            className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333]"
          >
            Shop All Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-12 bg-[#f8f8f8]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="relative h-80 md:h-96 w-full mb-12 rounded-xl overflow-hidden">
          <Image
            src="/images/explore-categories.JPG"
            alt="New Arrivals"
            fill
            className="object-cover object-center"
            priority
          />
          <div className="absolute inset-0 bg-black/30 flex flex-col items-center justify-center text-center p-6">
            <h1 className="text-4xl md:text-5xl font-heading text-white tracking-wider mb-4">
              New Arrivals
            </h1>
            <p className="text-lg text-white max-w-2xl">
              Discover our latest curated selection of luxury vintage pieces, handpicked for their exceptional quality and timeless style.
            </p>
          </div>
        </div>
        
        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {products.length > 0 ? (
            products.map((product) => (
              <motion.div 
                key={product.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
                className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow"
              >
                <Link href={`/products/${product.slug}`} className="block">
                  <div className="relative h-64 w-full bg-gray-100">
                    {product.image_url ? (
                      <Image
                        src={product.image_url}
                        alt={product.name}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <span className="text-gray-400">No image</span>
                      </div>
                    )}
                    
                    {/* Condition Badge */}
                    {product.specifications?.condition && (
                      <div className="absolute top-3 left-3 bg-black/70 text-white text-xs px-2 py-1 rounded">
                        {getConditionText(product)}
                      </div>
                    )}
                    
                    {/* Sold Out Overlay */}
                    {isProductSoldOut(product) && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <span className="text-white font-medium px-4 py-2 bg-black bg-opacity-75 rounded-md">
                          Sold Out
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <div className="text-xs text-gray-500 mb-1">{getCategoryName(product)}</div>
                    <h3 className="font-medium text-[#171717] line-clamp-1 mb-2">{product.name}</h3>
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-lg">{formatPrice(product.price)}</span>
                      {isProductSoldOut(product) && (
                        <span className="text-xs font-medium text-red-600">
                          Sold Out
                        </span>
                      )}
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))
          ) : (
            <div className="col-span-full py-12 text-center">
              <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 12H4M8 16l-4-4m0 0l4-4m-4 4h16" />
              </svg>
              <h3 className="text-xl font-medium text-gray-900 mb-1">No new arrivals yet</h3>
              <p className="text-gray-500">Check back soon for our latest luxury pieces.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}