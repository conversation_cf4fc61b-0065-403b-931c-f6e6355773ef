'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface OrderData {
  id: string;
  created_at: string;
  user_id: string;
  shipping_address_id: string;
  status: string;
  total_amount: number;
  shipping_cost: number;
  tax_amount: number;
  payment_method: string;
  payment_status: string;
  tracking_number: string | null;
  payment_intent: string; // Added field
  payment_provider: string; // Added field
  shipping_address: {
    name: string;
    street: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
  };
  items: Array<{
    id: string;
    order_id: string;
    product_id: string;
    quantity: number;
    price: number;
    product: {
      name: string;
      slug: string;
      main_image_url: string;
      condition: string;
    };
  }>;
}

export default function OrderConfirmation({ params }: { params: { id: string } }) {
  const { id } = params;
  const supabase = createClientComponentClient();
  const router = useRouter();
  const [order, setOrder] = useState<OrderData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        setLoading(true);
        
        // Check if user is logged in
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          router.push('/login');
          return;
        }
        
        // Fetch order with related data
        const { data, error: orderError } = await supabase
          .from('orders')
          .select(`
            id,
            created_at,
            user_id,
            payment_intent,
            payment_provider,
            shipping_address_id,
            status,
            total_amount,
            shipping_cost,
            tax_amount,
            payment_method,
            payment_status,
            tracking_number,
            shipping_address:shipping_addresses(
              name,
              street,
              city,
              state,
              country,
              postal_code
            ),
            items:order_items(
              id,
              order_id,
              product_id,
              quantity,
              price,
              product:products(
                name,
                slug,
                main_image_url,
                condition
              )
            )
          `)
          .eq('id', id)
          .eq('user_id', session.user.id)
          .single();
          
        if (orderError) throw orderError;
        
        if (!data) {
          setError('Order not found');
          return;
        }
        
        setOrder({
          ...data,
          shipping_address: Array.isArray(data.shipping_address) ? data.shipping_address[0] : data.shipping_address,
          items: data.items.map(item => ({
            ...item,
            product: Array.isArray(item.product) ? item.product[0] : item.product
          }))
        });
      } catch (error) {
        console.error('Error fetching order details:', error);
        setError('Failed to load order details');
      } finally {
        setLoading(false);
      }
    };
    
    fetchOrderDetails();
  }, [id, supabase, router]);
  
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };
  
  if (loading) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
      </div>
    );
  }
  
  if (error || !order) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="max-w-md w-full p-8 bg-white rounded-xl shadow-sm text-center">
          <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 className="text-xl font-heading mb-2">Order Not Found</h2>
          <p className="text-gray-600 mb-6">{error || 'The order you are looking for does not exist or you do not have access to view it.'}</p>
          <Link 
            href="/account" 
            className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333]"
          >
            Go to Your Account
          </Link>
        </div>
      </div>
    );
  }
  
  // Calculate subtotal
  const subtotal = order.total_amount - order.shipping_cost - order.tax_amount;
  
  return (
    <div className="min-h-screen pt-20 pb-12 bg-[#f8f8f8]">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="py-8 text-center"
        >
          <div className="w-20 h-20 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-3xl font-heading tracking-wider text-[#171717]">Order Confirmed!</h1>
          <p className="mt-2 text-gray-600">Thank you for your purchase</p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Order Summary */}
          <div className="md:col-span-2 space-y-8">
            {/* Order Info */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex flex-wrap justify-between items-center mb-6">
                <div>
                  <h2 className="text-xl font-heading text-[#171717]">Order #{order.id.slice(0, 8)}</h2>
                  <p className="text-sm text-gray-500 mt-1">Placed on {formatDate(order.created_at)}</p>
                </div>
                <div className="mt-2 sm:mt-0">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </span>
                </div>
              </div>
              
              {/* Order Items */}
              <div className="border-t border-gray-100 pt-6">
                <h3 className="font-medium text-gray-900 mb-4">Items in Your Order</h3>
                
                <div className="divide-y divide-gray-100">
                  {order.items.map((item) => (
                    <div key={item.id} className="py-4 flex">
                      <div className="flex-shrink-0 w-20 h-20 bg-gray-100 rounded-md overflow-hidden relative">
                        {item.product.main_image_url ? (
                          <Image
                            src={item.product.main_image_url}
                            alt={item.product.name}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full bg-gray-200">
                            <span className="text-gray-400 text-xs">No image</span>
                          </div>
                        )}
                      </div>
                      <div className="ml-4 flex-1">
                        <Link 
                          href={`/products/${item.product.slug}`}
                          className="font-medium text-[#171717] hover:underline line-clamp-1"
                        >
                          {item.product.name}
                        </Link>
                        <div className="mt-1 flex items-center text-sm text-gray-500">
                          <span>Condition: {item.product.condition}</span>
                          <span className="mx-2">•</span>
                          <span>Qty: {item.quantity}</span>
                        </div>
                        <div className="mt-1 font-medium">${item.price.toLocaleString()}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Tracking Info (if available) */}
              {order.tracking_number && (
                <div className="mt-6 border-t border-gray-100 pt-6">
                  <h3 className="font-medium text-gray-900 mb-2">Tracking Information</h3>
                  <p className="text-sm">
                    Tracking Number: <span className="font-medium">{order.tracking_number}</span>
                  </p>
                  <a href="#" className="text-sm text-[#171717] hover:underline mt-2 inline-block">
                    Track Your Package
                  </a>
                </div>
              )}
            </div>
            
            {/* Shipping and Delivery */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-heading text-[#171717] mb-4">Shipping Details</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Shipping Address</h3>
                  <div className="text-sm text-gray-600">
                    <p className="font-medium">{order.shipping_address.name}</p>
                    <p>{order.shipping_address.street}</p>
                    <p>
                      {order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.postal_code}
                    </p>
                    <p>{order.shipping_address.country}</p>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Shipping Method</h3>
                  <p className="text-sm text-gray-600">Standard Shipping (3-5 business days)</p>
                  {order.status === 'processing' && (
                    <p className="text-sm text-gray-600 mt-4">
                      Your order is being processed and will be shipped soon.
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* Order Summary and Actions */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-6 sticky top-24">
              <h2 className="text-xl font-heading text-[#171717] mb-4">Order Summary</h2>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">${subtotal.toLocaleString()}</span>
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">${order.shipping_cost.toLocaleString()}</span>
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Tax</span>
                  <span className="font-medium">${order.tax_amount.toFixed(2)}</span>
                </div>
                
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="flex justify-between">
                    <span className="text-base font-medium text-[#171717]">Total</span>
                    <span className="text-base font-medium text-[#171717]">${order.total_amount.toFixed(2)}</span>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 space-y-3">
                <h3 className="font-medium text-gray-900 mb-2">Payment Information</h3>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Method:</span> {order.payment_method === 'card' ? 'Credit Card' : 'PayPal'}
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Status:</span> {order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1)}
                </p>
              </div>
              
              <div className="mt-6 space-y-2">
                <Link 
                  href="/account" 
                  className="w-full block text-center px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333]"
                >
                  View Order History
                </Link>
                <Link 
                  href="/products" 
                  className="w-full block text-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Continue Shopping
                </Link>
              </div>
              
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="font-medium text-gray-900 mb-2">Need Help?</h3>
                <p className="text-sm text-gray-600 mb-3">
                  If you have any questions about your order, please contact our customer support.
                </p>
                <Link 
                  href="/contact" 
                  className="text-sm text-[#171717] hover:underline"
                >
                  Contact Support
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}