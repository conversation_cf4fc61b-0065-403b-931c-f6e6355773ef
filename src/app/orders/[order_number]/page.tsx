'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export default function OrderReceiptPage() {
  const { order_number } = useParams();
  const supabase = createClientComponentClient();
  const [order, setOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchOrder() {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          status,
          total_amount,
          created_at,
          tracking_number,
          carrier,
          tracking_url,
          user_id,
          payment_intent,
          order_items(*, product:products(name, price))
        `)
        .eq('order_number', order_number)
        .single();

      if (error) {
        console.error('Error fetching order:', error);
        setError('Order not found.');
      } else {
        setOrder(data);
      }

      setLoading(false);
    }

    if (order_number) {
      fetchOrder();
    }
  }, [order_number]);

  if (loading) return <div className="p-6">Loading order details...</div>;
  if (error) return <div className="p-6 text-red-600">{error}</div>;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Order Receipt</h1>
      <div className="bg-white shadow-sm rounded-lg p-4 space-y-2">
        <p><strong>Order Number:</strong> #{order.order_number}</p>
        <p><strong>Status:</strong> {order.status}</p>
        <p><strong>Total:</strong> €{order.total_amount?.toFixed(2)}</p>
        <p><strong>Date:</strong> {new Date(order.created_at).toLocaleDateString()}</p>
        
        {order.tracking_number && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h2 className="text-lg font-semibold mb-2">Shipping Information</h2>
            <p><strong>Tracking Number:</strong> {order.tracking_number}</p>
            {order.carrier && <p><strong>Carrier:</strong> {order.carrier}</p>}
            {order.tracking_url && (
              <p>
                <strong>Track Your Package:</strong>{' '}
                <a 
                  href={order.tracking_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  Click here to track
                </a>
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
