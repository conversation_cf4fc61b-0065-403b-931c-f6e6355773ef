'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'

export default function Home() {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const header = document.querySelector('header');
    const footer = document.querySelector('footer');
    
    if (isVisible) {
      if (header) header.style.display = 'none';
      if (footer) footer.style.display = 'none';
    }
    
    const timer = setTimeout(() => {
      setIsVisible(false)
      
      if (header) header.style.display = '';
      if (footer) footer.style.display = '';
    }, 5000)
    
    return () => {
      clearTimeout(timer);
      if (header) header.style.display = '';
      if (footer) footer.style.display = '';
    }
  }, [isVisible])

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#f5f5f5' }}>
      <AnimatePresence mode="wait">
        {isVisible ? (
          <motion.div
            key="welcome"
            className="fixed inset-0 flex items-center justify-center z-50"
            style={{ backgroundColor: '#f5f5f5' }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="relative w-full max-w-4xl mx-auto px-6">
              <div className="relative">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  className="absolute -top-24 left-1/2 transform -translate-x-1/2 w-full text-center"
                >
                </motion.div>

                <motion.div
                  className="relative rounded-lg overflow-hidden"
                  initial={{ opacity: 0, scale: 0.98 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8 }}
                >
                  <Image
                    src="/images/welcome-card.png"
                    alt="Welcome to Maimi-Luxury"
                    width={1200}
                    height={800}
                    priority
                    className="w-full h-auto"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-[rgba(0,0,0,0.1)] to-transparent" />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                  className="absolute bottom-[-60px] w-full text-center"
                >
                  <p className="text-lg font-body italic mx-auto max-w-[280px] sm:max-w-full px-4" style={{ color: '#666666' }}>
                    "Every piece tells a story..."
                  </p>
                </motion.div>
              </div>
            </div>
          </motion.div>
        ) : (
          <motion.div
            key="main"
            className="relative min-h-[calc(100vh-5rem)] flex flex-col items-center justify-center px-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
          >
            <div className="max-w-4xl mx-auto w-full space-y-12 text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <h1 className="text-5xl md:text-6xl font-heading tracking-[0.2em]" style={{ color: '#171717' }}>
                  CURATED FROM CLOSETS
                </h1>
              </motion.div>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <p className="text-xl md:text-2xl font-body italic max-w-2xl mx-auto leading-relaxed" style={{ color: '#666666' }}>
                  Handpicked luxury, just for you.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="pt-8"
              >
                <Link
                  href="/new-arrivals"
                  className="inline-block px-12 py-4 text-white font-body tracking-[0.15em] text-sm transition-all duration-300 bg-[#171717] hover:bg-[#333333]"
                >
                  DISCOVER NEW ARRIVALS
                </Link>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
