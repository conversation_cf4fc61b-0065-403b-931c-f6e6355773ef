'use client';

import { motion } from 'framer-motion';

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-[#f8f8f8] pt-32 pb-24">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-4xl md:text-5xl font-heading tracking-wider text-[#171717] mb-8 text-center">
            Privacy Policy
          </h1>

          <div className="bg-white rounded-xl shadow-sm p-8 md:p-12 space-y-8">
            <section>
              <h2 className="text-2xl font-heading mb-4">Introduction</h2>
              <p className="text-[#666666] leading-relaxed">
                At The Treasure of Maimi, we take your privacy seriously. This Privacy Policy explains how 
                we collect, use, and protect your personal information when you use our website and services.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Information We Collect</h2>
              <ul className="list-disc list-inside space-y-2 text-[#666666]">
                <li>Personal information (name, email address, shipping address)</li>
                <li>Payment information (processed securely through Stripe)</li>
                <li>Shopping preferences and history</li>
                <li>Device and browser information</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">How We Use Your Information</h2>
              <ul className="list-disc list-inside space-y-2 text-[#666666]">
                <li>Process your orders and provide customer service</li>
                <li>Send order updates and shipping notifications</li>
                <li>Improve our products and services</li>
                <li>Personalize your shopping experience</li>
                <li>Comply with legal obligations</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Data Security</h2>
              <p className="text-[#666666] leading-relaxed">
                We implement appropriate security measures to protect your personal information. Your payment 
                information is processed through Stripe, a secure payment gateway that complies with PCI DSS 
                standards.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Your Rights</h2>
              <p className="text-[#666666] leading-relaxed mb-4">
                Under GDPR and other applicable privacy laws, you have the right to:
              </p>
              <ul className="list-disc list-inside space-y-2 text-[#666666]">
                <li>Access your personal data</li>
                <li>Correct inaccurate data</li>
                <li>Request deletion of your data</li>
                <li>Object to data processing</li>
                <li>Data portability</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Contact Us</h2>
              <p className="text-[#666666] leading-relaxed">
                If you have any questions about our Privacy Policy or how we handle your data, please contact us at{' '}
                <a 
                  href="mailto:<EMAIL>"
                  className="text-[#171717] hover:underline"
                >
                  <EMAIL>
                </a>
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Updates to This Policy</h2>
              <p className="text-[#666666] leading-relaxed">
                We may update this Privacy Policy from time to time. We will notify you of any changes by 
                posting the new Privacy Policy on this page and updating the "Last Updated" date.
              </p>
            </section>

            <div className="text-sm text-[#666666] pt-8 border-t border-gray-200">
              Last Updated: March 4, 2025
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
