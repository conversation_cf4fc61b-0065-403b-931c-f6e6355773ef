'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import CertificateOfAuthenticity from '@/components/CertificateOfAuthenticity';
import { Database } from '@/lib/database.types';
import { customToast } from '@/components/ui/CustomToast';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { debouncedTrackProductView } from '@/lib/product-analytics';
import { useWishlist } from '@/context/WishlistContext'; 
import { getCloudinaryUrl, getProductImageUrl } from '@/lib/image-utils';
import useSWR from 'swr';

// Define types based on Supabase schema
type DbProduct = Database['public']['Tables']['products']['Row'];
type DbProductMedia = Database['public']['Tables']['product_media']['Row'];
type DbCategory = Database['public']['Tables']['categories']['Row'];

// Define a type for product specifications
interface ProductSpecifications {
  color?: string;
  material?: string;
  size?: string;
  design?: string;
  strap?: string;
  interior?: string;
  condition?: string;
  rarity?: string;
  model?: string;
  versatility?: string;
  serial_number?: string;
  certificate_issued_at?: string | null;
  [key: string]: any;
}

// Extended product type with additional fields needed for the UI
interface Product extends Omit<Database['public']['Tables']['products']['Row'], 'specifications'> {
  image_url?: string | null;
  category_name?: string;
  mediaItems: MediaItem[];
  specifications: ProductSpecifications;
}

type MediaItem = {
  id: string;
  product_id: string | null; // Allow null for compatibility with existing data
  url: string;
  type?: string;
  media_type?: string;
  position?: number | null; // Allow null for compatibility with existing data
  alt?: string | null;
  created_at?: string | null;
};

// Helper function to get high-resolution Cloudinary URLs for product display
const getFullCloudinaryUrl = (url: string | null | undefined): string => {
  const baseUrl = getCloudinaryUrl(url);
  if (!baseUrl) return '';
  
  // If already a Cloudinary URL, add transformation parameters
  if (baseUrl.includes('res.cloudinary.com')) {
    // Insert transformation parameters before the upload part
    return baseUrl.replace('/upload/', '/upload/q_auto,f_auto,w_1200/');
  }
  
  return baseUrl;
};

// Helper function to safely handle localStorage
const safeLocalStorage = {
  getItem: (key: string): string | null => {
    try {
      return localStorage.getItem(key);
    } catch (e) {
      console.error(`[DEBUG] Error reading from localStorage:`, e);
      return null;
    }
  },
  setItem: (key: string, value: string): boolean => {
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (e) {
      console.error(`[DEBUG] Error writing to localStorage:`, e);
      return false;
    }
  }
};

// Helper function to dispatch storage change event
const dispatchStorageEvent = () => {
  // This will trigger our custom event listener in the Header component
  window.dispatchEvent(new Event('localStorageChange'));
};

// Helper function to format currency
const formatCurrency = (amount: number, currency: string = 'EUR') => {
  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Helper function to format condition
const formatCondition = (condition: string | null | undefined): string => {
  if (!condition) return 'Not specified';
  
  const conditionMap: Record<string, string> = {
    'new': 'New with tags',
    'like_new': 'Like new',
    'good': 'Good condition',
    'fair': 'Fair condition',
    'poor': 'Poor condition'
  };
  
  return conditionMap[condition.toLowerCase()] || condition;
};

// Add this helper function near the top of the file, after the type definitions
const isProductSoldOut = (product: any) => {
  console.log('Checking if product is sold out:', {
    status: product?.status,
    quantity: product?.quantity,
    isSoldOut: product?.status === 'sold_out' || !product?.quantity || product.quantity <= 0
  });
  return product?.status === 'sold_out' || !product?.quantity || product.quantity <= 0;
};

// Add revalidation interval
const REVALIDATION_INTERVAL = 5000; // 5 seconds

const SoldOutBadge = () => (
  <motion.div
    initial={{ opacity: 0, scale: 0.8, rotate: -15 }}
    animate={{ opacity: 1, scale: 1, rotate: 0 }}
    transition={{ duration: 0.6, ease: "easeOut" }}
    className="absolute inset-0 z-20 flex items-center justify-center"
  >
    {/* Dark overlay with blur */}
    <div className="absolute inset-0 bg-black/60 backdrop-blur-md" />
    
    {/* Badge container */}
    <div className="relative w-72 h-72 flex items-center justify-center">
      <Image
        src="/images/sold-out.png"
        alt="Sold Out"
        width={288}
        height={288}
        priority
        className="w-full h-full object-contain"
      />
    </div>
  </motion.div>
);

export default function ProductPage() {
  const params = useParams();
  const router = useRouter();
  
  // Get the product slug from the URL parameters
  const slug = params?.slug as string;
  
  // State for product data
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [category, setCategory] = useState<DbCategory | null>(null);
  
  // State for selected image
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  
  // State for wishlist message
  const [wishlistMessage, setWishlistMessage] = useState<{
    text: string;
    type: 'success' | 'error' | 'info';
  } | null>(null);
  
  // State for tab selection
  const [selectedTab, setSelectedTab] = useState<'details' | 'specifications'>('details');
  
  // State for related products
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  
  // State for 3D/AR viewer
  const [showModelViewer, setShowModelViewer] = useState(false);
  
  // State for model load error
  const [modelLoadError, setModelLoadError] = useState(false);
  
  // Fixed quantity to 1 since we only have one of each product
  const quantity = 1;

  // Animation states
  const [cartAnimation, setCartAnimation] = useState(false);
  const [cartButtonScale, setCartButtonScale] = useState(1);
  const [wishlistAnimation, setWishlistAnimation] = useState(false);
  const [wishlistButtonScale, setWishlistButtonScale] = useState(1);
  const [floatingImage, setFloatingImage] = useState({ 
    active: false, 
    type: '', 
    startX: 0, 
    startY: 0, 
    imageUrl: '' 
  });

  // Initialize Supabase client
  const supabase = createClientComponentClient<Database>();
  const wishlistContext = useWishlist();
  
  if (!wishlistContext) {
    console.error("[DEBUG] Wishlist context is not available");
    return null;
  }
  
  const { addToWishlist, removeFromWishlist, isInWishlist } = wishlistContext;

  // Use useEffect with cleanup to prevent memory leaks
  useEffect(() => {
    let timeouts: number[] = [];
    
    const registerTimeout = (callback: () => void, delay: number) => {
      const id = window.setTimeout(callback, delay);
      timeouts.push(id);
      return id;
    };
    
    // Clean up all timeouts when component unmounts
    return () => {
      timeouts.forEach(id => window.clearTimeout(id));
    };
  }, []);
  
  // Register a safe timeout that cleans up automatically
  const createSafeTimeout = (callback: () => void, delay: number): number => {
    const timeoutId = window.setTimeout(callback, delay);
    
    // Store the ID for potential manual cleanup
    const timeouts = (window as any).__safeTimeouts = (window as any).__safeTimeouts || [];
    timeouts.push(timeoutId);
    
    return timeoutId;
  };
  
  // Clear a safe timeout
  const clearSafeTimeout = (id: number): void => {
    window.clearTimeout(id);
    
    // Remove from tracking array if it exists
    const timeouts = (window as any).__safeTimeouts;
    if (timeouts) {
      const index = timeouts.indexOf(id);
      if (index !== -1) {
        timeouts.splice(index, 1);
      }
    }
  };

  // Replace the useEffect for fetching with SWR
  const { data: productData, mutate } = useSWR(
    slug ? `/api/products/${slug}` : null,
    async () => {
      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:categories(id, name, slug),
          mediaItems:product_media(*)
        `)
        .eq('slug', slug)
        .single();

      if (error) throw error;
      return data;
    },
    {
      refreshInterval: 0, // Disable automatic refresh to improve performance
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 60000, // Increase deduping interval to 1 minute
    }
  );

  useEffect(() => {
    if (productData) {
      setProduct(productData);
      setMediaItems(productData.mediaItems || []);
      setCategory(productData.category);
      setLoading(false);
    }
  }, [productData]);

  useEffect(() => {
    // Load model-viewer web component
    const script = document.createElement('script');
    script.type = 'module';
    script.src = 'https://unpkg.com/@google/model-viewer/dist/model-viewer.min.js';
    document.head.appendChild(script);
    
    return () => {
      // Clean up if needed
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  useEffect(() => {
    if (product?.id) {
      // Track product view when product is loaded
      console.log('[ProductPage] Tracking view for product:', product.id);
      debouncedTrackProductView(product.id);
    }
  }, [product?.id]);

  // Function to get certificate data from product
  const getCertificateData = () => {
    if (!product) return null;
    
    return {
      id: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      condition: product.specifications?.condition || '',
      main_image_url: mediaItems.length > 0 ? getCloudinaryUrl(mediaItems[0].url) : (product.image_url ? getCloudinaryUrl(product.image_url) : ''),
      category: {
        name: product.category_name || 'Unknown Category'
      },
      specifications: product.specifications || {},
      serial_number: product.specifications?.serial_number || '',
      certificate_issued_at: product.specifications?.certificate_issued_at 
        ? new Date(product.specifications.certificate_issued_at as string).toISOString()
        : new Date().toISOString()
    };
  };

  // Function to add product to cart with enhanced animation
  const handleAddToCart = (e?: React.MouseEvent) => {
    // Prevent default if event is passed
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    // Prevent multiple clicks
    if (cartAnimation) return;
    
    if (!product) return;
    
    // Check if product is in stock
    if (!product.quantity || product.quantity < 1) {
      customToast.error(
        <div>
          <p className="font-medium">This item is currently out of stock.</p>
        </div>
      );
      return;
    }
    
    try {
      console.log("[DEBUG] Product: Adding to cart -", product.id);
      
      // Get coordinates for animation starting point
      const rect = e?.currentTarget?.getBoundingClientRect();
      const startX = rect ? rect.left + rect.width / 2 : window.innerWidth / 2;
      const startY = rect ? rect.top + rect.height / 2 : window.innerHeight / 2;
      
      // Trigger animation
      setCartAnimation(true);
      setCartButtonScale(0.95); // More pronounced scale down
      
      // Use safe timeout that won't cause memory leaks
      createSafeTimeout(() => {
        setCartButtonScale(1.06); // More pronounced scale up
        createSafeTimeout(() => setCartButtonScale(1), 250);
      }, 200);
      
      createSafeTimeout(() => setCartAnimation(false), 1200);
      
      // Show floating image animation with improved parameters
      setFloatingImage({ 
        active: true, 
        type: 'cart', 
        startX, 
        startY,
        imageUrl: mediaItems.length > 0 ? mediaItems[0].url : (product.image_url || '')
      });
      
      createSafeTimeout(() => setFloatingImage({ 
        active: false, 
        type: '', 
        startX: 0, 
        startY: 0,
        imageUrl: '' 
      }), 1800);
      
      // Generate a unique ID for the cart item
      const cartItemId = `cart-${Date.now()}-${product.id}`;
      console.log("[DEBUG] Product: Generated cart item ID -", cartItemId);
      
      // Create the cart item object
      const cartItem = {
        id: cartItemId,
        product_id: product.id,
        quantity: 1,
        added_at: new Date().toISOString(),
        product: {
          id: product.id,
          name: product.name,
          slug: product.slug,
          price: product.price,
          condition: product.specifications?.condition || 'Good',
          main_image_url: mediaItems.length > 0 ? getCloudinaryUrl(mediaItems[0].url) : (product.image_url ? getCloudinaryUrl(product.image_url) : ''),
          category: {
            name: product.category_name || 'Unknown Category'
          }
        }
      };
      
      // Always use localStorage for cart - this eliminates glitchy auth behavior
      const GUEST_CART_KEY = 'shop-maimi-guest-cart';
      
      // Get existing cart items
      let cartItems = [];
      try {
        const storedCart = safeLocalStorage.getItem(GUEST_CART_KEY);
        if (storedCart) {
          cartItems = JSON.parse(storedCart);
          console.log("[DEBUG] Product: Retrieved cart items -", cartItems.length);
        } else {
          console.log("[DEBUG] Product: No existing cart items found");
        }
      } catch (error) {
        console.error('[DEBUG] Product: Error parsing cart from localStorage:', error);
        cartItems = [];
      }
      
      // Check if product already in cart
      const existingItemIndex = cartItems.findIndex((item: any) => item.product_id === product.id);
      
      // Get current available quantity for this product
      const availableQuantity = product.quantity || 0;

      if (existingItemIndex >= 0) {
        // Item already exists in cart - check if we would exceed the available quantity
        const currentQuantityInCart = cartItems[existingItemIndex].quantity;
        
        if (currentQuantityInCart >= availableQuantity) {
          // Already at max available quantity
          customToast.error(
            <div>
              <p className="font-medium">Maximum quantity reached</p>
              <p className="text-xs mt-1">We only have {availableQuantity} unit{availableQuantity !== 1 ? 's' : ''} in stock</p>
            </div>
          );
          return;
        }
        
        // Create toast notification that it's already in cart
        customToast.cart(
          <div>
            <p className="font-medium">Item already in cart</p>
            <p className="text-xs mt-1">You can adjust quantity in the cart</p>
          </div>
        );
        
        // Still trigger animation for user feedback
        const saveSuccess = true;
      } else {
        // New item - add to cart with quantity 1 (which is already set in cartItem)
        cartItems.push(cartItem);
        console.log("[DEBUG] Product: Added new item to cart");
        
        // Save updated cart to localStorage
        const saveSuccess = safeLocalStorage.setItem(GUEST_CART_KEY, JSON.stringify(cartItems));
        
        if (saveSuccess) {
          // Show success message using custom toast
          console.log("[DEBUG] Product: Successfully saved cart to localStorage");
          customToast.cart(`${product.name} has been added to your cart.`);
          
          // Dispatch event to update header counter
          dispatchStorageEvent();
        } else {
          console.error('[DEBUG] Product: Failed to save cart to localStorage');
          customToast.error('Could not save to cart. Please try again.');
        }
      }
    } catch (error) {
      console.error('[DEBUG] Product: Error adding to cart:', error);
      customToast.error('Failed to add item to cart. Please try again.');
    }
  };

  // Function to add product to wishlist with enhanced animation
  const handleAddToWishlist = async (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!product) {
      console.error("[DEBUG] Product: Cannot add to wishlist - product is null");
      return;
    }

    try {
      const alreadyInWishlist = isInWishlist(product.id);

      if (alreadyInWishlist) {
        await removeFromWishlist(product.id);
        customToast.info(`${product.name} removed from wishlist`);
      } else {
        const success = await addToWishlist(product.id);
        if (success) {
          customToast.success(`${product.name} added to wishlist`);
        }
      }
    } catch (error) {
      console.error('[DEBUG] Wishlist error:', error);
      customToast.error('Failed to update wishlist');
    }
  };

  // Handle certificate download
  const handleCertificateDownload = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    const certificateData = getCertificateData();
    if (!certificateData) return;
    
    // Open certificate in new tab
    window.open(`/api/certificate?data=${encodeURIComponent(JSON.stringify(certificateData))}`, '_blank');
  };

  // Handle share on social media
  const handleShare = (platform: string) => {
    const url = window.location.href;
    const text = `Check out this amazing ${product?.name} on Shop Maimi!`;
    
    let shareUrl = '';
    
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'pinterest':
        const imageUrl = mediaItems.length > 0 
          ? getCloudinaryUrl(mediaItems[0]?.url) 
          : '';
        shareUrl = `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}&media=${encodeURIComponent(imageUrl)}&description=${encodeURIComponent(text)}`;
        break;
      default:
        break;
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank');
    }
  };

  // Add a function to check if a 3D model exists
  const check3DModelAvailability = async (url: string) => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      console.error('Error checking 3D model availability:', error);
      return false;
    }
  };

  // Check model availability when selected image changes
  useEffect(() => {
    if (showModelViewer && product && mediaItems.length > 0) {
      const modelUrl = product.model_url || 
        `${getCloudinaryUrl(mediaItems[selectedImageIndex]?.url)
          .replace('/image/upload/', '/image/upload/auto_3d/v1/')
          .replace(/\.\w+$/, '.glb')}`;
          
      check3DModelAvailability(modelUrl).then(isAvailable => {
        setModelLoadError(!isAvailable);
      });
    }
  }, [showModelViewer, selectedImageIndex, product, mediaItems]);

  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center pt-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center pt-20">
        <div className="max-w-md p-6 bg-white rounded-lg shadow-md text-center">
          <h2 className="text-2xl font-heading text-red-600 mb-4">Error</h2>
          <p className="text-gray-700 mb-6">{error}</p>
          <Link
            href="/products"
            className="inline-block px-6 py-2 text-white font-body tracking-wide text-sm transition-all duration-300 bg-[#171717] hover:bg-[#333333] rounded-md"
          >
            Return to Products
          </Link>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex justify-center items-center pt-20">
        <div className="max-w-md p-6 bg-white rounded-lg shadow-md text-center">
          <h2 className="text-3xl font-heading text-[#171717] mb-4">Product Not Found</h2>
          <p className="text-gray-700 mb-6">The requested product could not be found or may have been removed.</p>
          <Link
            href="/products"
            className="inline-block px-6 py-2 text-white font-body tracking-wide text-sm transition-all duration-300 bg-[#171717] hover:bg-[#333333] rounded-md"
          >
            Return to Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-24 bg-[#f8f8f8]">
      {/* Breadcrumbs */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="text-sm font-body text-gray-500">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="hover:text-[#171717]">Home</Link>
              </li>
              <li>
                <span className="mx-2">/</span>
              </li>
              <li>
                <Link href="/products" className="hover:text-[#171717]">Products</Link>
              </li>
              {category && (
                <>
                  <li>
                    <span className="mx-2">/</span>
                  </li>
                  <li>
                    <Link href={`/category/${category.slug}`} className="hover:text-[#171717]">
                      {category.name}
                    </Link>
                  </li>
                </>
              )}
              <li>
                <span className="mx-2">/</span>
              </li>
              <li className="text-[#171717] font-medium truncate max-w-[200px]">
                {product.name}
              </li>
            </ol>
          </nav>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            {/* Product Images */}
            <div className="p-6 lg:p-8 lg:border-r border-gray-100">
              {mediaItems.length > 0 ? (
                <div className="space-y-4">
                  {/* Main Image with proper proportions */}
                  <div className={`relative aspect-square w-full overflow-hidden rounded-xl ${isProductSoldOut(product) ? 'group' : ''}`}>
                    <Image
                      src={getFullCloudinaryUrl(mediaItems[selectedImageIndex]?.url)}
                      alt={product.name}
                      fill
                      priority
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      className={`object-cover ${isProductSoldOut(product) ? 'group-hover:scale-105 transition-transform duration-500' : ''}`}
                    />
                    {isProductSoldOut(product) && <SoldOutBadge />}
                  </div>
                  
                  {/* 3D/AR Viewer Button */}
                  <div className="flex justify-center">
                    <button
                      onClick={() => setShowModelViewer(true)}
                      className="mt-4 px-4 py-2 text-sm text-white bg-black rounded hover:bg-gray-800 transition flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 2a1 1 0 00-1 1v14a1 1 0 001 1h10a1 1 0 001-1V3a1 1 0 00-1-1H5zm0 2h10v12H5V4zm2 3a1 1 0 011-1h4a1 1 0 110 2H8a1 1 0 01-1-1zm0 3a1 1 0 011-1h2a1 1 0 110 2H8a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      View in AR / 3D
                    </button>
                  </div>
                  
                  {/* Image Thumbnails */}
                  {mediaItems.length > 1 && (
                    <div className="grid grid-cols-4 gap-3 justify-center">
                      {mediaItems.map((media, index) => (
                        <div
                          key={media.id}
                          onClick={() => setSelectedImageIndex(index)}
                          className={`cursor-pointer border-2 rounded-md p-1 ${
                            selectedImageIndex === index 
                              ? 'border-black' 
                              : 'border-transparent hover:border-gray-300'
                          }`}
                        >
                          <Image
                            src={getCloudinaryUrl(media.url)}
                            alt={`Thumbnail ${index + 1}`}
                            width={80}
                            height={80}
                            className="object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="aspect-square rounded-lg bg-gray-100 flex items-center justify-center">
                  <p className="text-gray-400">No images available</p>
                </div>
              )}
            </div>

            {/* Product Information */}
            <div className="p-6 lg:p-8">
              <div className="space-y-6">
                {/* Product Header */}
                <div>
                  {category && (
                    <Link 
                      href={`/category/${category.slug}`}
                      className="inline-block text-sm font-medium text-[#666666] mb-2 hover:text-[#171717]"
                    >
                      {category.name}
                    </Link>
                  )}
                  <h1 className="text-3xl font-heading text-[#171717] mb-2">{product.name}</h1>
                  {product.specifications?.model && (
                    <p className="text-gray-500 text-sm">Model: {product.specifications.model}</p>
                  )}
                  <div className="mt-4 flex items-baseline">
                    <span className="text-2xl font-medium text-[#171717]">{formatCurrency(product.price)}</span>
                    
                    {/* Availability indicator */}
                    <span className={`ml-3 px-2 py-1 text-xs rounded-full ${
                      isProductSoldOut(product)
                        ? 'bg-red-50 text-red-700'
                        : 'bg-green-50 text-green-700'
                    }`}>
                      {isProductSoldOut(product)
                        ? 'Sold Out'
                        : `${product.quantity} in stock`}
                    </span>
                  </div>
                </div>
                
                {/* Quick Specs */}
                <div className="grid grid-cols-2 gap-4 py-4 border-t border-b border-gray-100">
                  {product.specifications?.condition && (
                    <div>
                      <p className="text-xs text-gray-500">Condition</p>
                      <p className="font-medium">{formatCondition(product.specifications.condition)}</p>
                    </div>
                  )}
                  {product.specifications?.color && (
                    <div>
                      <p className="text-xs text-gray-500">Color</p>
                      <p className="font-medium">{product.specifications.color}</p>
                    </div>
                  )}
                </div>
                
                {/* Add to Cart Section */}
                <div className="space-y-4">
                  <div className="flex items-center">
                    <span className="mr-4 text-sm font-medium text-gray-700">Quantity</span>
                    <div className="flex items-center border border-gray-300 rounded-md bg-gray-50">
                      <button 
                        disabled
                        className="w-10 h-10 flex items-center justify-center text-gray-400 cursor-not-allowed"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                        </svg>
                      </button>
                      <span className="w-10 text-center">1</span>
                      <button 
                        disabled
                        className="w-10 h-10 flex items-center justify-center text-gray-400 cursor-not-allowed"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </button>
                    </div>
                    <span className="ml-4 text-xs text-gray-500">
                      {isProductSoldOut(product) ? 'Out of Stock' : 'Limited availability'}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <motion.button 
                      onClick={(e) => handleAddToCart(e)}
                      disabled={isProductSoldOut(product)}
                      style={{ scale: cartButtonScale }}
                      className={`relative inline-block px-6 py-3 text-white font-body tracking-wide text-sm transition-all duration-300 rounded-md overflow-hidden group ${
                        isProductSoldOut(product)
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-[#171717] hover:bg-[#333333]'
                      }`}
                      whileHover={isProductSoldOut(product) ? {} : { y: -2, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
                      whileTap={isProductSoldOut(product) ? {} : { scale: 0.98 }}
                    >
                      {/* Subtle background gradient */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${
                        isProductSoldOut(product)
                          ? 'from-gray-500 to-gray-600'
                          : 'from-gray-800 to-black'
                      }`}></div>
                      
                      {/* Button animation when clicked */}
                      {cartAnimation && (
                        <motion.div 
                          className="absolute inset-0 bg-white"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: [0, 0.15, 0] }}
                          transition={{ duration: 0.5, times: [0, 0.1, 1] }}
                        />
                      )}

                      <motion.span 
                        className="relative z-10 flex items-center justify-center"
                        animate={cartAnimation ? {
                          opacity: [1, 0.7, 1],
                          y: [0, -2, 0]
                        } : {}}
                        transition={{ duration: 0.5, times: [0, 0.5, 1] }}
                      >
                        {cartAnimation ? (
                          <span className="flex items-center">
                            Added to Cart
                            <motion.svg 
                              xmlns="http://www.w3.org/2000/svg" 
                              width="18" 
                              height="18" 
                              viewBox="0 0 24 24" 
                              fill="none" 
                              stroke="currentColor" 
                              strokeWidth="2" 
                              strokeLinecap="round" 
                              strokeLinejoin="round"
                              className="ml-2"
                              initial={{ opacity: 0, scale: 0.5 }}
                              animate={{ 
                                opacity: 1,
                                scale: 1
                              }}
                              transition={{ 
                                duration: 0.2,
                                delay: 0.1
                              }}
                            >
                              <path d="M20 6L9 17l-5-5"></path>
                            </motion.svg>
                          </span>
                        ) : (
                          <span className="flex items-center">
                            <svg 
                              xmlns="http://www.w3.org/2000/svg" 
                              width="18" 
                              height="18" 
                              viewBox="0 0 24 24" 
                              fill="none" 
                              stroke="currentColor" 
                              strokeWidth="2" 
                              strokeLinecap="round" 
                              strokeLinejoin="round"
                              className="mr-2 group-hover:translate-x-[2px] transition-transform duration-300"
                            >
                              <circle cx="9" cy="21" r="1"></circle>
                              <circle cx="20" cy="21" r="1"></circle>
                              <path d="M1 1h4l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            {isProductSoldOut(product) ? 'Sold Out' : 'Add to Cart'}
                          </span>
                        )}
                      </motion.span>
                    </motion.button>
                    
                    <motion.button 
                      onClick={(e) => handleAddToWishlist(e)}
                      style={{ scale: wishlistButtonScale }}
                      className="relative inline-block px-6 py-3 text-white font-body tracking-wide text-sm transition-all duration-300 bg-[#171717] hover:bg-[#333333] rounded-md overflow-hidden group"
                      whileHover={{ y: -2, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {/* Subtle background gradient */}
                      <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-black"></div>
                      
                      {/* Button animation when clicked */}
                      {wishlistAnimation && (
                        <motion.div 
                          className="absolute inset-0 bg-white"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: [0, 0.15, 0] }}
                          transition={{ duration: 0.5, times: [0, 0.1, 1] }}
                        />
                      )}

                      <motion.span 
                        className="relative z-10 flex items-center justify-center"
                        animate={wishlistAnimation ? {
                          opacity: [1, 0.7, 1],
                          y: [0, -2, 0]
                        } : {}}
                        transition={{ duration: 0.5, times: [0, 0.5, 1] }}
                      >
                        {wishlistAnimation ? (
                          <span className="flex items-center">
                            Added to Wishlist
                            <motion.svg 
                              xmlns="http://www.w3.org/2000/svg" 
                              width="18"
                              height="18"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="ml-2"
                              initial={{ opacity: 0, scale: 0.5 }}
                              animate={{ 
                                opacity: 1,
                                scale: 1
                              }}
                              transition={{ 
                                duration: 0.2,
                                delay: 0.1
                              }}
                            >
                              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                            </motion.svg>
                          </span>
                        ) : (
                          <span className="flex items-center">
                            <svg 
                              xmlns="http://www.w3.org/2000/svg" 
                              width="18" 
                              height="18" 
                              viewBox="0 0 24 24" 
                              fill="none" 
                              stroke="currentColor" 
                              strokeWidth="2" 
                              strokeLinecap="round" 
                              strokeLinejoin="round"
                              className="mr-2 group-hover:scale-110 transition-transform duration-300"
                            >
                              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                            </svg>
                            Add to Wishlist
                          </span>
                        )}
                      </motion.span>
                    </motion.button>
                  </div>
                </div>
                
                {/* Display wishlist message if present */}
                {wishlistMessage && (
                  <div className={`mt-4 p-3 rounded-md ${wishlistMessage.type === 'success' ? 'bg-green-100 text-green-800' : wishlistMessage.type === 'error' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`}>
                    {wishlistMessage.text}
                  </div>
                )}
                
                {/* Tabs for Details and Specifications */}
                <div className="pt-6">
                  <div className="border-b border-gray-200">
                    <nav className="flex -mb-px">
                      <button
                        onClick={() => setSelectedTab('details')}
                        className={`py-4 px-1 mr-8 font-medium text-sm border-b-2 ${
                          selectedTab === 'details'
                            ? 'border-[#171717] text-[#171717]'
                            : 'border-transparent text-gray-500 hover:text-[#171717]'
                        }`}
                      >
                        Details
                      </button>
                      <button
                        onClick={() => setSelectedTab('specifications')}
                        className={`py-4 px-1 mr-8 font-medium text-sm border-b-2 ${
                          selectedTab === 'specifications'
                            ? 'border-[#171717] text-[#171717]'
                            : 'border-transparent text-gray-500 hover:text-[#171717]'
                        }`}
                      >
                        Specifications
                      </button>
                    </nav>
                  </div>
                  
                  <div className="pt-4">
                    {selectedTab === 'details' ? (
                      <div className="prose prose-sm max-w-none">
                        <p>{product.description}</p>
                        {product.specifications?.versatility && (
                          <div className="mt-4">
                            <h4 className="text-sm font-medium mb-2">Versatility</h4>
                            <p>{product.specifications.versatility}</p>
                          </div>
                        )}
                        {product.specifications?.rarity && (
                          <div className="mt-4">
                            <h4 className="text-sm font-medium mb-2">Rarity</h4>
                            <p>{product.specifications.rarity}</p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="prose prose-sm max-w-none">
                        <h3 className="text-lg font-medium text-gray-900">Specifications</h3>
                        <ul className="mt-2 space-y-3">
                          {product.specifications?.model && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                              <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                              </svg>
                              <span className="font-medium">Model:</span> {product.specifications?.model || ''}
                            </li>
                          )}
                          {product.specifications?.color && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                            <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                            </svg>
                              <span className="font-medium">Color:</span> {product.specifications?.color || ''}
                            </li>
                          )}
                          {product.specifications?.material && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                              <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.828 14.849a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span className="font-medium">Material:</span> {product.specifications?.material || ''}
                            </li>
                          )}
                          {product.specifications?.size && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                              <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5" />
                              </svg>
                              <span className="font-medium">Size:</span> {product.specifications?.size || ''}
                            </li>
                          )}
                          {product.specifications?.design && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                            <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                              <span className="font-medium">Design:</span> {product.specifications?.design || ''}
                            </li>
                          )}
                          {product.specifications?.strap && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                              <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.849a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span className="font-medium">Strap:</span> {product.specifications?.strap || ''}
                            </li>
                          )}
                          {product.specifications?.interior && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                              <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                              </svg>
                              <span className="font-medium">Interior:</span> {product.specifications?.interior || ''}
                            </li>
                          )}
                          {product.specifications?.condition && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                              <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                              <span className="font-medium">Condition:</span> {formatCondition(product.specifications.condition)}
                            </li>
                          )}
                          {product.specifications?.versatility && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                              <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                              <span className="font-medium">Versatility:</span> {product.specifications?.versatility}
                            </li>
                          )}
                          {product.specifications?.rarity && (
                            <li className="flex items-center transition-transform hover:translate-x-1 duration-200">
                              <svg className="w-5 h-5 mr-2 text-[#171717]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                              </svg>
                              <span className="font-medium">Rarity:</span> {product.specifications?.rarity}
                            </li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>

                {/* Certificate of Authenticity Section */}
                {product && (
                  <div className="mt-8">
                    <CertificateOfAuthenticity
                      productId={product.id}
                      productName={product.name}
                      serialNumber={product.specifications?.serial_number || product.id.slice(0, 8).toUpperCase()}
                      issueDate={product.specifications?.certificate_issued_at 
                        ? new Date(product.specifications.certificate_issued_at as string).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                        : 'Not Available'}
                      description={product.description || ''}
                      condition={product.specifications?.condition || ''}
                      imageUrl={mediaItems.length > 0 ? getCloudinaryUrl(mediaItems[0].url) : ''}
                    />
                  </div>
                )}
                
              </div>
            </div>
          </div>
        
        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div className="mt-12">
            <h2 className="text-xl font-heading text-[#171717] mb-6">You may also like</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {relatedProducts.map(relatedProduct => (
                <Link 
                  href={`/products/${relatedProduct.slug}`} 
                  key={relatedProduct.id}
                  className="group"
                >
                  <div className="aspect-square rounded-md overflow-hidden bg-gray-100 relative mb-2">
                    {relatedProduct.mediaItems && relatedProduct.mediaItems.length > 0 ? (
                      <Image
                        src={getCloudinaryUrl(relatedProduct.mediaItems[0].url)}
                        alt={relatedProduct.name}
                        width={200}
                        height={200}
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <span className="text-gray-400 text-sm">No image</span>
                      </div>
                    )}
                  </div>
                  <h3 className="text-sm font-medium text-[#171717] truncate">{relatedProduct.name}</h3>
                  <p className="text-sm text-[#666666]">{formatCurrency(relatedProduct.price)}</p>
                </Link>
              ))}
            </div>
          </div>
        )}
        
        {/* Discover New Arrivals Section */}
        <div className="mt-16 mb-8">
          <div className="bg-gradient-to-r from-gray-100 to-gray-50 rounded-lg overflow-hidden shadow-sm">
            <div className="flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 p-8 md:p-12">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <h2 className="text-3xl font-heading text-[#171717] mb-4">Discover New Arrivals</h2>
                  <p className="text-gray-600 mb-6">Be the first to explore our latest luxury additions. Each piece is unique and carefully selected for our discerning clientele.</p>
                  <Link 
                    href="/new-arrivals" 
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-[#171717] hover:bg-[#333333] transition-colors duration-200"
                  >
                    View New Arrivals
                    <svg className="ml-2 -mr-1 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </motion.div>
              </div>
              <div className="md:w-1/2 relative">
                <div className="aspect-[4/3] md:aspect-auto md:h-full relative">
                  <Image
                    src="/images/about-hero.JPG"
                    alt="Luxury Collection"
                    width={400}
                    height={300}
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Shopping Cart Animation */}
      {cartAnimation && (
        <motion.div
          className="fixed z-50 pointer-events-none"
          initial={{ 
            opacity: 0,
            scale: 0.4,
            top: floatingImage.startY, 
            left: floatingImage.startX,
          }}
          animate={{ 
            opacity: [0, 1, 1, 0],
            scale: [0.4, 0.6, 0.6, 0.4],
            top: [
              floatingImage.startY,
              floatingImage.startY - 20,
              floatingImage.type === 'cart' ? window.innerHeight - 80 : 80,
              floatingImage.type === 'cart' ? window.innerHeight - 60 : 60
            ],
            left: [
              floatingImage.startX,
              floatingImage.startX,
              floatingImage.type === 'cart' ? window.innerWidth - 80 : window.innerWidth / 2,
              floatingImage.type === 'cart' ? window.innerWidth - 60 : window.innerWidth / 2,
            ],
          }}
          transition={{ 
            duration: 1.2,
            ease: "easeInOut",
            times: [0, 0.1, 0.9, 1]
          }}
        >
          {/* Animation content */}
        </motion.div>
      )}
      
      {/* 3D/AR Model Viewer Modal */}
      {showModelViewer && (
        <motion.div 
          className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <div className="relative bg-white rounded-lg w-full max-w-4xl h-[80vh] overflow-hidden">
            <button
              onClick={() => setShowModelViewer(false)}
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-800"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Model Viewer */}
            {mediaItems.length > 0 && mediaItems[selectedImageIndex]?.url && (
              modelLoadError ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center p-8">
                    <svg className="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <h3 className="text-xl font-semibold mb-2">3D Model Not Available</h3>
                    <p className="text-gray-600">We couldn't load a 3D model for this product. Please try another image or contact us for more information.</p>
                  </div>
                </div>
              ) : (
                <model-viewer
              src={
                product?.model_url 
                  ? product.model_url 
                  : (
                      getCloudinaryUrl(mediaItems[selectedImageIndex]?.url || '')
                        ?.replace('/image/upload/', '/image/upload/auto_3d/v1/')
                        ?.replace(/\.\w+$/, '.glb') || ''
                    )
              }
                  alt={product?.name || "Product"}
                  ar
                  ar-modes="webxr scene-viewer quick-look"
                  environment-image="neutral"
                  auto-rotate
                  camera-controls
                  style={{ width: '100%', height: '100%' }}
                  onError={() => setModelLoadError(true)}
                ></model-viewer>
              )
            )}
          </div>
        </motion.div>
      )}
      
      {/* Floating image animation */}
      {floatingImage.active && (
        <motion.div
          className="fixed z-50 pointer-events-none"
          initial={{ 
            opacity: 0,
            scale: 0.4,
            top: floatingImage.startY, 
            left: floatingImage.startX,
          }}
          animate={{ 
            opacity: [0, 1, 1, 0],
            scale: [0.4, 0.6, 0.6, 0.4],
            top: [
              floatingImage.startY,
              floatingImage.startY - 20,
              floatingImage.type === 'cart' ? window.innerHeight - 80 : 80,
              floatingImage.type === 'cart' ? window.innerHeight - 60 : 60
            ],
            left: [
              floatingImage.startX,
              floatingImage.startX,
              floatingImage.type === 'cart' ? window.innerWidth - 80 : window.innerWidth / 2,
              floatingImage.type === 'cart' ? window.innerWidth - 60 : window.innerWidth / 2,
            ],
          }}
          transition={{ 
            duration: 1.2,
            ease: "easeInOut",
            times: [0, 0.1, 0.9, 1]
          }}
        >
          <div className="relative h-16 w-16 rounded-lg overflow-hidden border border-white/20 shadow-md">
            {floatingImage.imageUrl && (
              <Image 
                src={getCloudinaryUrl(floatingImage.imageUrl)}
                alt="Product"
                width={80}
                height={80}
                className="h-full w-full object-contain"
              />
            )}
            <motion.div 
              className="absolute inset-0 flex items-center justify-center bg-black/30"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8, duration: 0.2 }}
            >
              {floatingImage.type === 'cart' ? (
                <svg fill="none" stroke="white" viewBox="0 0 24 24">
                  <circle cx="9" cy="21" r="1"></circle>
                  <circle cx="20" cy="21" r="1"></circle>
                  <path d="M1 1h4l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              ) : (
                <svg fill="none" stroke="white" viewBox="0 0 24 24">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
              )}
            </motion.div>
          </div>
        </motion.div>
      )}
    </div> 
    </div>
  );
}
