'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/lib/database.types';
import { getCloudinaryUrl, getProductImageUrl } from '@/lib/image-utils';

// Define interfaces using the database types
type ProductRow = Database['public']['Tables']['products']['Row'];
type CategoryRow = Database['public']['Tables']['categories']['Row'];
type MediaItemRow = Database['public']['Tables']['product_media']['Row'];

// Define a simplified MediaItem type for what we get from the join query
interface MediaItem {
  id: string;
  product_id: string | null;
  url: string;
  type?: string;
  position?: number | null;
  alt?: string | null;
  created_at?: string | null;
  is_main?: boolean | null;
}

// Create a type that extends ProductRow but with proper specifications handling
interface Product extends Omit<ProductRow, 'specifications'> {
  image_url?: string;
  mediaItems?: MediaItem[];
  specifications?: {
    condition?: string;
    [key: string]: any;
  } | null;
}

interface Category extends CategoryRow {}

interface FilterState {
  category: string | null;
  priceRange: [number, number];
  sort: string;
}

// Add this helper function near the top of the file, after the type definitions
const isProductSoldOut = (product: Product) => {
  return product.status === 'sold_out' || !product.quantity || product.quantity <= 0;
};

export default function ProductsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get initial category from URL if present
  const initialCategory = searchParams.get('category');
  
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<FilterState>({
    category: initialCategory,
    priceRange: [0, 10000],
    sort: 'newest',
  });
  const [error, setError] = useState<string | null>(null);
  const [priceMin, setPriceMin] = useState<number>(0);
  const [priceMax, setPriceMax] = useState<number>(10000);
  const [showFilters, setShowFilters] = useState(false);
  const [quickViewProduct, setQuickViewProduct] = useState<Product | null>(null);
  const [isFilterUpdating, setIsFilterUpdating] = useState(false);
  const [visibleProducts, setVisibleProducts] = useState<number>(12);
  const productContainerRef = useRef<HTMLDivElement>(null);
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.05 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };
  
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } }
  };

  useEffect(() => {
    fetchCategories();
    fetchProducts();
    
    // Scroll to top on mount
    window.scrollTo(0, 0);
    
    // Add scroll event listener for infinite scroll
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    applyFilters();
  }, [products, filters]);
  
  const handleScroll = () => {
    if (productContainerRef.current && filteredProducts.length > visibleProducts) {
      const containerBottom = productContainerRef.current.getBoundingClientRect().bottom;
      const isNearBottom = containerBottom <= window.innerHeight + 300;
      
      if (isNearBottom) {
        loadMoreProducts();
      }
    }
  };
  
  const loadMoreProducts = () => {
    setVisibleProducts(prev => Math.min(prev + 8, filteredProducts.length));
  };

  const fetchCategories = async () => {
    try {
      const supabase = createClientComponentClient<Database>();
      const { data, error } = await supabase.from('categories').select('*');
      if (error) {
        console.error('Error loading categories:', error);
        setCategories([]);
      } else {
        setCategories(data || []);
      }
    } catch (err) {
      console.error('Error loading categories:', err);
      setCategories([]);
    }
  };

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const supabase = createClientComponentClient<Database>();
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          product_media:product_media(id, product_id, url, type, position, alt, created_at, is_main)
        `);

      if (error) {
        console.error('Error loading products:', error);
        setProducts([]);
        setError('Failed to load products. Please try again later.');
      } else {
        // Process the products data
        if (data) {
          try {
            // Map the data to include specifications and media items
            const productsWithDetails = data.map(product => {
              const specifications = product.specifications || {};
              const mediaItems = product.product_media || [];
              
              // Convert mediaItems to the expected Database type format
              const typedMediaItems: Database['public']['Tables']['product_media']['Row'][] = 
                mediaItems.map(item => ({
                  id: item.id,
                  product_id: item.product_id,
                  url: item.url,
                  type: item.type || 'image',
                  position: item.position || null,
                  alt: item.alt || null,
                  created_at: item.created_at || null,
                  is_main: item.is_main || null
                }));
              
              return {
                ...product,
                specifications,
                mediaItems: typedMediaItems,
                image_url: getProductImageUrl(typedMediaItems)
              } as Product;
            });
            
            setProducts(productsWithDetails);
            if (productsWithDetails.length > 0) {
              const prices = productsWithDetails.map(p => p.price);
              const min = Math.min(...prices);
              const max = Math.max(...prices);
              setPriceMin(min);
              setPriceMax(max);
              setFilters(prev => ({
                ...prev,
                priceRange: [min, max]
              }));
            }
          } catch (err) {
            console.error('Error processing products:', err);
            setProducts([]);
          }
        }
      }
    } catch (err) {
      console.error('Error loading products:', err);
      setError('Failed to load products. Please try again later.');
    }
    setLoading(false);
  };
  
  const applyFilters = () => {
    setIsFilterUpdating(true);
    let result = [...products];
    
    if (filters.category) {
      result = result.filter(product => product.category_id === filters.category);
    }
    
    result = result.filter(product => 
      product.price >= filters.priceRange[0] && 
      product.price <= filters.priceRange[1]
    );
    
    switch (filters.sort) {
      case 'newest':
        result.sort((a, b) => {
          const dateA = new Date(a.created_at || 0);
          const dateB = new Date(b.created_at || 0);
          return dateB.getTime() - dateA.getTime();
        });
        break;
      case 'price-asc':
        result.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        result.sort((a, b) => b.price - a.price);
        break;
      case 'name-asc':
        result.sort((a, b) => a.name.localeCompare(b.name));
        break;
    }
    
    setFilteredProducts(result);
    setVisibleProducts(12); 
    setIsFilterUpdating(false);
  };
  
  const updateURL = (categoryId: string | null) => {
    const params = new URLSearchParams(window.location.search);
    
    if (categoryId) {
      params.set('category', categoryId);
    } else {
      params.delete('category');
    }
    
    router.replace(`/products?${params.toString()}`);
  };

  const handleCategoryChange = (categoryId: string | null) => {
    setFilters(prev => ({ ...prev, category: categoryId }));
    updateURL(categoryId);
  };
  
  const handleSortChange = (sortOption: string) => {
    setFilters(prev => ({ ...prev, sort: sortOption }));
  };
  
  const handlePriceRangeChange = (min: number, max: number) => {
    setFilters(prev => ({ ...prev, priceRange: [min, max] }));
  };
  
  const openQuickView = (product: Product) => {
    setQuickViewProduct(product);
  };
  
  const closeQuickView = () => {
    setQuickViewProduct(null);
  };

  const getCategoryName = (categoryId: string | null | undefined): string => {
    if (!categoryId) return '';
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : '';
  };
  
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const getConditionText = (product: Product): string => {
    if (!product.specifications || !product.specifications.condition) return '';
    
    const condition = product.specifications.condition;
    const conditionMap: Record<string, string> = {
      'new': 'New with tags',
      'like_new': 'Like new',
      'good': 'Good condition',
      'fair': 'Fair condition',
      'poor': 'Poor condition'
    };
    
    return conditionMap[condition.toLowerCase()] || condition;
  };

  return (
    <div className="min-h-screen pt-20 pb-24 bg-[#f8f8f8]">
      {/* Hero Banner */}
      <div className="relative h-[30vh] md:h-[40vh] bg-[#171717] overflow-hidden">
        <div className="absolute inset-0 opacity-60">
          <Image 
            src="https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2070&auto=format&fit=crop" 
            alt="Luxury bags collection"
            fill
            priority
            className="object-cover object-center"
          />
        </div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/40" />
        
        <div className="relative z-10 flex items-center justify-center h-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <motion.h1 
              className="text-3xl md:text-5xl font-heading tracking-wider text-white mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              LUXURY COLLECTION
            </motion.h1>
            <motion.p 
              className="text-lg text-white/80 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Discover our handpicked selection of premium designer bags
            </motion.p>
          </div>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Filters Section */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-8">
          <div className="p-6 border-b border-gray-100">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <h2 className="text-xl font-heading text-[#171717]">
                {filteredProducts.length} {filteredProducts.length === 1 ? 'Product' : 'Products'}
              </h2>
              
              <div className="flex items-center gap-4">
                <button 
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 text-sm font-medium text-[#171717] hover:text-[#444]"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                  </svg>
                  {showFilters ? 'Hide Filters' : 'Show Filters'}
                </button>
                
                <div className="relative">
                  <select
                    value={filters.sort}
                    onChange={(e) => handleSortChange(e.target.value)}
                    className="appearance-none bg-white border border-gray-200 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-gray-200"
                  >
                    <option value="newest">Newest First</option>
                    <option value="price-asc">Price: Low to High</option>
                    <option value="price-desc">Price: High to Low</option>
                    <option value="name-asc">Name: A to Z</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <AnimatePresence>
            {showFilters && (
              <motion.div 
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="p-6 bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Category Filter */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-3">Categories</h3>
                      <div className="space-y-2">
                        <button
                          onClick={() => handleCategoryChange(null)}
                          className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                            filters.category === null
                              ? 'bg-[#171717] text-white'
                              : 'bg-white hover:bg-gray-100 text-gray-700'
                          }`}
                        >
                          All Categories
                        </button>
                        {categories.map((category) => (
                          <button
                            key={category.id}
                            onClick={() => handleCategoryChange(category.id)}
                            className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                              filters.category === category.id
                                ? 'bg-[#171717] text-white'
                                : 'bg-white hover:bg-gray-100 text-gray-700'
                            }`}
                          >
                            {category.name}
                          </button>
                        ))}
                      </div>
                    </div>
                    
                    {/* Price Range Filter */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-3">Price Range</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500">{formatPrice(filters.priceRange[0])}</span>
                          <span className="text-sm text-gray-500">{formatPrice(filters.priceRange[1])}</span>
                        </div>
                        <div className="relative pt-1">
                          <input
                            type="range"
                            min={priceMin}
                            max={priceMax}
                            value={filters.priceRange[0]}
                            onChange={(e) => handlePriceRangeChange(Number(e.target.value), filters.priceRange[1])}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                          />
                          <input
                            type="range"
                            min={priceMin}
                            max={priceMax}
                            value={filters.priceRange[1]}
                            onChange={(e) => handlePriceRangeChange(filters.priceRange[0], Number(e.target.value))}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer mt-4"
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* Reset Filters */}
                    <div className="flex items-end">
                      <button
                        onClick={() => {
                          setFilters({
                            category: null,
                            priceRange: [priceMin, priceMax],
                            sort: 'newest'
                          });
                          updateURL(null);
                        }}
                        className="w-full px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 text-sm rounded-md transition-colors"
                      >
                        Reset Filters
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        
        {error && (
          <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-8">
            {error}
          </div>
        )}
        
        {/* Products Grid */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717]"></div>
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="text-center py-16 bg-white rounded-xl shadow-sm">
            <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
            </svg>
            <h3 className="text-xl font-medium text-gray-700 mb-4">No products found</h3>
            <p className="text-gray-500 mb-6">
              {filters.category
                ? "No products match your current filters."
                : "No products available at the moment."}
            </p>
            <button
              onClick={() => {
                setFilters({
                  category: null,
                  priceRange: [priceMin, priceMax],
                  sort: 'newest'
                });
                updateURL(null);
              }}
              className="inline-block px-6 py-3 bg-[#171717] text-white rounded-md hover:bg-[#333333] transition-colors duration-300"
            >
              Clear Filters
            </button>
          </div>
        ) : (
          <div ref={productContainerRef}>
            <motion.div 
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              variants={containerVariants}
              initial="hidden"
              animate={isFilterUpdating ? "hidden" : "visible"}
            >
              {filteredProducts.slice(0, visibleProducts).map((product) => (
                <motion.div
                  key={product.id}
                  className="group"
                  variants={itemVariants}
                >
                  <div className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
                    <Link href={`/products/${product.slug}`}>
                      <div className="relative h-64 w-full overflow-hidden rounded-lg bg-gray-100">
                        <Image
                          src={product.image_url || '/placeholder-image.jpg'}
                          alt={product.name}
                          width={300}
                          height={300}
                          className="h-full w-full object-cover object-center group-hover:scale-105 transition-transform duration-500"
                        />
                        
                        {/* Condition Badge */}
                        {product.specifications?.condition && (
                          <div className="absolute top-3 left-3 bg-black/70 text-white text-xs px-2 py-1 rounded">
                            {getConditionText(product)}
                          </div>
                        )}
                        
                        {/* Sold Out Overlay */}
                        {isProductSoldOut(product) && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                            <span className="text-white font-medium px-4 py-2 bg-black bg-opacity-75 rounded-md">
                              Sold Out
                            </span>
                          </div>
                        )}
                      </div>
                    </Link>
                    
                    <div className="p-4">
                      <Link href={`/products/${product.slug}`}>
                        <h3 className="font-heading text-base text-[#171717] mb-1 truncate hover:text-[#444]">
                          {product.name}
                        </h3>
                      </Link>
                      <p className="text-sm text-[#666666] mb-2">{getCategoryName(product.category_id)}</p>
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-[#171717]">€{product.price.toLocaleString()}</p>
                        <div className="flex items-center space-x-2">
                          {isProductSoldOut(product) && (
                            <span className="text-xs font-medium text-red-600">
                              Sold Out
                            </span>
                          )}
                          <button
                            onClick={() => openQuickView(product)}
                            className="text-xs font-medium uppercase tracking-wider text-[#666] hover:text-[#171717]"
                          >
                            Quick View
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
            
            {/* Load More Button - visible when there are more products to load */}
            {visibleProducts < filteredProducts.length && (
              <div className="text-center mt-12">
                <button
                  onClick={loadMoreProducts}
                  className="inline-block px-6 py-3 bg-white border border-gray-200 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors"
                >
                  Load More Products
                </button>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Quick View Modal */}
      <AnimatePresence>
        {quickViewProduct && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 overflow-y-auto"
          >
            <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" onClick={closeQuickView}>
                <div className="absolute inset-0 bg-black opacity-75"></div>
              </div>
              
              <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>
              
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
                className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
              >
                <div className="absolute top-0 right-0 pt-4 pr-4">
                  <button
                    onClick={closeQuickView}
                    className="bg-white rounded-full p-1 text-gray-400 hover:text-gray-500 focus:outline-none"
                  >
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2">
                  <div className="p-6">
                    <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden relative">
                      <Image
                        src={quickViewProduct.image_url || '/placeholder-image.jpg'}
                        alt={quickViewProduct.name}
                        fill
                        sizes="(max-width: 768px) 100vw, 50vw"
                        className="object-cover object-center"
                      />
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <div className="mb-4">
                      <h3 className="text-xl font-heading text-[#171717] mb-2">{quickViewProduct.name}</h3>
                      <p className="text-sm text-[#666666] mb-4">{getCategoryName(quickViewProduct.category_id)}</p>
                      <p className="text-2xl font-medium text-[#171717] mb-6">€{quickViewProduct.price.toLocaleString()}</p>
                    </div>
                    
                    <div className="prose prose-sm mb-6">
                      <p>{quickViewProduct.description}</p>
                    </div>
                    
                    {quickViewProduct.specifications?.condition && (
                      <div className="mb-6">
                        <p className="text-sm font-medium text-[#171717] mb-1">Condition</p>
                        <p>{getConditionText(quickViewProduct)}</p>
                      </div>
                    )}
                    
                    <div className="space-y-4">
                      <Link 
                        href={`/products/${quickViewProduct.slug}`}
                        className="block w-full bg-[#171717] text-white py-3 px-6 font-body tracking-wide text-sm text-center hover:bg-[#333333] transition-colors duration-300 rounded-md"
                      >
                        View Full Details
                      </Link>
                      
                      <button 
                        className={`block w-full py-3 px-6 font-body tracking-wide text-sm text-center transition-colors duration-300 rounded-md ${
                          isProductSoldOut(quickViewProduct)
                            ? 'bg-gray-400 text-white cursor-not-allowed'
                            : 'bg-white border border-[#171717] text-[#171717] hover:bg-gray-50'
                        }`}
                        disabled={isProductSoldOut(quickViewProduct)}
                      >
                        {isProductSoldOut(quickViewProduct) ? 'Sold Out' : 'Add to Cart'}
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
