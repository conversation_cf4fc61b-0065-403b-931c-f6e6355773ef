'use client';

import { motion } from 'framer-motion';

export default function RefundPolicyPage() {
  return (
    <div className="min-h-screen bg-[#f8f8f8] pt-32 pb-24">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-4xl md:text-5xl font-heading tracking-wider text-[#171717] mb-8 text-center">
            Return & Refund Policy
          </h1>

          <div className="bg-white rounded-xl shadow-sm p-8 md:p-12 space-y-8">
            <section>
              <h2 className="text-2xl font-heading mb-4">Return Eligibility</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>
                  We accept returns within 14 days of delivery, provided that:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>The item is in its original condition</li>
                  <li>All original tags are attached</li>
                  <li>Original packaging is intact</li>
                  <li>A return request has been submitted</li>
                </ul>
                <div className="bg-[#f8f8f8] p-4 rounded-lg mt-4">
                  <p className="font-medium text-[#171717]">Please Note:</p>
                  <p className="mt-2">
                    Some items may be marked as "Final Sale" due to their unique vintage nature. 
                    These items are not eligible for return unless they are found to be inauthentic 
                    or significantly misrepresented in their description.
                  </p>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Return Process</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>To initiate a return:</p>
                <ol className="list-decimal list-inside space-y-2 ml-4">
                  <li>Contact our customer service within 14 days of delivery</li>
                  <li>Fill out the return form provided via email</li>
                  <li>Receive return shipping instructions</li>
                  <li>Package the item securely with all original materials</li>
                  <li>Ship the item back using a tracked service</li>
                </ol>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Refund Timeline</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Returns are processed within 3-5 business days of receipt</li>
                  <li>Refunds are issued to the original payment method</li>
                  <li>Processing time may vary by payment provider (2-10 business days)</li>
                  <li>You will receive email confirmation once the refund is processed</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Shipping Costs</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Return shipping costs are the responsibility of the customer</li>
                  <li>Original shipping fees are non-refundable</li>
                  <li>We recommend using a tracked shipping service</li>
                  <li>Shipping costs will be refunded only if the return is due to our error</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Non-Returnable Items</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>The following items cannot be returned:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Items marked as "Final Sale"</li>
                  <li>Items that have been worn, altered, or damaged</li>
                  <li>Items without original tags and packaging</li>
                  <li>Items returned after the 14-day window</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Authenticity Guarantee</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>
                  If an item is found to be inauthentic:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>We will provide a full refund including all shipping costs</li>
                  <li>Return shipping will be arranged at our expense</li>
                  <li>No time limit applies to authenticity claims</li>
                  <li>Professional authentication documentation may be required</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Damaged or Incorrect Items</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>
                  If you receive a damaged or incorrect item:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Contact us within 24 hours of delivery</li>
                  <li>Provide photos of the damage or incorrect item</li>
                  <li>We will arrange return shipping at our expense</li>
                  <li>A full refund or replacement will be provided</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Contact Us</h2>
              <p className="text-[#666666] leading-relaxed">
                For any questions about returns or refunds, please contact us at{' '}
                <a 
                  href="mailto:<EMAIL>"
                  className="text-[#171717] hover:underline"
                >
                  <EMAIL>
                </a>
              </p>
            </section>

            <div className="text-sm text-[#666666] pt-8 border-t border-gray-200">
              Last Updated: March 4, 2025
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
