'use client';

import { motion } from 'framer-motion';

export default function ShippingPolicyPage() {
  return (
    <div className="min-h-screen bg-[#f8f8f8] pt-32 pb-24">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-4xl md:text-5xl font-heading tracking-wider text-[#171717] mb-8 text-center">
            Shipping Policy
          </h1>

          <div className="bg-white rounded-xl shadow-sm p-8 md:p-12 space-y-8">
            <section>
              <h2 className="text-2xl font-heading mb-4">Shipping Fees</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>
                  Shipping fees are calculated based on your location and will be displayed during the final 
                  checkout process. The exact shipping cost will be shown before you complete your purchase, 
                  ensuring full transparency.
                </p>
                <div className="bg-[#f8f8f8] p-4 rounded-lg mt-4">
                  <p className="font-medium text-[#171717]">Important Note:</p>
                  <p className="mt-2">
                    All shipping costs are calculated in real-time at checkout based on:
                  </p>
                  <ul className="list-disc list-inside space-y-2 mt-2 ml-4">
                    <li>Destination country and region</li>
                    <li>Selected shipping method</li>
                    <li>Package weight and dimensions</li>
                    <li>Insurance coverage value</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Shipping Methods</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>We offer several shipping options from our location in Valencia, Spain:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Standard Shipping (7-14 business days)</li>
                  <li>Express Shipping (3-5 business days)</li>
                  <li>Priority Express (1-2 business days, select locations)</li>
                </ul>
                <p className="mt-4">
                  Available shipping methods and estimated delivery times will be shown during checkout 
                  based on your location.
                </p>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Order Processing</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Orders are processed within 1-2 business days</li>
                  <li>You'll receive a confirmation email once your order ships</li>
                  <li>Tracking information will be provided via email</li>
                  <li>Signature may be required upon delivery</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">International Shipping</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>For international orders, please note:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Import duties and taxes are not included in the shipping fee</li>
                  <li>These charges are the responsibility of the recipient</li>
                  <li>Customs processing may affect delivery times</li>
                  <li>We provide all necessary customs documentation</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Package Protection</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>All shipments include:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Secure, protective packaging</li>
                  <li>Insurance coverage for loss or damage</li>
                  <li>Tracking capability</li>
                  <li>Signature confirmation for valuable items</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Shipping Restrictions</h2>
              <p className="text-[#666666] leading-relaxed">
                While we ship to most countries worldwide, some restrictions may apply. If we are unable 
                to ship to your location, you will be notified during the checkout process before any 
                payment is processed.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Delivery Issues</h2>
              <div className="space-y-4 text-[#666666] leading-relaxed">
                <p>In case of delivery issues:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Contact us immediately if your tracking shows any problems</li>
                  <li>We'll work with the shipping carrier to resolve any issues</li>
                  <li>Lost packages will be investigated and replaced if necessary</li>
                  <li>Damaged items should be reported within 24 hours of delivery</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-heading mb-4">Contact Us</h2>
              <p className="text-[#666666] leading-relaxed">
                For any shipping-related questions, please contact us at{' '}
                <a 
                  href="mailto:<EMAIL>"
                  className="text-[#171717] hover:underline"
                >
                  <EMAIL>
                </a>
              </p>
            </section>

            <div className="text-sm text-[#666666] pt-8 border-t border-gray-200">
              Last Updated: March 4, 2025
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
