import { MetadataRoute } from 'next';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://treasuresofmaimi.com';
  
  // Static routes with AI-friendly priorities
  const staticRoutes = [
    { route: '', priority: 1, changeFrequency: 'daily' as const },
    { route: '/products', priority: 0.9, changeFrequency: 'daily' as const },
    { route: '/collection', priority: 0.9, changeFrequency: 'weekly' as const },
    { route: '/about', priority: 0.8, changeFrequency: 'monthly' as const },
    { route: '/contact', priority: 0.7, changeFrequency: 'monthly' as const },
    { route: '/new-arrivals', priority: 0.8, changeFrequency: 'daily' as const },
    { route: '/faq', priority: 0.7, changeFrequency: 'monthly' as const },
    { route: '/category', priority: 0.6, changeFrequency: 'weekly' as const },
    { route: '/privacy-policy', priority: 0.5, changeFrequency: 'yearly' as const },
    { route: '/terms-of-service', priority: 0.5, changeFrequency: 'yearly' as const },
    { route: '/shipping-policy', priority: 0.6, changeFrequency: 'monthly' as const },
    { route: '/refund-policy', priority: 0.6, changeFrequency: 'monthly' as const },
    { route: '/login', priority: 0.3, changeFrequency: 'never' as const },
    { route: '/register', priority: 0.3, changeFrequency: 'never' as const },
    // Add AI-specific file
    { route: '/llm.txt', priority: 0.9, changeFrequency: 'monthly' as const },
  ].map(({ route, priority, changeFrequency }) => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
    changeFrequency,
    priority,
  }));

  return [
    ...staticRoutes,
  ];
}
