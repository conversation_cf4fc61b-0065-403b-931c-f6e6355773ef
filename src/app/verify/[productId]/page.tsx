'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import Link from 'next/link';

interface Params {
  params: {
    productId: string;
  };
}

interface VerificationResult {
  verified: boolean;
  product: {
    id: string;
    name: string;
    description: string;
    condition: string;
    issueDate: string;
    serialNumber: string;
    image_url?: string;
    price: number;
  };
}

export default function VerifyPage({ params }: Params) {
  const [result, setResult] = useState<VerificationResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function verifyProduct() {
      try {
        const response = await fetch(`/api/verify/${params.productId}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Unable to verify product');
        }

        setResult(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unable to verify product');
      } finally {
        setLoading(false);
      }
    }

    verifyProduct();
  }, [params.productId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#f5f5f5]">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#171717] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[#666666] font-body">Verifying your product...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#f5f5f5]">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="w-16 h-16 mx-auto mb-4 text-red-500">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h1 className="text-2xl font-heading mb-2">Product Not Found</h1>
          <p className="text-[#666666] font-body mb-6">
            We couldn't find this product in our database. If you believe this is an error, please contact our support team.
          </p>
          <Link
            href="/"
            className="inline-block px-6 py-3 bg-[#171717] text-white rounded-lg hover:bg-[#2c2c2c] transition-colors duration-300"
          >
            Return to Homepage
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f5f5f5] py-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-2xl mx-auto px-4"
      >
        <div className="bg-white rounded-lg shadow-lg p-8">
          {/* Success Icon */}
          <div className="w-16 h-16 mx-auto mb-6 text-green-500">
            <svg
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>

          {/* Welcome Card */}
          <div className="relative w-48 h-48 mx-auto mb-6">
            <Image
              src="/images/welcome-card.png"
              alt="Welcome to Treasure of Maimi"
              fill
              className="object-contain"
            />
          </div>

          {/* Verification Status */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-heading mb-2">Product Verified</h1>
            <p className="text-[#666666] font-body">
              This product has been verified as an authentic Treasure of Maimi item
            </p>
          </div>

          {/* Product Image */}
          {result.product.image_url && (
            <div className="relative w-64 h-64 mx-auto mb-8 rounded-lg overflow-hidden">
              <Image
                src={result.product.image_url}
                alt={result.product.name}
                fill
                className="object-cover"
              />
            </div>
          )}

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h2 className="text-sm text-[#666666] font-body mb-1">Product Name</h2>
              <p className="text-xl font-heading">{result.product.name}</p>
            </div>

            <div>
              <h2 className="text-sm text-[#666666] font-body mb-1">Serial Number</h2>
              <p className="text-xl font-heading">{result.product.serialNumber}</p>
            </div>

            <div>
              <h2 className="text-sm text-[#666666] font-body mb-1">Issue Date</h2>
              <p className="text-xl font-heading">
                {new Date(result.product.issueDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </p>
            </div>

            {result.product.condition && (
              <div>
                <h2 className="text-sm text-[#666666] font-body mb-1">Condition</h2>
                <p className="text-xl font-heading">{result.product.condition}</p>
              </div>
            )}

            {result.product.description && (
              <div>
                <h2 className="text-sm text-[#666666] font-body mb-1">Description</h2>
                <p className="text-[#171717] font-body">{result.product.description}</p>
              </div>
            )}

            <div>
              <h2 className="text-sm text-[#666666] font-body mb-1">Value</h2>
              <p className="text-xl font-heading">
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'USD'
                }).format(result.product.price)}
              </p>
            </div>
          </div>

          {/* View Product Button */}
          <div className="mt-8 text-center">
            <Link
              href={`/products/${result.product.id}`}
              className="inline-block px-6 py-3 bg-[#171717] text-white rounded-lg hover:bg-[#2c2c2c] transition-colors duration-300"
            >
              View Product Details
            </Link>
          </div>

          {/* Treasure of Maimi Seal */}
          <div className="mt-12 pt-8 border-t border-[#e5e5e5] text-center">
            <div className="font-heading text-2xl mb-2">Treasure of Maimi</div>
            <p className="text-sm text-[#666666] font-body">
              Luxury Vintage Authentication Service
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
