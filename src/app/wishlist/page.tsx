'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { customToast } from '@/components/ui/CustomToast';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { motion } from 'framer-motion';
import { getCloudinaryUrl, getProductImageUrl } from '@/lib/image-utils';

// Define wishlist item type locally
interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  product_media?: {
    url: string;
    is_main?: boolean;
    id?: string;
    product_id?: string;
    type?: string;
    position?: number | null;
    alt?: string | null;
    created_at?: string | null;
  }[];
}

interface WishlistItem {
  id: string;
  product_id: string;
  created_at: string;
  product: Product;
}

interface DatabaseWishlistItem {
  id: string;
  product_id: string;
  user_id?: string;
  created_at: string | null;
  product?: {
    id: string;
    name: string;
    slug: string;
    price: number;
    product_media: {
      url: string;
      is_main: boolean;
    }[];
    sale_price: null;
    category_name: null;
    brand: null;
  };
}

// Database wishlist item type (may have slightly different structure)
interface GuestWishlistItem {
  id: string;
  product_id: string;
  created_at: string;
  product: Product;
}

// Define localStorage key for guest wishlist
const GUEST_WISHLIST_KEY = 'shop-maimi-guest-wishlist';
const GUEST_CART_KEY = 'shop-maimi-guest-cart';

// Helper function to dispatch storage change event
const dispatchStorageEvent = () => {
  // This will trigger our custom event listener in the Header component
  window.dispatchEvent(new Event('localStorageChange'));
};

// console.log("[DEBUG] Wishlist: Component initialized");

// Helper function to get guest wishlist items from localStorage
const getGuestWishlistItems = (): GuestWishlistItem[] => {
  try {
    const storedWishlist = localStorage.getItem(GUEST_WISHLIST_KEY);
    if (!storedWishlist) {
      console.log("[DEBUG] Wishlist: No items in localStorage");
      return [];
    }
    
    const parsedItems = JSON.parse(storedWishlist);
    console.log("[DEBUG] Wishlist: Parsed items from localStorage -", parsedItems.length);
    return parsedItems;
  } catch (error) {
    console.error('[DEBUG] Wishlist: Error getting guest wishlist from localStorage:', error);
    return [];
  }
};

// Helper function to save guest wishlist to localStorage
const saveGuestWishlist = (items: GuestWishlistItem[]): boolean => {
  try {
    console.log("[DEBUG] Wishlist: Saving items to localStorage -", items.length);
    localStorage.setItem(GUEST_WISHLIST_KEY, JSON.stringify(items));
    // Notify header to update wishlist count
    dispatchStorageEvent();
    return true;
  } catch (error) {
    console.error('[DEBUG] Wishlist: Error saving guest wishlist to localStorage:', error);
    return false;
  }
};

export default function Wishlist() {
  const router = useRouter();
  
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [movingToCart, setMovingToCart] = useState<string | null>(null);
  const [removingItem, setRemovingItem] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  
  useEffect(() => {
    const initializeWishlist = async () => {
      try {
        setLoading(true);
        console.log("[DEBUG] Wishlist: Initializing wishlist");
 
        // Check if user is authenticated
        const supabase = createClientComponentClient();
        const { data: { session } } = await supabase.auth.getSession();
        const isUserAuthenticated = !!session?.user;
        setIsAuthenticated(isUserAuthenticated);
 
        if (isUserAuthenticated) {
          // Migrate guest wishlist to database
          const migrateGuestWishlist = async (userId: string) => {
            const guestWishlist = getGuestWishlistItems();
 
            if (guestWishlist.length === 0) return;
 
            const { error } = await supabase
              .from('wishlists')
              .insert(
                guestWishlist.map(item => ({
                  user_id: userId,
                  product_id: item.product_id,
                }))
              );
 
            if (error) {
              console.error('[DEBUG] Wishlist: Failed to migrate guest wishlist:', error);
              return;
            }
 
            console.log('[DEBUG] Wishlist: Migrated guest wishlist to database');
            localStorage.removeItem(GUEST_WISHLIST_KEY);
            dispatchStorageEvent();
          };
 
          await migrateGuestWishlist(session.user.id);
 
          console.log("[DEBUG] Wishlist: User is authenticated, fetching from database");
          const { getWishlistItems } = await import('@/lib/wardrobe');
          const dbItems = await getWishlistItems(session.user.id, {
            select: 'id, product_id, created_at, product:products(id, name, slug, price, product_media(url, is_main))',
          });
          console.log("[DEBUG] Wishlist: Retrieved database items -", dbItems.length);
 
          const typedItems = dbItems as unknown as DatabaseWishlistItem[];
 
          setWishlistItems(typedItems.map((item: DatabaseWishlistItem) => ({
            id: item.id,
            product_id: item.product_id,
            created_at: item.created_at || '',
            product: {
              id: item.product?.id || '',
              name: item.product?.name || '',
              slug: item.product?.slug || '',
              price: item.product?.price || 0,
              product_media: item.product?.product_media || [],
            },
          })));
        } else {
          // For guests: Fetch product data from Supabase using product_id in localStorage
          console.log("[DEBUG] Wishlist: User is not authenticated, using localStorage");
 
          const guestWishlistItems = getGuestWishlistItems();
          console.log("[DEBUG] Wishlist: Retrieved localStorage items -", guestWishlistItems.length);
 
          if (guestWishlistItems.length === 0) {
            setWishlistItems([]);
            return;
          }
 
          const productIds = guestWishlistItems.map((item) => item.product_id);
 
          const { data: products, error } = await supabase
            .from('products')
            .select('id, name, slug, price, product_media(url, is_main)')
            .in('id', productIds);
 
          if (error) {
            console.error('[DEBUG] Wishlist: Failed to fetch products for guest:', error);
            setWishlistItems([]);
            return;
          }
 
          const mappedItems: WishlistItem[] = guestWishlistItems.map((item) => {
            const product = products?.find((p) => p.id === item.product_id);
            return {
              id: item.id,
              product_id: item.product_id,
              created_at: item.created_at,
              product: {
                id: product?.id || '',
                name: product?.name || '',
                slug: product?.slug || '',
                price: product?.price || 0,
                product_media: product?.product_media || [],
              },
            };
          });
 
          setWishlistItems(mappedItems);
        }
      } catch (err) {
        console.error('[DEBUG] Wishlist: Error initializing wishlist:', err);
        customToast.error('Failed to load wishlist');
      } finally {
        setLoading(false);
      }
    };

    initializeWishlist();
  }, []);
  
  const handleShare = async () => {
    try {
      if (!window.navigator.clipboard) {
        customToast.error('Your browser does not support copying to clipboard');
        return;
      }
      
      // Sharing requires auth, but we're in guest mode
      customToast.error('You need to sign in to share your wishlist');
      return;
    } catch (error: any) {
      console.error('[DEBUG] Wishlist: Error sharing wishlist:', error);
      customToast.error(error.message || 'Failed to share wishlist');
    }
  };

  const handleRemoveFromWishlist = async (itemId: string) => {
    try {
      setRemovingItem(itemId);
      const removeToastId = toast.loading('Removing from wishlist...');
      
      if (isAuthenticated) {
        console.log("[DEBUG] Wishlist: Removing item from database:", itemId);
        // For authenticated users, remove from database
        const supabase = createClientComponentClient();
        const { error } = await supabase
          .from('wishlists')
          .delete()
          .eq('id', itemId);
          
        if (error) {
          console.error("[DEBUG] Wishlist: Error removing item from database:", error);
          toast.dismiss(removeToastId);
          customToast.error('Failed to remove item from wishlist');
          return;
        }
      }
      
      // For all users, update local state
      const updatedItems = wishlistItems.filter(item => item.id !== itemId);
      setWishlistItems(updatedItems);
      
      // For guest users, also update localStorage
      if (!isAuthenticated) {
        console.log("[DEBUG] Wishlist: Removing item from localStorage:", itemId);
        const saveResult = saveGuestWishlist(updatedItems as GuestWishlistItem[]);
        
        if (!saveResult) {
          toast.dismiss(removeToastId);
          customToast.error('Failed to update wishlist');
          return;
        }
      }
      
      toast.dismiss(removeToastId);
      customToast.success('Item removed from wishlist');
    } catch (error) {
      console.error('[DEBUG] Wishlist: Error removing from wishlist:', error);
      customToast.error('Failed to remove item from wishlist');
    } finally {
      setRemovingItem(null);
    }
  };

  const handleAddToCart = async (item: WishlistItem) => {
    // Prevent adding to cart if already in progress
    if (movingToCart) return;
    
    try {
      setMovingToCart(item.id);
      console.log("[DEBUG] Wishlist: Moving to cart -", item.id);
      
      // Show a loading toast
      const cartToastId = `add-to-cart-${item.id}`;
      toast.loading('Adding to cart...', { id: cartToastId, duration: 2000 });
      
      // Always handle client-side with localStorage
      // Generate a unique ID for the cart item
      const cartItemId = `cart-${Date.now()}-${item.product_id}`;
      
      // Get current cart items
      const storedCart = localStorage.getItem(GUEST_CART_KEY);
      let cartItems = [];
      try {
        if (storedCart) {
          cartItems = JSON.parse(storedCart);
        }
      } catch (e) {
        console.error('[DEBUG] Wishlist: Error parsing cart -', e);
      }
      
      console.log("[DEBUG] Wishlist: Current cart items -", cartItems.length);
      
      // Check if the product is already in the cart
      const existingItem = cartItems.find((i: any) => i.product_id === item.product_id);
      
      if (existingItem) {
        // Increment quantity
        existingItem.quantity += 1;
        console.log("[DEBUG] Wishlist: Incrementing existing cart item quantity");
      } else {
        // Add new item
        cartItems.push({
          id: cartItemId,
          product_id: item.product_id,
          quantity: 1,
          added_at: new Date().toISOString(),
          product: item.product
        });
        console.log("[DEBUG] Wishlist: Added new item to cart");
      }
      
      // Save to localStorage
      try {
        localStorage.setItem(GUEST_CART_KEY, JSON.stringify(cartItems));
        console.log("[DEBUG] Wishlist: Saved updated cart to localStorage");
        toast.dismiss(cartToastId);
        customToast.cart('Item added to cart');
        
        // Remove from wishlist after adding to cart
        console.log("[DEBUG] Wishlist: Removing item from wishlist after adding to cart");
        handleRemoveFromWishlist(item.id);
      } catch (e) {
        console.error('[DEBUG] Wishlist: Error saving cart -', e);
        toast.dismiss(cartToastId);
        customToast.error('Failed to add to cart');
      }
    } catch (error: any) {
      console.error('[DEBUG] Wishlist: Error adding to cart:', error);
      toast.dismiss(`add-to-cart-${item.id}`);
      customToast.error('Failed to add item to cart');
    } finally {
      setMovingToCart(null);
    }
  };

  // Show loading state while loading
  if (loading) {
    return (
      <div className="py-20 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900 mb-4"></div>
        <p>Loading your wishlist...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-12 bg-[#f8f8f8]">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-8">
          <div>
            <h1 className="text-3xl font-heading tracking-wider text-[#171717]">My Wishlist</h1>
            <p className="mt-2 text-gray-600">Save items for later and share with friends</p>
          </div>
          {wishlistItems.length > 0 && (
            <button
              onClick={handleShare}
              className="px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] transition-colors"
            >
              Share Wishlist
            </button>
          )}
        </div>

        {wishlistItems.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            <h2 className="text-xl font-heading mb-2">Your wishlist is empty</h2>
            <p className="text-gray-600 mb-6">Browse our products and add some items to your wishlist</p>
            <Link 
              href="/products" 
              className="inline-block px-6 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] transition-colors"
            >
              Browse Products
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {wishlistItems.map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="bg-white rounded-xl shadow-sm overflow-hidden"
              >
                <Link href={`/products/${item.product.slug}`}>
                  <div className="relative h-48 w-full overflow-hidden rounded-lg bg-gray-100">
                    {getProductImageUrl(item.product.product_media) ? (
                      <Image
                        src={getCloudinaryUrl(getProductImageUrl(item.product.product_media))}
                        alt={item.product.name}
                        width={400}
                        height={200}
                        className="h-full w-full object-cover object-center"
                        placeholder="blur"
                        blurDataURL="/blur-placeholder.png"
                      />
                    ) : (
                      <div className="h-full w-full flex items-center justify-center text-gray-400">
                        Sorry, we are constructing the image &nbsp;:)
                      </div>
                    )}
                  </div>
                </Link>
                <div className="p-4">
                  <Link href={`/products/${item.product.slug}`}>
                    <h3 className="text-lg font-medium text-[#171717] hover:text-[#333333] transition-colors">
                      {item.product.name}
                    </h3>
                  </Link>
                  <p className="mt-1 text-lg font-semibold text-[#171717]">
                    ${item.product.price.toFixed(2)}
                  </p>
                  <div className="mt-4 flex justify-between gap-2">
                    <button
                      onClick={() => handleAddToCart(item)}
                      disabled={movingToCart === item.id}
                      className="flex-1 px-4 py-2 bg-[#171717] text-white rounded-md hover:bg-[#333333] transition-colors disabled:opacity-50"
                    >
                      {movingToCart === item.id ? 'Adding...' : 'Add to Cart'}
                    </button>
                    <button
                      onClick={() => handleRemoveFromWishlist(item.id)}
                      disabled={removingItem === item.id}
                      className="px-4 py-2 text-red-600 hover:text-red-800 transition-colors disabled:opacity-50"
                    >
                      {removingItem === item.id ? 'Removing...' : 'Remove'}
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
