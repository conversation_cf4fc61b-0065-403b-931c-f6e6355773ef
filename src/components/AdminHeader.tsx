'use client';

import { memo, useState } from 'react';
import NotificationBell from '@/components/admin/notifications/NotificationBell';
import NotificationPanel from '@/components/admin/notifications/NotificationPanel';

// Memoize the component to prevent unnecessary re-renders
const AdminHeader = memo(function AdminHeader() {
  const [isNotificationPanelOpen, setIsNotificationPanelOpen] = useState(false);

  return (
    <>
      <div className="fixed top-4 right-4 z-50">
        <NotificationBell onNotificationClick={() => setIsNotificationPanelOpen(true)} />
      </div>

      {/* Notification Panel */}
      <NotificationPanel
        isOpen={isNotificationPanelOpen}
        onClose={() => setIsNotificationPanelOpen(false)}
      />
    </>
  );
});

export default AdminHeader;
