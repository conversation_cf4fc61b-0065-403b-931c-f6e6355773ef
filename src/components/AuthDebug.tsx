import React from 'react';

import { useState } from 'react';
import { getUser, getSession, signOut } from '@/lib/auth';
import AuthRefreshButton from './AuthRefreshButton';

/**
 * Debug component for authentication status
 * Only shown in development mode
 */
export default function AuthDebug() {
  const [expanded, setExpanded] = useState(false);
  const [userData, setUserData] = useState<any>(null);
  const [sessionData, setSessionData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Function to refresh user data
  const refreshData = async () => {
    setLoading(true);
    try {
      const user = await getUser();
      const session = await getSession();
      
      setUserData(user);
      setSessionData(session);
    } catch (error) {
      console.error('Error refreshing auth data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initial data load
  React.useEffect(() => {
    refreshData();
  }, []);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-md">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-bold">Auth Debug</h3>
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-sm bg-gray-700 px-2 py-1 rounded hover:bg-gray-600"
        >
          {expanded ? 'Hide' : 'Show'}
        </button>
      </div>
      
      {expanded && (
        <div className="mt-4 space-y-4">
          <div>
            <h4 className="font-semibold">User</h4>
            <pre className="text-xs bg-gray-900 p-2 rounded mt-1 overflow-auto max-h-40">
              {JSON.stringify(userData, null, 2)}
            </pre>
          </div>
          
          <div>
            <h4 className="font-semibold">Session</h4>
            <pre className="text-xs bg-gray-900 p-2 rounded mt-1 overflow-auto max-h-40">
              {JSON.stringify(sessionData, null, 2)}
            </pre>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={refreshData}
              disabled={loading}
              className="text-sm bg-blue-600 px-3 py-1 rounded hover:bg-blue-500 disabled:opacity-50"
            >
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
            
            <button
              onClick={() => signOut()}
              className="text-sm bg-red-600 px-3 py-1 rounded hover:bg-red-500"
            >
              Sign Out
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
