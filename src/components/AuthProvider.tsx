'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import type { Session, User } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@/lib/supabase-browser';

interface AuthContextProps {
  session: Session | null;
  user: User | null;
  isLoading: boolean;
  supabase: ReturnType<typeof createClient>;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuth must be used within an AuthProvider');
  return context;
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastSessionFetch, setLastSessionFetch] = useState(0);
  const supabase = createClient();
  
  // Rate limiting: minimum 5 seconds between session fetches to prevent rate limits
  const SESSION_FETCH_THROTTLE = 5000;

  useEffect(() => {
    let mounted = true;

    const getSession = async () => {
      try {
        if (!supabase) return;
        
        // Rate limiting: prevent too frequent session fetches
        const now = Date.now();
        if (now - lastSessionFetch < SESSION_FETCH_THROTTLE) {
          console.log('[AuthProvider] Session fetch throttled');
          if (mounted) setIsLoading(false);
          return;
        }
        
        setLastSessionFetch(now);
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error('[AuthProvider] Session fetch error:', error);
          return;
        }

        if (!mounted) return;
        setSession(data.session);
        setUser(data.session?.user ?? null);
      } catch (err) {
        console.error('[AuthProvider] Error:', err);
      } finally {
        if (mounted) {
          // Reduced delay for faster loading
          setTimeout(() => setIsLoading(false), 200);
        }
      }
    };

    const listener = supabase ? supabase.auth.onAuthStateChange((event: any, session: any) => {
      if (!mounted) return;
      
      console.log('[AuthProvider] Auth state change:', event);

      // Optimize state updates to reduce re-renders and prevent rapid requests
      if (event === 'SIGNED_IN') {
        // Use the session from the event instead of fetching again
        setSession(session);
        setUser(session?.user ?? null);
        setIsLoading(false);
      } else if (event === 'SIGNED_OUT') {
        setSession(null);
        setUser(null);
        setIsLoading(false);
      } else if (event === 'TOKEN_REFRESHED') {
        // Don't reload on token refresh to prevent rate limits
        setSession(session);
        setUser(session?.user ?? null);
      } else {
        setSession(session);
        setUser(session?.user ?? null);
        setIsLoading(false);
      }
    }) : null;

    getSession();

    return () => {
      mounted = false;
      listener?.data.subscription.unsubscribe();
    };
  }, []); // Remove supabase dependency to prevent unnecessary re-runs

  return (
    <AuthContext.Provider value={{ session, user, isLoading, supabase }}>
      {children}
    </AuthContext.Provider>
  );
};