import React, { useState } from 'react';
import { getSession } from '@/lib/auth';

type AuthRefreshButtonProps = {
  variant?: 'default' | 'outline' | 'link';
  size?: 'default' | 'sm' | 'lg';
  label?: string;
  className?: string;
};

/**
 * Button to refresh the auth session
 */
export default function AuthRefreshButton({
  variant = 'default',
  size = 'default',
  label = 'Refresh Session',
  className = '',
}: AuthRefreshButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  
  // Get base styles based on variant and size
  const getBaseStyles = () => {
    // Variant styles
    const variantStyles = {
      default: 'bg-blue-600 hover:bg-blue-700 text-white',
      outline: 'border border-blue-600 text-blue-600 hover:bg-blue-50',
      link: 'text-blue-600 hover:underline',
    };
    
    // Size styles
    const sizeStyles = {
      default: 'px-4 py-2',
      sm: 'px-2 py-1 text-sm',
      lg: 'px-6 py-3 text-lg',
    };
    
    return `${variantStyles[variant]} ${sizeStyles[size]} rounded transition-colors`;
  };
  
  // Handle refresh click
  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      // Refresh the session
      const session = await getSession();
      console.log('Session refreshed:', session);
      
      // Optional: Reload the page to ensure all components have the latest session
      // window.location.reload();
    } catch (error) {
      console.error('Error refreshing session:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <button
      onClick={handleRefresh}
      disabled={isLoading}
      className={`${getBaseStyles()} ${className} ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
    >
      {isLoading ? 'Refreshing...' : label}
    </button>
  );
}
