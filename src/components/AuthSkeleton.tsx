'use client';

import React from 'react';

/**
 * A skeleton loader component that displays while authentication is being checked
 * This prevents the flash of login page before redirecting to protected content
 */
export default function AuthSkeleton() {
  return (
    <div className="min-h-screen pt-20 flex items-center justify-center bg-[#f8f8f8]">
      <div className="w-full max-w-lg p-8 bg-white rounded-xl shadow-sm">
        <div className="flex justify-center mb-8">
          <div className="h-6 w-36 bg-gray-200 rounded animate-pulse"></div>
        </div>
        
        <div className="h-4 w-3/4 mx-auto bg-gray-200 rounded animate-pulse mb-2"></div>
        <div className="h-4 w-2/3 mx-auto bg-gray-200 rounded animate-pulse mb-12"></div>
        
        <div className="space-y-4">
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        </div>
        
        <div className="mt-8 flex justify-center gap-4">
          <div className="h-5 w-24 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-5 w-24 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}
