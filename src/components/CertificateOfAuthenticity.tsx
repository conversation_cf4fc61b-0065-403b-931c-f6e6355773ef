'use client';

import { useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import QRCode from 'react-qr-code';

interface CertificateProps {
  productId: string;
  productName: string;
  serialNumber: string;
  issueDate: string;
  description?: string;
  condition?: string;
  imageUrl?: string;
}

export default function CertificateOfAuthenticity({
  productId,
  productName,
  serialNumber,
  issueDate,
  description,
  condition,
  imageUrl,
}: CertificateProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadError, setDownloadError] = useState(false);
  
  // Use the production URL in production environment
  const verificationUrl = typeof window !== 'undefined' 
    ? `${window.location.origin}/verify/${productId}`
    : `${process.env.NEXT_PUBLIC_SITE_URL || 'https://treasuresofmaimi.com'}/verify/${productId}`;

  const handleDownload = async () => {
    try {
      setIsDownloading(true);
      setDownloadError(false);
      
      // Get the full URL to the API endpoint
      const apiUrl = typeof window !== 'undefined'
        ? `${window.location.origin}/api/generate-certificate/${productId}`
        : `/api/generate-certificate/${productId}`;
      
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        throw new Error(`Failed to generate certificate: ${response.status} ${response.statusText}`);
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      // Create an anchor tag and simulate a click
      const a = document.createElement('a');
      a.href = url;
      a.download = `treasure-of-maimi-certificate-${serialNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }, 100);
      
    } catch (error) {
      console.error('Error downloading certificate:', error);
      setDownloadError(true);
      setTimeout(() => setDownloadError(false), 3000);
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <div className="w-full">
      {/* Toggle Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between px-6 py-4 bg-[#f5f5f5] hover:bg-[#eaeaea] transition-colors duration-300 rounded-lg"
      >
        <div className="flex items-center space-x-3">
          <svg
            className={`w-5 h-5 transform transition-transform duration-300 ${
              isExpanded ? 'rotate-180' : ''
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 9l-7 7-7-7"
            />
          </svg>
          <span className="font-heading text-lg">Certificate of Authenticity</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-[#666666]">
            {isExpanded ? 'Hide Details' : 'View Details'}
          </span>
        </div>
      </button>

      {/* Certificate Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="mt-6 p-8 border border-[#e5e5e5] rounded-lg bg-white">
              {/* Header with Welcome Card */}
              <div className="text-center mb-8">
                <div className="relative w-48 h-48 mx-auto mb-6">
                  <Image
                    src="/images/welcome-card.png"
                    alt="Welcome to Treasure of Maimi"
                    fill
                    className="object-contain"
                  />
                </div>
                <h2 className="font-heading text-3xl mb-2">Treasure of Maimi</h2>
                <p className="text-[#666666] text-sm">Certificate of Authenticity</p>
                <p className="mt-4 text-[#666666] italic">
                  Thank you for your purchase. This certificate verifies the authenticity of your luxury item.
                </p>
              </div>

              {/* Product Image */}
              {imageUrl && (
                <div className="relative w-48 h-48 mx-auto mb-8 rounded-lg overflow-hidden">
                  <Image
                    src={imageUrl}
                    alt={productName}
                    fill
                    className="object-cover"
                  />
                </div>
              )}

              {/* Product Details */}
              <div className="space-y-4 mb-8">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm text-[#666666]">Product Name</h3>
                    <p className="font-heading text-lg">{productName}</p>
                  </div>
                  <div>
                    <h3 className="text-sm text-[#666666]">Serial Number</h3>
                    <p className="font-heading text-lg">{serialNumber}</p>
                  </div>
                  <div>
                    <h3 className="text-sm text-[#666666]">Issue Date</h3>
                    <p className="font-heading text-lg">{issueDate}</p>
                  </div>
                  {condition && (
                    <div>
                      <h3 className="text-sm text-[#666666]">Condition</h3>
                      <p className="font-heading text-lg">{condition}</p>
                    </div>
                  )}
                </div>
                {description && (
                  <div>
                    <h3 className="text-sm text-[#666666]">Description</h3>
                    <p className="mt-1 text-[#171717]">{description}</p>
                  </div>
                )}
              </div>

              {/* Verification QR Code */}
              <div className="flex flex-col items-center justify-center p-4 bg-[#f5f5f5] rounded-lg">
                <div className="bg-white p-4 rounded-lg mb-4">
                  <QRCode value={verificationUrl} size={120} />
                </div>
                <p className="text-sm text-[#666666] text-center">
                  Scan to verify authenticity or visit:
                  <br />
                  <a
                    href={verificationUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#171717] hover:underline"
                  >
                    {verificationUrl}
                  </a>
                </p>
              </div>

              {/* Download Button */}
              <div className="mt-8 flex flex-col items-center justify-center">
                <button
                  onClick={handleDownload}
                  disabled={isDownloading}
                  className={`px-6 py-3 bg-[#171717] text-white rounded-lg transition-all duration-300 ${
                    isDownloading 
                      ? 'opacity-70 cursor-not-allowed' 
                      : 'hover:bg-[#2c2c2c]'
                  }`}
                >
                  {isDownloading ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                      </svg>
                      Preparing Certificate...
                    </span>
                  ) : (
                    'Download Certificate'
                  )}
                </button>
                {downloadError && (
                  <p className="mt-2 text-red-500 text-sm">
                    Unable to generate certificate. Please try again.
                  </p>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
