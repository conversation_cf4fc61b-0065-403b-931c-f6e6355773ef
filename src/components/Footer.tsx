import Link from 'next/link';
import { Instagram } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#171717] text-white pt-16 pb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 pb-12 border-b border-white/10">
          <div className="md:col-span-2">
            <h3 className="font-heading text-2xl mb-4">The Treasure of Maimi</h3>
            <p className="font-body text-white/70 mb-6 max-w-md">
              Discover curated vintage luxury fashion pieces. Each item tells a unique story 
              of elegance and sustainability.
            </p>
            <div className="flex space-x-4">
              <a 
                href="https://instagram.com/treasuresof.maimi" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-white/70 hover:text-white transition-colors duration-300"
              >
                <Instagram size={24} />
              </a>
            </div>
          </div>

          <div>
            <h4 className="font-heading text-lg mb-4">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/about" className="font-body text-white/70 hover:text-white transition-colors duration-300">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/collection" className="font-body text-white/70 hover:text-white transition-colors duration-300">
                  Collection
                </Link>
              </li>
              <li>
                <Link href="/contact" className="font-body text-white/70 hover:text-white transition-colors duration-300">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/faq" className="font-body text-white/70 hover:text-white transition-colors duration-300">
                  FAQ
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-heading text-lg mb-4">Legal</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/privacy-policy" className="font-body text-white/70 hover:text-white transition-colors duration-300">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms-of-service" className="font-body text-white/70 hover:text-white transition-colors duration-300">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/shipping-policy" className="font-body text-white/70 hover:text-white transition-colors duration-300">
                  Shipping Policy
                </Link>
              </li>
              <li>
                <Link href="/refund-policy" className="font-body text-white/70 hover:text-white transition-colors duration-300">
                  Refund Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="pt-8 text-center">
          <p className="font-body text-white/70 text-sm">
            © {currentYear} The Treasure of Maimi. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
