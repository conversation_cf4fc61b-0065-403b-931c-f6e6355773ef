'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { usePathname, useRouter } from 'next/navigation'
import { ShoppingBag, Heart, Menu, X, ChevronDown, User } from 'lucide-react'
import type { Database } from '@/lib/database.types'
import { useAuth } from '@/components/AuthProvider'
import { useWishlist } from '@/context/WishlistContext'

type CategoryRow = Database['public']['Tables']['categories']['Row'];

interface Category extends CategoryRow {}

interface Announcement {
  text: string
}

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
const [featuredCategories, setFeaturedCategories] = useState<Category[]>([])
const [featuredProducts, setFeaturedProducts] = useState<any[]>([])
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [timeRemaining, setTimeRemaining] = useState(5)
  const [currentAnnouncementIndex, setCurrentAnnouncementIndex] = useState(0)
  const [cartItemCount, setCartItemCount] = useState(0)
  const [wishlistItemCount, setWishlistItemCount] = useState(0)

  const pathname = usePathname()
  const router = useRouter()
  const [isAdmin, setIsAdmin] = useState(false)
  const { session, user, isLoading, supabase } = useAuth()
  const { wishlist } = useWishlist()

  // Check admin status when session changes (optimized with caching)
  useEffect(() => {
    if (isLoading || !user) {
      setIsAdmin(false);
      return;
    }

    let mounted = true;

    const checkAdminStatus = async () => {
      try {
        // Check if admin status is cached in session metadata first
        const cachedAdminStatus = user.user_metadata?.is_admin;
        if (cachedAdminStatus !== undefined) {
          if (mounted) {
            setIsAdmin(!!cachedAdminStatus);
          }
          return;
        }

        // Only query database if not cached
        if (!supabase) return;
        const { data: profileData, error } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Header: Error checking admin status:', error);
          return;
        }

        if (mounted) {
          const isUserAdmin = !!profileData?.is_admin;
          setIsAdmin(isUserAdmin);
        }
      } catch (err) {
        console.error('Header: Unexpected error checking admin status:', err);
      }
    };

    checkAdminStatus();

    return () => {
      mounted = false;
    };
  }, [user?.id, isLoading]); // Optimized dependencies

  // Rotating announcements
  const announcements = [
    "🔥 SELLING FAST! Limited pieces available",
    "🎁 Authentic luxury pieces with certificates",
    "✨ Exclusive vintage finds you won't find elsewhere",
    "🛍️ New treasures added monthly"
  ];

  useEffect(() => {
    // Rotate through announcements
    const rotationInterval = setInterval(() => {
      setCurrentAnnouncementIndex(prev => (prev + 1) % announcements.length);
    }, 5000); // Change every 5 seconds
    
    return () => clearInterval(rotationInterval);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Fetch categories and featured products with shared client
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        if (!supabase) return;
        const { data, error } = await supabase.from('categories').select('*');
        if (error) {
          console.error('Error loading categories:', error);
          setCategories([]);
        } else {
          setCategories(data || []);
          setFeaturedCategories(data.slice(0, 3) || []);
        }
      } catch (err) {
        console.error('Error loading categories:', err);
        setCategories([]);
      }
    };

    const fetchFeaturedProducts = async () => {
      try {
        if (!supabase) return;
        const { data, error } = await supabase
          .from('products')
          .select(`
            id,
            name,
            slug,
            product_media (id, url, is_main, position),
            product_views (id)
          `);

        if (error) {
          console.error('Error loading featured products:', error);
          // Set empty array to prevent infinite loading
          setFeaturedProducts([]);
        } else if (data) {
          // Calculate view counts properly
          const productsWithViewCount = data.map((product: any) => ({
            ...product,
            viewCount: product.product_views ? product.product_views.length : 0,
          }));

          // Sort and slice for top viewed products
          const sorted = productsWithViewCount
            .sort((a: any, b: any) => b.viewCount - a.viewCount)
            .slice(0, 2); // Limit to 2 products

          setFeaturedProducts(sorted);
        }
      } catch (err) {
        console.error('Error loading featured products:', err);
        // Set empty array to prevent infinite loading
        setFeaturedProducts([]);
      }
    };

    fetchCategories();
    fetchFeaturedProducts();
  }, [supabase])

  // Constants for localStorage keys
  const GUEST_CART_KEY = 'shop-maimi-guest-cart'
  
  // Function to update cart and wishlist counts from localStorage
  const updateItemCounts = useCallback(() => {
    // Update cart count
    try {
      const storedCart = localStorage.getItem(GUEST_CART_KEY)
      if (storedCart) {
        const cartItems = JSON.parse(storedCart)
        setCartItemCount(cartItems.length)
      } else {
        setCartItemCount(0)
      }
    } catch (error) {
      console.error('Error updating item counts:', error)
    }
  }, [])
  
  // Listen for storage events to update counts when changed from other tabs/pages
  useEffect(() => {
    // Initial count update
    updateItemCounts()
    
    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === GUEST_CART_KEY) {
        updateItemCounts()
      }
    }
    
    window.addEventListener('storage', handleStorageChange)
    
    // Custom event for same-tab updates
    const handleCustomStorageChange = () => updateItemCounts()
    window.addEventListener('localStorageChange', handleCustomStorageChange)
    
    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('localStorageChange', handleCustomStorageChange)
    }
  }, [updateItemCounts])

  // Update wishlist count when wishlist context changes
  useEffect(() => {
    if (wishlist && wishlist.items) {
      setWishlistItemCount(wishlist.items.length);
    }
  }, [wishlist]);

  // Function to handle profile/auth navigation
  const handleUserIconClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent default link behavior
    
    // Check if auth is still loading
    if (isLoading) {
      console.log('[Header] Auth still loading, skipping navigation');
      return;
    }
    
    if (session && user) {
      console.log('[Header] User authenticated, navigating to profile', { isAdmin });
      // If user is authenticated, direct to admin or account based on role
      if (isAdmin) {
        router.push('/admin');
      } else {
        router.push('/account');
      }
    } else {
      console.log('[Header] No session/user, navigating to login');
      // If not authenticated, direct to login page
      router.push('/auth/login');
    }
  };

  return (
    <>
      {/* Dynamic Announcement Bar */}
      <div className={`relative overflow-hidden transition-all duration-500 ${
        isScrolled ? 'h-0' : 'h-10'
      }`}>
        <div className="bg-[#171717] text-white relative overflow-hidden">
          {/* Background animation */}
          <motion.div 
            className="absolute inset-0 bg-gradient-to-r from-[#171717] via-[#333333] to-[#171717]"
            initial={{ x: '-100%' }}
            animate={{ x: '100%' }}
            transition={{ 
              duration: 15, 
              repeat: Infinity,
              ease: "linear" 
            }}
          />
          
          <div className="relative h-9">
            <AnimatePresence mode="wait">
              <motion.div 
                key={currentAnnouncementIndex}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-x-0 h-full flex items-center justify-center"
              >
                <motion.p 
                  className="text-sm font-body tracking-[0.15em] flex items-center"
                  animate={{ 
                    scale: [1, 1.02, 1],
                    textShadow: ["0 0 0px rgba(255,255,255,0)", "0 0 5px rgba(255,255,255,0.5)", "0 0 0px rgba(255,255,255,0)"]
                  }}
                  transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                >
                  <motion.span 
                    className="inline-block mr-2"
                    animate={{ rotate: [0, 10, 0, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    {announcements[currentAnnouncementIndex].split(' ')[0]}
                  </motion.span>
                  {announcements[currentAnnouncementIndex].split(' ').slice(1).join(' ')}
                </motion.p>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header 
        className={`fixed w-full z-50 transition-all duration-500 ${
          isScrolled ? 'bg-white/70 backdrop-blur-lg backdrop-saturate-150' : 'bg-[#171717]'
        }`}
      >
        <div className="container-custom">
          <nav className="flex items-center justify-between py-12">
            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(true)}
              className={`lg:hidden ${isScrolled ? 'text-[#171717]' : 'text-white'}`}
              aria-label="Open menu"
            >
              <Menu size={24} />
            </button>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-12">
              <div 
                className="relative group"
                onMouseEnter={() => setIsDropdownOpen(true)}
                onMouseLeave={() => setIsDropdownOpen(false)}
              >
                <button className={`flex items-center space-x-1.5 font-body tracking-[0.2em] ${
                  isScrolled ? 'text-[#171717]' : 'text-white'
                } hover:opacity-70 transition-all duration-300`}>
                  <span className="text-lg">SHOP</span>
                  <ChevronDown size={16} className="group-hover:rotate-180 transition-transform duration-300" />
                </button>

                {/* Luxury Mega Menu */}
                <AnimatePresence>
                  {isDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.3 }}
                      className="absolute left-0 top-full pt-6"
                    >
                      <div className="bg-white shadow-xl w-[800px] p-8">
                        <div className="grid grid-cols-12 gap-8">
                          {/* Categories */}
                          <div className="col-span-4">
                            <h3 className="font-heading text-sm tracking-[0.2em] mb-6">CATEGORIES</h3>
                            <div className="space-y-4">
                              {categories.map((category) => (
                                <Link
                                  key={category.id}
                                  href={`/category/${category.slug}`}
                                  className="block font-body tracking-wider text-lg text-[#171717] hover:opacity-70 transition-all duration-300"
                                >
                                  {category.name}
                                </Link>
                              ))}
                            </div>
                          </div>

                          {/* Featured Products */}
                          <div className="col-span-8">
                            <h3 className="font-heading text-sm tracking-[0.2em] mb-6">FEATURED</h3>
                            <div className="grid grid-cols-2 gap-6">
                              {featuredProducts.map((product) => (
                                <Link
                                  key={product.id}
                                  href={`/products/${product.slug}`}
                                  className="group relative aspect-[4/5] overflow-hidden"
                                >
                              {(() => {
                                const secondImage = product.product_media?.find((m: any) => m.position === 2) || product.product_media?.[1];
                                if (!secondImage) return null;
                                return (
                                  <>
                                    <Image
                                      src={secondImage.url}
                                      alt={product.name}
                                      fill
                                      sizes="(max-width: 768px) 100vw, 400px"
                                      className="object-cover transition-transform duration-700 group-hover:scale-110"
                                    />
                                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300" />
                                    <div className="absolute inset-0 flex items-center justify-center">
                                      <span className="text-white font-heading tracking-[0.2em] text-lg">
                                        {product.name}
                                      </span>
                                    </div>
                                  </>
                                );
                              })()}
                                </Link>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              <Link
                href="/new-arrivals"
                className={`font-body tracking-[0.2em] text-lg ${
                  isScrolled ? 'text-[#171717]' : 'text-white'
                } hover:opacity-70 transition-all duration-300`}
              >
                NEW ARRIVALS
              </Link>

              <Link
                href="/collection"
                className={`font-body tracking-[0.2em] text-lg ${
                  isScrolled ? 'text-[#171717]' : 'text-white'
                } hover:opacity-70 transition-all duration-300`}
              >
                COLLECTION
              </Link>
            </div>

            {/* Logo */}
            <Link href="/" className="absolute left-1/2 transform -translate-x-1/2">
              <div className="relative w-48 h-20">
                <Image
                  src="/images/logo.png"
                  alt="Mai Mi"
                  fill
                  priority
                  sizes="(max-width: 768px) 120px, 192px"
                  className="object-contain transition-all duration-500 scale-110"
                  style={{ 
                    filter: isScrolled ? 'none' : 'brightness(0) invert(1)'
                  }}
                />
              </div>
            </Link>

            {/* Right Navigation */}
            <div className="flex items-center space-x-8">
              <Link
                href="/about"
                className={`hidden lg:inline-block font-body tracking-[0.2em] text-sm ${
                  isScrolled ? 'text-[#171717]' : 'text-white'
                } hover:opacity-70 transition-all duration-300`}
              >
                ABOUT
              </Link>
              <Link
                href="/contact"
                className={`hidden lg:inline-block font-body tracking-[0.2em] text-sm ${
                  isScrolled ? 'text-[#171717]' : 'text-white'
                } hover:opacity-70 transition-all duration-300`}
              >
                CONTACT
              </Link>
              <button 
                onClick={handleUserIconClick}
                className={`hover:opacity-70 transition-all duration-300 relative group ${
                  isScrolled ? 'text-[#171717]' : 'text-white'
                }`}
                aria-label={session && user ? "My Profile" : "Sign In"}
              >
                <User size={24} />
                <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-[#171717] text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                  {session && user ? "My Profile" : "Sign In"}
                </span>
              </button>



              <Link
                href="/wishlist"
                className={`hover:opacity-70 transition-all duration-300 relative ${
                  isScrolled ? 'text-[#171717]' : 'text-white'
                }`}
                aria-label="Wishlist"
              >
                <div className="relative">
                  <Heart size={24} />
                  {wishlistItemCount > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                      {wishlistItemCount}
                    </span>
                  )}
                </div>
                <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-[#171717] text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                  Wishlist
                </span>
              </Link>
              <Link
                href="/cart"
                className={`hover:opacity-70 transition-all duration-300 relative ${
                  isScrolled ? 'text-[#171717]' : 'text-white'
                }`}
                aria-label="Shopping cart"
              >
                <ShoppingBag size={24} />
                {cartItemCount > 0 && (
                  <motion.span 
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                  >
                    {cartItemCount > 99 ? '99+' : cartItemCount}
                  </motion.span>
                )}
              </Link>
            </div>
          </nav>
        </div>
      </header>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 z-50 lg:hidden"
          >
            <div className="absolute inset-0 bg-black/30" onClick={() => setIsMobileMenuOpen(false)} />
            
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ duration: 0.4, ease: [0.32, 0.72, 0, 1] }}
              className="absolute inset-y-0 left-0 w-4/5 max-w-sm bg-white"
            >
              <div className="p-6">
                <div className="flex justify-between items-center mb-10">
                  <div className="relative w-24 h-8">
                    <Image
                      src="/images/logo.png"
                      alt="Mai Mi"
                      fill
                      sizes="96px"
                      className="object-contain"
                    />
                  </div>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="text-[#171717] hover:opacity-70 transition-opacity"
                    aria-label="Close menu"
                  >
                    <X size={24} />
                  </button>
                </div>

                <div className="space-y-8">
                  <div>
                    <h3 className="font-heading text-sm tracking-[0.2em] text-[#666666] mb-6">
                      CATEGORIES
                    </h3>
                    <div className="space-y-5">
                      {categories.map((category) => (
                        <Link
                          key={category.id}
                          href={`/category/${category.slug}`}
                          className="block font-body tracking-[0.15em] text-lg text-[#171717] hover:opacity-70 transition-all duration-300"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {category.name}
                        </Link>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-5">
                    <Link
                      href="/new-arrivals"
                      className="block font-body tracking-[0.15em] text-lg text-[#171717] hover:opacity-70 transition-all duration-300"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      NEW ARRIVALS
                    </Link>
                    <Link
                      href="/collection"
                      className="block font-body tracking-[0.15em] text-lg text-[#171717] hover:opacity-70 transition-all duration-300"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      COLLECTION
                    </Link>
                    <Link
                      href="/about"
                      className="block font-body tracking-[0.15em] text-lg text-[#171717] hover:opacity-70 transition-all duration-300"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      ABOUT
                    </Link>
                    <Link
                      href="/contact"
                      className="block font-body tracking-[0.15em] text-lg text-[#171717] hover:opacity-70 transition-all duration-300"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      CONTACT
                    </Link>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        setIsMobileMenuOpen(false);
                        
                        // Check if auth is still loading
                        if (isLoading) {
                          console.log('[Header Mobile] Auth still loading, skipping navigation');
                          return;
                        }
                        
                        if (session && user) {
                          console.log('[Header Mobile] User authenticated, navigating to profile', { isAdmin });
                          router.push(isAdmin ? '/admin' : '/account');
                        } else {
                          console.log('[Header Mobile] No session/user, navigating to login');
                          router.push('/auth/login');
                        }
                      }}
                      className="block font-body tracking-[0.15em] text-lg text-[#171717] hover:opacity-70 transition-all duration-300 text-left w-full"
                    >
                      {session && user ? 'MY PROFILE' : 'SIGN IN'}
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>


    </>
  )
}
