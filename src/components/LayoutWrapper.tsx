'use client';

import { usePathname } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { AuthProvider } from '@/components/AuthProvider';

export default function LayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isAdminPage = pathname?.startsWith('/admin');
  
  return (
    <AuthProvider>
      {!isAdminPage && <Header />}
      <main className={!isAdminPage ? "pt-24" : ""}>
        {children}
      </main>
      {!isAdminPage && <Footer />}
    </AuthProvider>
  );
}
