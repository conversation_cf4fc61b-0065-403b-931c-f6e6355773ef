'use client'

import { useEffect, useRef, useState } from 'react'

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'model-viewer': any
    }
  }
}

interface ModelViewerProps {
  src: string
  arEnabled?: boolean
  arUrl?: string
  onError?: () => void
}

export default function ModelViewer({ src, arEnabled = false, arUrl, onError }: ModelViewerProps) {
  const modelRef = useRef<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [isXRSupported, setIsXRSupported] = useState(false)

  useEffect(() => {
    // Check for XR support
    const checkXRSupport = async () => {
      try {
        if (typeof window !== 'undefined' && 'xr' in navigator) {
          const supported = await (navigator as any).xr?.isSessionSupported('immersive-ar');
          setIsXRSupported(!!supported);
        } else {
          setIsXRSupported(false);
        }
      } catch (e) {
        console.warn('Error checking XR support:', e);
        setIsXRSupported(false);
      }
    };

    checkXRSupport();

    // Load the model-viewer script dynamically
    const script = document.createElement('script')
    script.type = 'module'
    script.src = 'https://ajax.googleapis.com/ajax/libs/model-viewer/3.3.0/model-viewer.min.js'
    document.head.appendChild(script)

    // Reset states when src changes
    setLoading(true)
    setError(false)

    return () => {
      // Only remove if this is the last ModelViewer component
      const modelViewers = document.querySelectorAll('model-viewer')
      if (modelViewers.length <= 1) {
        try {
          document.head.removeChild(script)
        } catch (e) {
          // Script might have been removed already
        }
      }
    }
  }, [src])

  const handleLoad = () => {
    setLoading(false)
  }

  const handleError = () => {
    setLoading(false)
    setError(true)
    if (onError) onError()
  }

  return (
    <div className="relative w-full h-full">
      <model-viewer
        ref={modelRef}
        src={src}
        ar={arEnabled && isXRSupported}
        ar-modes={isXRSupported ? "webxr scene-viewer quick-look" : ""}
        ar-scale="fixed"
        camera-controls
        auto-rotate
        shadow-intensity="1"
        environment-image="neutral"
        exposure="1"
        style={{ width: '100%', height: '100%', backgroundColor: '#f5f5f5' }}
        {...(arUrl && { 'ios-src': arUrl })}
        poster={src.replace('.glb', '.jpg')} // Fallback image while loading
        onLoad={handleLoad}
        onError={handleError}
      >
        <div className="progress-bar hide" slot="progress-bar">
          <div className="update-bar"></div>
        </div>
        {isXRSupported && arEnabled && (
          <button slot="ar-button" className="ar-button bg-white/90 backdrop-blur-sm text-black px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 hover:bg-white transition-colors duration-300">
            View in your space
          </button>
        )}
      </model-viewer>

      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100/75">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#171717] mb-4"></div>
            <p className="text-sm text-gray-700">Loading 3D model...</p>
          </div>
        </div>
      )}

      {/* Error overlay */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100/75">
          <div className="text-center p-6 max-w-sm">
            <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load 3D model</h3>
            <p className="text-sm text-gray-600 mb-4">The 3D model could not be loaded. This might be due to a connection issue or an unsupported format.</p>
          </div>
        </div>
      )}
    </div>
  )
}
