'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { ProductMediaItem, getProductMedia } from '../lib/product-media';

type ProductImageGalleryProps = {
  productId: string;
  initialImage?: string;
  className?: string;
  showThumbnails?: boolean;
};

export default function ProductImageGallery({
  productId,
  initialImage,
  className = '',
  showThumbnails = true,
}: ProductImageGalleryProps) {
  const [mediaItems, setMediaItems] = useState<ProductMediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  useEffect(() => {
    async function loadProductMedia() {
      try {
        setLoading(true);
        const media = await getProductMedia(productId);
        
        // If we have media items, use them
        if (media && media.length > 0) {
          const validItems = media.filter((item: ProductMediaItem) => item.url);
          setMediaItems(validItems);
          
          // If initialImage is provided, try to find its index
          if (initialImage) {
            const index = validItems.findIndex((item: ProductMediaItem) => item.url === initialImage);
            if (index !== -1) {
              setSelectedIndex(index);
            }
          }
        } 
        // Otherwise, if we have an initialImage, create a placeholder media item
        else if (initialImage) {
          setMediaItems([{
            id: 'placeholder',
            product_id: productId,
            url: initialImage,
            type: 'image',
            position: 0,
            created_at: new Date().toISOString(),
            alt: null
          }]);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error loading product media:', err);
        setError('Failed to load product images');
        setLoading(false);
        
        // Still use initialImage if provided, even if there's an error
        if (initialImage) {
          setMediaItems([{
            id: 'placeholder',
            product_id: productId,
            url: initialImage,
            type: 'image',
            position: 0,
            created_at: new Date().toISOString(),
            alt: null
          }]);
        }
      }
    }
    
    loadProductMedia();
  }, [productId, initialImage]);
  
  const handleThumbnailClick = (index: number) => {
    setSelectedIndex(index);
  };
  
  if (loading) {
    return (
      <div className={`bg-gray-100 animate-pulse ${className}`}>
        <div className="aspect-square w-full"></div>
      </div>
    );
  }
  
  if (error && mediaItems.length === 0) {
    return (
      <div className={`bg-gray-50 flex items-center justify-center ${className}`}>
        <div className="text-red-500 text-sm p-4">{error}</div>
      </div>
    );
  }
  
  if (mediaItems.length === 0) {
    return (
      <div className={`bg-gray-50 flex items-center justify-center ${className}`}>
        <div className="text-gray-400">No images available</div>
      </div>
    );
  }
  
  // Main image
  const mainMedia = mediaItems[selectedIndex];
  const is3DModel = mainMedia?.type === 'model_3d';
  const isAREnabled = mediaItems.some((item: ProductMediaItem) => item.type === 'ar');
  
  return (
    <div className={`${className}`}>
      {/* Main image/media */}
      <div className="relative aspect-square w-full mb-2 bg-gray-50 overflow-hidden">
        {mainMedia?.type === 'image' ? (
          <Image
            src={mainMedia.url || ''}
            alt="Product image"
            fill
            priority
            className="object-cover"
          />
        ) : mainMedia?.type === 'model_3d' ? (
          <div className="w-full h-full flex items-center justify-center">
            <iframe 
              src={mainMedia.url}
              title="3D Model Viewer"
              className="w-full h-full border-0"
              allow="camera *; accelerometer *; autoplay *; fullscreen *;"
            ></iframe>
          </div>
        ) : (
          <Image
            src={mainMedia?.url || ''}
            alt="Product image"
            fill
            priority
            className="object-cover"
          />
        )}
        
        {/* 3D and AR buttons if available */}
        <div className="absolute bottom-3 right-3 flex space-x-2">
          {mediaItems.some((item: ProductMediaItem) => item.type === 'model_3d') && (
            <button 
              onClick={() => {
                const modelIndex = mediaItems.findIndex((item: ProductMediaItem) => item.type === 'model_3d');
                if (modelIndex >= 0) setSelectedIndex(modelIndex);
              }}
              className={`p-2 rounded-full ${
                is3DModel ? 'bg-black text-white' : 'bg-white/80 text-gray-800 hover:bg-white'
              } backdrop-blur-sm shadow-sm`}
              title="View in 3D"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0zm0 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
              </svg>
            </button>
          )}
          
          {isAREnabled && (
            <a 
              href={mediaItems.find((item: ProductMediaItem) => item.type === 'ar')?.url}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 rounded-full bg-white/80 text-gray-800 hover:bg-white backdrop-blur-sm shadow-sm"
              title="View in AR"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
              </svg>
            </a>
          )}
        </div>
      </div>
      
      {/* Thumbnails */}
      {showThumbnails && mediaItems.length > 1 && (
        <div className="flex space-x-2 overflow-x-auto py-2">
          {mediaItems.map((item, index) => (
            <div 
              key={item.id} 
              className={`relative w-16 h-16 cursor-pointer border-2 ${
                selectedIndex === index ? 'border-black' : 'border-transparent'
              }`}
              onClick={() => handleThumbnailClick(index)}
            >
              {item.type === 'image' ? (
                <Image
                  src={item.url}
                  alt={`Product thumbnail ${index + 1}`}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100">
                  {item.type === 'model_3d' ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0zm0 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                      <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                    </svg>
                  )}
                </div>
              )}
              {/* Indicator for media type */}
              {item.type !== 'image' && (
                <div className="absolute bottom-0 right-0 bg-black text-white text-[8px] px-1 rounded-sm">
                  {item.type === 'model_3d' ? '3D' : 'AR'}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

/**
 * Admin version of the ProductImageGallery component that allows uploading and managing images
 */
export function AdminProductImageGallery({
  productId,
  initialImage,
  className = '',
  onImagesUpdated,
}: ProductImageGalleryProps & { onImagesUpdated?: () => void }) {
  const [mediaItems, setMediaItems] = useState<ProductMediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  
  useEffect(() => {
    loadProductMedia();
  }, [productId]);
  
  async function loadProductMedia() {
    try {
      setLoading(true);
      const media = await getProductMedia(productId);
      
      // If we have media items, use them
      if (media && media.length > 0) {
        const validItems = media.filter((item: ProductMediaItem) => item.url);
        setMediaItems(validItems);
        
        // If initialImage is provided, try to find its index
        if (initialImage) {
          const index = validItems.findIndex((item: ProductMediaItem) => item.url === initialImage);
          if (index !== -1) {
            setSelectedIndex(index);
          }
        }
      } 
      // Otherwise, if we have an initialImage, create a placeholder media item
      else if (initialImage) {
        setMediaItems([{
          id: 'placeholder',
          product_id: productId,
          url: initialImage,
          type: 'image',
          position: 0,
          created_at: new Date().toISOString(),
          alt: null
        }]);
      }
      
      setLoading(false);
    } catch (err) {
      console.error('Error loading product media:', err);
      setError('Failed to load product images');
      setLoading(false);
      
      // Still use initialImage if provided, even if there's an error
      if (initialImage) {
        setMediaItems([{
          id: 'placeholder',
          product_id: productId,
          url: initialImage,
          type: 'image',
          position: 0,
          created_at: new Date().toISOString(),
          alt: null
        }]);
      }
    }
  }
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImageFile(e.target.files[0]);
    }
  };
  
  const handleMultipleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      try {
        setUploading(true);
        
        // Convert files to base64
        const filesArray = Array.from(e.target.files);
        const imageDataPromises = filesArray.map(file => {
          return new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = error => reject(error);
          });
        });
        
        const imageDataList = await Promise.all(imageDataPromises);
        
        // Upload images using the utility function
        const response = await fetch('/api/upload', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ images: imageDataList }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to upload images');
        }
        
        const data = await response.json();
        
        // Associate uploaded images with the product
        const mediaItems = data.results.map((result: any, index: number) => ({
          url: result.url,
          type: 'image',
          position: index
        }));
        
        const mediaResponse = await fetch('/api/product-media', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            productId,
            mediaItems
          }),
        });
        
        if (!mediaResponse.ok) {
          throw new Error('Failed to associate images with product');
        }
        
        // Refresh the media list
        await loadProductMedia();
        
        // Notify parent component if needed
        if (onImagesUpdated) {
          onImagesUpdated();
        }
      } catch (err) {
        console.error('Error uploading images:', err);
        setError('Failed to upload images');
      } finally {
        setUploading(false);
        // Clear the file input
        e.target.value = '';
      }
    }
  };
  
  const handleDeleteImage = async (mediaId: string) => {
    if (!confirm('Are you sure you want to delete this image?')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/product-media?id=${mediaId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete image');
      }
      
      // Refresh the media list
      await loadProductMedia();
      
      // Reset selected index if needed
      if (selectedIndex >= mediaItems.length - 1) {
        setSelectedIndex(Math.max(0, mediaItems.length - 2));
      }
      
      // Notify parent component if needed
      if (onImagesUpdated) {
        onImagesUpdated();
      }
    } catch (err) {
      console.error('Error deleting image:', err);
      setError('Failed to delete image');
    }
  };
  
  return (
    <div className={`${className}`}>
      {/* Main image */}
      <div className="relative aspect-square w-full mb-2 bg-gray-50 overflow-hidden">
        {mediaItems.length > 0 ? (
          <Image
            src={mediaItems[selectedIndex]?.url || ''}
            alt="Product image"
            fill
            priority
            className="object-cover"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <span className="text-gray-400">No images yet</span>
          </div>
        )}
      </div>
      
      {/* Thumbnails and controls */}
      <div className="flex flex-col space-y-4">
        {/* Thumbnails */}
        {mediaItems.length > 1 && (
          <div className="flex space-x-2 overflow-x-auto py-2">
            {mediaItems.map((item, index) => (
              <div 
                key={item.id} 
                className="relative group"
              >
                <div 
                  className={`relative w-16 h-16 cursor-pointer border-2 ${
                    selectedIndex === index ? 'border-black' : 'border-transparent'
                  }`}
                  onClick={() => setSelectedIndex(index)}
                >
                  <Image
                    src={item.url}
                    alt={`Product thumbnail ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
                
                {/* Delete button */}
                <button
                  type="button"
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => handleDeleteImage(item.id)}
                  title="Delete image"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
        
        {/* Upload controls */}
        <div className="flex flex-col space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Add More Images
          </label>
          <input
            type="file"
            onChange={handleMultipleFileUpload}
            multiple
            accept="image/*"
            className="block w-full text-sm text-gray-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded file:border-0
                      file:text-sm file:font-semibold
                      file:bg-gray-50 file:text-gray-700
                      hover:file:bg-gray-100"
            disabled={uploading}
          />
          
          {uploading && (
            <div className="text-sm text-gray-500 flex items-center">
              <div className="mr-2 animate-spin h-4 w-4 border-2 border-gray-500 rounded-full border-t-transparent"></div>
              Uploading...
            </div>
          )}
          
          {error && (
            <div className="text-sm text-red-500">{error}</div>
          )}
        </div>
      </div>
    </div>
  );
}