'use client'

import { usePathname } from 'next/navigation'

interface Product {
  id: string
  name: string
  description?: string
  price: number
  brand?: string
  condition?: string
  main_image_url?: string
  category?: string
  availability?: string
}

interface StructuredDataProps {
  type: 'organization' | 'product' | 'breadcrumb' | 'website' | 'faq'
  data?: any
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const pathname = usePathname()

  const getStructuredData = () => {
    const baseUrl = 'https://treasuresofmaimi.com'
    
    switch (type) {
      case 'organization':
        return {
          '@context': 'https://schema.org',
          '@type': 'Organization',
          name: 'Treasures of Maimi',
          alternateName: 'Shop Maimi',
          description: 'AI-curated vintage luxury fashion boutique specializing in authenticated pre-loved designer pieces with certificate of authenticity. Sustainable luxury fashion from Miami.',
          '@id': `${baseUrl}/#organization`,
          foundingDate: '2023',
          numberOfEmployees: '5-10',
          currenciesAccepted: 'EUR, USD',
          paymentAccepted: 'Credit Card, PayPal, Stripe',
          priceRange: '€€€-€€€€',
          url: baseUrl,
          logo: `${baseUrl}/images/logo.png`,
          image: `${baseUrl}/images/logo.png`,
          email: '<EMAIL>',
          address: {
            '@type': 'PostalAddress',
            addressCountry: 'ES',
            addressLocality: 'Madrid',
            addressRegion: 'Madrid'
          },
          contactPoint: {
            '@type': 'ContactPoint',
            email: '<EMAIL>',
            contactType: 'customer service',
            availableLanguage: ['English', 'Spanish']
          },
          sameAs: [
            'https://instagram.com/treasuresofmaimi',
            'https://facebook.com/treasuresofmaimi'
          ],
          founder: {
            '@type': 'Person',
            name: 'Maimi',
            description: 'Luxury fashion curator and vintage specialist'
          },
          specialty: [
            'Authenticated Vintage Luxury Fashion',
            'Pre-loved Designer Items with Certificate of Authenticity',
            'Sustainable Circular Fashion',
            'AI-Curated Luxury Collections',
            'Vintage Designer Handbags',
            'Authenticated Designer Clothing',
            'Luxury Fashion Authentication Service',
            'Sustainable Luxury Shopping'
          ],
          makesOffer: [
            {
              '@type': 'Offer',
              itemOffered: {
                '@type': 'Service',
                name: 'Authentication Service',
                description: 'Professional authentication with certificate of authenticity'
              }
            },
            {
              '@type': 'Offer',
              itemOffered: {
                '@type': 'Service',
                name: 'AI Curation Service',
                description: 'AI-powered personalized vintage luxury recommendations'
              }
            }
          ],
          keywords: 'authenticated vintage luxury fashion, pre-loved designer bags, sustainable luxury boutique, AI curated vintage, certificate of authenticity, circular fashion economy, luxury consignment Miami, vintage designer authentication, sustainable fashion shopping, authenticated Chanel vintage, authenticated Louis Vuitton vintage, authenticated Gucci vintage, authenticated Hermès vintage',
          areaServed: {
            '@type': 'Place',
            name: 'Worldwide',
            description: 'Global shipping from Miami, Florida'
          },
          serviceArea: {
            '@type': 'GeoCircle',
            geoRadius: 'Global',
            description: 'Worldwide shipping for authenticated vintage luxury fashion'
          },
          hasOfferCatalog: {
            '@type': 'OfferCatalog',
            name: 'Vintage Luxury Collection',
            itemListElement: [
              {
                '@type': 'Offer',
                itemOffered: {
                  '@type': 'Product',
                  category: 'Luxury Handbags'
                }
              },
              {
                '@type': 'Offer',
                itemOffered: {
                  '@type': 'Product',
                  category: 'Designer Clothing'
                }
              },
              {
                '@type': 'Offer',
                itemOffered: {
                  '@type': 'Product',
                  category: 'Vintage Accessories'
                }
              }
            ]
          }
        }

      case 'website':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          name: 'Treasures of Maimi',
          alternateName: 'Shop Maimi',
          url: baseUrl,
          description: 'Discover AI-curated vintage luxury fashion pieces with certificate of authenticity. Shop authenticated pre-loved designer items from Louis Vuitton, Chanel, Gucci, Hermès. Sustainable luxury fashion with worldwide shipping from Miami.',
          mainContentOfPage: {
            '@type': 'WebPageElement',
            description: 'Authenticated vintage luxury fashion collection with AI-powered curation'
          },
          significantLink: [
            `${baseUrl}/products`,
            `${baseUrl}/collection`,
            `${baseUrl}/about`,
            `${baseUrl}/contact`
          ],
          inLanguage: 'en',
          potentialAction: {
            '@type': 'SearchAction',
            target: {
              '@type': 'EntryPoint',
              urlTemplate: `${baseUrl}/products?search={search_term_string}`
            },
            'query-input': 'required name=search_term_string'
          },
          publisher: {
            '@type': 'Organization',
            name: 'Treasures of Maimi',
            logo: `${baseUrl}/images/logo.png`
          },
          about: {
            '@type': 'Thing',
            name: 'Vintage Luxury Fashion',
            description: 'Sustainable luxury fashion through curated vintage and pre-loved designer pieces'
          },
          audience: {
            '@type': 'Audience',
            audienceType: 'Fashion Enthusiasts',
            geographicArea: 'Worldwide'
          }
        }

      case 'product':
        if (!data) return null
        const product = data as Product
        return {
          '@context': 'https://schema.org',
          '@type': 'Product',
          name: product.name,
          description: product.description || `Vintage luxury ${product.name} from Treasures of Maimi collection`,
          image: product.main_image_url || `${baseUrl}/images/logo.png`,
          brand: {
            '@type': 'Brand',
            name: product.brand || 'Vintage Designer'
          },
          category: product.category || 'Luxury Fashion',
          condition: product.condition || 'Very Good',
          offers: {
            '@type': 'Offer',
            price: product.price,
            priceCurrency: 'EUR',
            availability: product.availability === 'available' 
              ? 'https://schema.org/InStock' 
              : 'https://schema.org/OutOfStock',
            seller: {
              '@type': 'Organization',
              name: 'Treasures of Maimi',
              url: baseUrl
            },
            priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
            itemCondition: product.condition === 'excellent' 
              ? 'https://schema.org/NewCondition'
              : 'https://schema.org/UsedCondition'
          },
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '4.8',
            reviewCount: '15',
            bestRating: '5',
            worstRating: '1'
          },
          audience: {
            '@type': 'Audience',
            audienceType: 'Fashion Enthusiasts'
          },
          additionalProperty: [
            {
              '@type': 'PropertyValue',
              name: 'Style',
              value: 'Vintage Luxury'
            },
            {
              '@type': 'PropertyValue',
              name: 'Sustainability',
              value: 'Pre-loved, Sustainable Fashion'
            }
          ]
        }

      case 'breadcrumb':
        const breadcrumbList = []
        const pathSegments = pathname.split('/').filter(Boolean)
        let url = baseUrl
        
        breadcrumbList.push({
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: baseUrl
        })

        pathSegments.forEach((segment, index) => {
          url += `/${segment}`
          breadcrumbList.push({
            '@type': 'ListItem',
            position: index + 2,
            name: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
            item: url
          })
        })

        return {
          '@context': 'https://schema.org',
          '@type': 'BreadcrumbList',
          itemListElement: breadcrumbList
        }

      case 'faq':
        return {
          '@context': 'https://schema.org',
          '@type': 'FAQPage',
          mainEntity: [
            {
              '@type': 'Question',
              name: 'What types of luxury items do you sell?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'We specialize in curated vintage luxury fashion including designer handbags, clothing, accessories, and jewelry from brands like Louis Vuitton, Chanel, Hermès, Gucci, and more. All items are authentic and carefully selected for quality.'
              }
            },
            {
              '@type': 'Question',
              name: 'How do you authenticate your products?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'Every item undergoes thorough authentication by our expert team and comes with a certificate of authenticity. We examine materials, craftsmanship, hardware, serial numbers, date codes, and other brand-specific details to ensure 100% authenticity. Our authentication process is backed by years of luxury fashion expertise.'
              }
            },
            {
              '@type': 'Question',
              name: 'Do you provide certificates of authenticity?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'Yes, every authenticated vintage luxury item comes with our official certificate of authenticity. This certificate details the authentication process and provides assurance of the item\'s genuineness.'
              }
            },
            {
              '@type': 'Question',
              name: 'How does your AI curation work?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'Our AI system analyzes your style preferences, browsing history, and fashion trends to recommend vintage luxury pieces that match your taste. This personalized curation helps you discover unique authenticated items that suit your style.'
              }
            },
            {
              '@type': 'Question',
              name: 'Do you ship internationally?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'Yes, we offer worldwide shipping. Delivery times and costs vary by destination. We use secure, tracked shipping methods to ensure your luxury items arrive safely.'
              }
            },
            {
              '@type': 'Question',
              name: 'What is your return policy?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'We offer a 14-day return policy for items in their original condition. All returns must be approved in advance and items must be returned in their original packaging.'
              }
            },
            {
              '@type': 'Question',
              name: 'Are your items sustainable?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'Absolutely! By offering pre-loved luxury items, we promote sustainable fashion and circular economy. Each purchase helps extend the lifecycle of luxury goods and reduces environmental impact.'
              }
            }
          ]
        }

      default:
        return null
    }
  }

  const structuredData = getStructuredData()
  
  if (!structuredData) return null

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData, null, 2) }}
    />
  )
}