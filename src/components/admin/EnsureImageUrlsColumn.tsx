'use client';

import React from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { getUser, isAdmin } from '@/lib/auth';
import { Database } from '@/lib/database.types';

export default function EnsureImageUrlsColumn() {
  const [status, setStatus] = useState<'pending' | 'checking' | 'success' | 'error'>('pending');
  const [message, setMessage] = useState('Checking database schema...');
  const [visible, setVisible] = useState(true);
  const [userIsAdmin, setUserIsAdmin] = useState(false);

  // Check if user is admin on component mount
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        // Get the current user first
        const user = await getUser();
        if (user && user.id) {
          const adminStatus = await isAdmin(user.id);
          setUserIsAdmin(adminStatus);
        } else {
          setUserIsAdmin(false);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        setUserIsAdmin(false);
      }
    };
    
    checkAdminStatus();
  }, []);

  useEffect(() => {
    // Hide the component after successful operation
    if (status === 'success') {
      const timer = setTimeout(() => {
        setVisible(false);
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [status]);
  
  useEffect(() => {
    const checkAndEnsureColumn = async () => {
      // Only try to run this if user is admin
      if (!userIsAdmin) {
        setStatus('error');
        setMessage('Admin access required to check database schema');
        return;
      }
      
      try {
        setStatus('checking');
        setMessage('Checking database schema...');
        
        const response = await fetch('/api/ensure-image-urls-column', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        const data = await response.json();
        
        if (data.success) {
          setStatus('success');
          setMessage(data.message);
          toast.success(data.message);
        } else {
          setStatus('error');
          setMessage(`Error: ${data.error}`);
          toast.error(`Schema check failed: ${data.error}`);
        }
      } catch (error) {
        setStatus('error');
        setMessage(`Failed to check database schema: ${error}`);
        toast.error('Failed to check database schema');
      }
    };
    
    // Wait a moment before checking to ensure auth state is loaded
    const timer = setTimeout(() => {
      checkAndEnsureColumn();
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [userIsAdmin]);
  
  if (!visible) return null;
  
  if (status === 'pending' || status === 'checking') {
    return (
      <div className="flex items-center justify-center p-4 bg-gray-50 rounded-md mb-4">
        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>{message}</span>
      </div>
    );
  }
  
  if (status === 'error') {
    return (
      <div className="p-4 bg-red-50 border border-red-200 text-red-700 rounded-md mb-4">
        <div className="flex items-center">
          <svg className="h-5 w-5 text-red-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <div>
            <p className="font-medium">Database schema error</p>
            <p className="text-sm">{message}</p>
          </div>
        </div>
        <div className="mt-3">
          <button
            onClick={() => window.location.reload()}
            className="px-3 py-1 text-sm text-white bg-red-600 hover:bg-red-700 rounded"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }
  
  // Success state - optionally you can hide this after a few seconds
  return (
    <div className="p-4 bg-green-50 border border-green-200 text-green-700 rounded-md mb-4">
      <div className="flex items-center">
        <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
        <p>{message}</p>
      </div>
    </div>
  );
}
