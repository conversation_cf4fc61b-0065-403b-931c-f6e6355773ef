'use client';

import { useState, useEffect } from 'react';
import { Bell, BellRing } from 'lucide-react';
import { notificationService, NotificationData } from '@/lib/notifications';

interface NotificationBellProps {
  onNotificationClick?: () => void;
}

export default function NotificationBell({ onNotificationClick }: NotificationBellProps) {
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    initializeNotifications();
  }, []);

  const initializeNotifications = async () => {
    try {
      // Initialize notification service
      const initialized = await notificationService.initialize();
      if (!initialized) {
        console.warn('Failed to initialize notification service');
        setIsLoading(false);
        return;
      }

      // Check permission status
      const permission = Notification.permission;
      setHasPermission(permission === 'granted');

      // Get initial unread count
      const count = await notificationService.getUnreadCount();
      setUnreadCount(count);

      // Subscribe to real-time notifications
      const subscription = notificationService.subscribeToNotifications(handleNewNotification);

      // Cleanup subscription on unmount
      return () => {
        subscription?.unsubscribe();
      };
    } catch (error) {
      console.error('Error initializing notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewNotification = (notification: NotificationData) => {
    // Update unread count
    setUnreadCount(prev => prev + 1);

    // Show browser notification if permission granted
    if (hasPermission && 'Notification' in window) {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/icons/icon-192.png',
        tag: notification.id,
        requireInteraction: notification.priority === 'urgent' || notification.priority === 'high'
      });
    }
  };

  const requestNotificationPermission = async () => {
    try {
      const permission = await notificationService.requestPermission();
      setHasPermission(permission === 'granted');

      if (permission === 'granted') {
        // Subscribe to push notifications
        await notificationService.subscribeToPush();
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  };

  const handleClick = () => {
    // Show the dropdown when bell is clicked
    onNotificationClick?.();
  };

  if (isLoading) {
    return (
      <button className="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
        <Bell className="h-6 w-6" />
      </button>
    );
  }

  return (
    <button
      onClick={handleClick}
      className="relative p-2 text-gray-600 hover:text-gray-800 transition-colors"
      title={hasPermission ? 'View notifications' : 'Enable notifications'}
    >
      {unreadCount > 0 ? (
        <BellRing className="h-6 w-6 text-blue-600" />
      ) : (
        <Bell className="h-6 w-6" />
      )}
      
      {unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]">
          {unreadCount > 99 ? '99+' : unreadCount}
        </span>
      )}
      
      {!hasPermission && (
        <button
          onClick={(e) => {
            e.stopPropagation(); // Prevent bell click
            requestNotificationPermission();
          }}
          className="absolute -top-1 -right-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs rounded-full h-3 w-3 transition-colors"
          title="Click to enable browser notifications"
        ></button>
      )}
    </button>
  );
}
