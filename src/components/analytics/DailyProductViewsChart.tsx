'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { getDailyProductViews, DailyProductView } from '@/lib/product-analytics';

// Generate unique colors for each product
const generateColor = (index: number): string => {
  const colors = [
    '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
    '#00C49F', '#FFBB28', '#FF8042', '#a4de6c', '#d0ed57'
  ];
  return colors[index % colors.length];
};

// Format date for display
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' });
};

export default function DailyProductViewsChart() {
  const [viewData, setViewData] = useState<DailyProductView[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [uniqueProducts, setUniqueProducts] = useState<string[]>([]);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getDailyProductViews();
        setViewData(data);
        
        // Extract unique product names
        const productNames = Array.from(new Set(data.map(item => item.product_name)));
        setUniqueProducts(productNames);
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching daily product views:', err);
        setError('Failed to load chart data');
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // Transform data for the chart
  const transformDataForChart = () => {
    // Group by day
    const groupedByDay: Record<string, Record<string, number>> = {};
    
    viewData.forEach(item => {
      if (!groupedByDay[item.view_day]) {
        groupedByDay[item.view_day] = {};
      }
      
      groupedByDay[item.view_day][item.product_name] = item.views;
    });
    
    // Convert to array format for Recharts
    return Object.entries(groupedByDay).map(([day, products]) => {
      const result: any = { day, formattedDay: formatDate(day) };
      
      // Add each product's views
      uniqueProducts.forEach(productName => {
        result[productName] = products[productName] || 0;
      });
      
      return result;
    }).sort((a, b) => a.day.localeCompare(b.day)); // Sort by date
  };
  
  const chartData = transformDataForChart();
  
  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading chart data...</div>;
  }
  
  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }
  
  if (viewData.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Daily Product Views</h3>
        <div className="text-gray-500 p-4 text-center">No view data available for the last 7 days</div>
      </div>
    );
  }
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Daily Product Views</h3>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="formattedDay" />
            <YAxis />
            <Tooltip />
            <Legend />
            {uniqueProducts.map((product, index) => (
              <Line
                key={product}
                type="monotone"
                dataKey={product}
                stroke={generateColor(index)}
                activeDot={{ r: 8 }}
                name={product}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </div>
      <div className="text-xs text-gray-500 mt-2 text-center">
        Data shown for the last 7 days
      </div>
    </div>
  );
}
