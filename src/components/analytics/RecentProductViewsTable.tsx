'use client';

import React, { useState, useEffect } from 'react';
import { getRecentProductViews } from '@/lib/product-analytics';
import Image from 'next/image';

type RecentView = {
  product_name: string;
  product_image_url: string | null;
  viewed_at: string;
  user_email: string;
  user_name: string | null;
};

type Props = {
  limit?: number;
};

export default function RecentProductViewsTable({ limit = 10 }: Props) {
  const [viewData, setViewData] = useState<RecentView[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getRecentProductViews(limit);
        
        // Map the data to the expected format
        const formattedData = data.map(item => ({
          product_name: item.product_name,
          product_image_url: item.product_image_url,
          viewed_at: new Date(item.viewed_at).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }),
          user_email: item.user?.email || 'Guest User',
          user_name: item.user ? `${item.user.first_name || ''} ${item.user.last_name || ''}`.trim() : null
        }));
        
        setViewData(formattedData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching recent product views:', err);
        setError('Failed to load recent views');
        setLoading(false);
      }
    };
    
    fetchData();
  }, [limit]);
  
  if (loading) {
    return <div className="flex justify-center items-center h-24">Loading recent views...</div>;
  }
  
  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }
  
  if (viewData.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Recent Product Views</h3>
        <div className="text-gray-500 p-4 text-center">No recent views available</div>
      </div>
    );
  }
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Recent Product Views</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Product
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Viewed At
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {viewData.map((view, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {view.product_image_url && (
                      <div className="flex-shrink-0 h-10 w-10 mr-3">
                        <Image
                          src={view.product_image_url}
                          alt={view.product_name}
                          width={40}
                          height={40}
                          className="rounded-md object-cover"
                        />
                      </div>
                    )}
                    <div className="text-sm font-medium text-gray-900">
                      {view.product_name}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {view.viewed_at}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm">
                    {view.user_name && (
                      <div className="font-medium text-gray-900">{view.user_name}</div>
                    )}
                    <div className="text-gray-500">{view.user_email}</div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
