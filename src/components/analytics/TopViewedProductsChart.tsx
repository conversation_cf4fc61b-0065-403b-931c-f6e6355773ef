'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { getTopViewedProducts, TopViewedProduct } from '@/lib/product-analytics';

type Props = {
  days?: number;
};

export default function TopViewedProductsChart({ days = 7 }: Props) {
  const [viewData, setViewData] = useState<TopViewedProduct[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getTopViewedProducts(days);
        
        // Sort by views in descending order and take top 10
        const sortedData = [...data]
          .sort((a, b) => b.views - a.views)
          .slice(0, 10);
          
        setViewData(sortedData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching top viewed products:', err);
        setError('Failed to load chart data');
        setLoading(false);
      }
    };
    
    fetchData();
  }, [days]);
  
  // Transform data for the chart
  const transformDataForChart = () => {
    return viewData.map(item => ({
      name: item.name.length > 20 ? item.name.substring(0, 20) + '...' : item.name,
      views: item.views,
      fullName: item.name // For tooltip
    }));
  };
  
  const chartData = transformDataForChart();
  
  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading chart data...</div>;
  }
  
  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }
  
  if (viewData.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Top Viewed Products</h3>
        <div className="text-gray-500 p-4 text-center">No view data available for the last {days} days</div>
      </div>
    );
  }
  
  // Custom tooltip to show full product name
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-sm rounded">
          <p className="font-medium">{payload[0].payload.fullName}</p>
          <p className="text-sm">Views: <span className="font-semibold">{payload[0].value}</span></p>
        </div>
      );
    }
    return null;
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Top Viewed Products</h3>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            layout="vertical"
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis type="category" dataKey="name" width={150} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey="views" fill="#8884d8" name="Views" />
          </BarChart>
        </ResponsiveContainer>
      </div>
      <div className="text-xs text-gray-500 mt-2 text-center">
        Data shown for the last {days} days
      </div>
    </div>
  );
}
