'use client';

import React, { useState, useEffect } from 'react';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart
} from 'recharts';
import { getViewsPerDay, ViewsPerDay } from '@/lib/product-analytics';

type Props = {
  days?: number;
};

// Format date for display
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' });
};

export default function ViewsPerDayChart({ days = 14 }: Props) {
  const [viewData, setViewData] = useState<ViewsPerDay[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getViewsPerDay(days);
        setViewData(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching views per day:', err);
        setError('Failed to load chart data');
        setLoading(false);
      }
    };
    
    fetchData();
  }, [days]);
  
  // Transform data for the chart
  const transformDataForChart = () => {
    return viewData.map(item => ({
      day: item.view_day,
      formattedDay: formatDate(item.view_day),
      views: item.total_views
    })).sort((a, b) => a.day.localeCompare(b.day)); // Sort by date
  };
  
  const chartData = transformDataForChart();
  
  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading chart data...</div>;
  }
  
  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }
  
  if (viewData.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Daily Product Views (All Products)</h3>
        <div className="text-gray-500 p-4 text-center">No view data available for the last {days} days</div>
      </div>
    );
  }
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Daily Product Views (All Products)</h3>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={chartData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="formattedDay" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area 
              type="monotone" 
              dataKey="views" 
              stroke="#8884d8" 
              fill="#8884d8" 
              fillOpacity={0.3}
              name="Total Views" 
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
      <div className="text-xs text-gray-500 mt-2 text-center">
        Data shown for the last {days} days
      </div>
    </div>
  );
}
