import React, { useState, useEffect } from 'react';
import { PayPalButtons, usePayPalScriptReducer, PayPalScriptProvider } from '@paypal/react-paypal-js';
import { customToast } from '@/components/ui/CustomToast';
import { toast } from 'react-hot-toast';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/database.types';

interface DiscountInfo {
  code: string;
  discount: number;
  applied: boolean;
}

interface PayPalCheckoutButtonProps {
  items: any[];
  shippingInfo: any;
  userEmail?: string;
  discountInfo?: DiscountInfo | null;
  onSuccess: (orderData: any) => void;
  onCancel?: () => void;
  onError?: (error: any) => void;
  disabled?: boolean;
}

export default function PayPalCheckoutButton({
  items,
  shippingInfo,
  userEmail,
  discountInfo,
  onSuccess,
  onCancel,
  onError,
  disabled = false
}: PayPalCheckoutButtonProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const supabase = createClientComponentClient<Database>();
  
  useEffect(() => {
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setIsAuthenticated(!!session);
    };
    
    checkSession();
  }, [supabase]);

  if (isAuthenticated === false) {
    return (
      <div className="w-full">
        <div className="bg-gray-100 rounded-md p-4 text-center">
          <p className="text-gray-700 mb-2">Please sign in to proceed with PayPal payment.</p>
          <a href="/sign-in" className="text-blue-500 underline">Sign In</a>
        </div>
      </div>
    );
  }

  return (
    <PayPalScriptProvider
      options={{
        clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID!,
        currency: 'EUR',
        intent: 'capture',
        components: 'buttons'
      }}
    >
      <PayPalButtonsWrapper
        items={items}
        shippingInfo={shippingInfo}
        userEmail={userEmail}
        discountInfo={discountInfo}
        onSuccess={onSuccess}
        onCancel={onCancel}
        onError={onError}
        disabled={disabled}
      />
    </PayPalScriptProvider>
  );
}

function PayPalButtonsWrapper({
  items,
  shippingInfo,
  userEmail,
  discountInfo,
  onSuccess,
  onCancel,
  onError,
  disabled
}: PayPalCheckoutButtonProps) {
  const [{ isPending, options }] = usePayPalScriptReducer();
  const [orderId, setOrderId] = useState<string | null>(null);
  const [dbOrderId, setDbOrderId] = useState<string | null>(null);

  const isPayPalConfigured = options.clientId !== 'disabled' && options.enabled !== false;

  const createOrder = async () => {
    try {
      const response = await fetch('/api/paypal/create-order', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ items, shipping: shippingInfo, userEmail, discountInfo }),
      });
      const orderData = await response.json();
      if (!response.ok || !orderData.id) throw new Error(orderData.error || 'Failed to create order with PayPal. Please try again later.');
      if (orderData.dbOrderId) setDbOrderId(orderData.dbOrderId);
      return orderData.id;
    } catch (error: any) {
      console.error('Error creating PayPal order:', error);
      customToast.error(error.message || 'Failed to create order with PayPal');
      if (onError) onError(error);
      throw error;
    }
  };

  const onApproveHandler = async (data: any, actions: any) => {
    try {
      const toastId = toast.loading('Processing your payment...');
      setOrderId(data.orderID);
      const response = await fetch('/api/paypal/capture-order', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ orderID: data.orderID, dbOrderId }),
      });
      const orderData = await response.json();
      if (!response.ok || !orderData) throw new Error(orderData.error || 'Failed to capture payment with PayPal. Please contact support.');
      toast.dismiss(toastId);
      toast.success('Payment successful!');
      onSuccess(orderData);
    } catch (error: any) {
      console.error('Error capturing PayPal order:', error);
      customToast.error(error.message || 'Payment failed. Please try again.');
      if (onError) onError(error);
    }
  };

  if (!isPayPalConfigured) {
    return (
      <div className="w-full">
        <div className="bg-gray-100 rounded-md p-4 text-center">
          <p className="text-gray-700 mb-2">PayPal checkout is currently unavailable.</p>
          <p className="text-sm text-gray-500">Please use another payment method or try again later.</p>
          <p className="text-sm text-gray-400 mt-2">Ensure your PayPal Client ID is correctly set in the environment variables.</p>
          <button
            className="mt-3 bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 transition-colors"
            onClick={() => {
              customToast.info('PayPal is not configured. Please contact the store administrator.');
              if (onError) onError(new Error('PayPal is not configured'));
            }}
          >
            Simulate Payment
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full ${disabled ? 'opacity-60 pointer-events-none' : ''}`}>
      {isPending ? (
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#0070ba]"></div>
        </div>
      ) : (
        <PayPalButtons
          style={{ layout: 'vertical', color: 'blue', shape: 'rect', label: 'pay', height: 40 }}
          createOrder={createOrder}
          onApprove={onApproveHandler}
          onCancel={() => {
            customToast.info('Payment canceled');
            if (onCancel) onCancel();
          }}
          onError={(err) => {
            console.error('PayPal button error:', err);
            customToast.error('An error occurred with PayPal. Please try again.');
            if (onError) onError(err);
          }}
          disabled={disabled}
        />
      )}
    </div>
  );
}