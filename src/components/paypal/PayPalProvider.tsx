"use client";

import { ReactNode, useEffect, useState } from 'react';
import { PayPalScriptProvider } from '@paypal/react-paypal-js';
import { useAuth } from '@/components/AuthProvider';

interface PayPalProviderProps {
  children: ReactNode;
}

export default function PayPalProvider({ children }: PayPalProviderProps) {
  const [isClientIdValid, setIsClientIdValid] = useState(false);
  const { session } = useAuth();
  
  // Get the client ID from environment variables
  const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || '';
  
  // Check if the client ID is valid (not the placeholder value)
  useEffect(() => {
    // Check if client ID is the placeholder or empty
    const isValid = clientId !== '' && clientId !== 'sb-client-id';
    setIsClientIdValid(isValid);
    
    // Log warning if using placeholder
    if (!isValid) {
      console.warn('PayPal integration is disabled: Using placeholder client ID. Replace with a real PayPal client ID in .env.local file.');
    }
  }, [clientId]);
  
  // If client ID is not valid, don't even attempt to load the PayPal script
  if (!isClientIdValid) {
    return <>{children}</>;
  }
  
  return (
    <PayPalScriptProvider
      options={{
        clientId,
        currency: 'USD',
        intent: 'capture',
        components: 'buttons',
        'enable-funding': 'paypal',
        'disable-funding': 'credit,card',
        'data-client-token': session ? 'authenticated' : 'guest',
      }}
    >
      {children}
    </PayPalScriptProvider>
  );
}