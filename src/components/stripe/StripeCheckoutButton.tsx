'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { CreditCard } from 'lucide-react';
import { useAuth } from '@/components/AuthProvider';

interface CartItem {
  id: string;
  quantity: number;
  product: {
    id: string;
    name: string;
    price: number;
    condition?: string;
    main_image_url?: string;
  };
}

interface ShippingInfo {
  cost: number;
  description?: string;
  estimatedDays?: string;
}

interface DiscountInfo {
  code: string;
  discount: number;
  applied: boolean;
}

interface StripeCheckoutButtonProps {
  items: CartItem[];
  orderId?: string;
  customerEmail?: string;
  buttonText?: string;
  disabled?: boolean;
  shippingInfo: ShippingInfo;
  discountInfo?: DiscountInfo | null;
  onError?: (error: any) => void;
  className?: string;
}

export default function StripeCheckoutButton({
  items,
  orderId,
  customerEmail,
  buttonText = 'Checkout with Stripe',
  className = '',
  disabled = false,
  shippingInfo,
  discountInfo,
  onError
}: StripeCheckoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { session } = useAuth();

  const validateCartItems = (items: CartItem[]) => {
    try {
      const invalidItems = items.filter(item => {
        const price = Number(item.product.price);
        const quantity = Number(item.quantity);
        return (
          isNaN(price) || 
          price <= 0 || 
          isNaN(quantity) || 
          quantity <= 0 ||
          !item.product.name
        );
      });

      if (invalidItems.length > 0) {
        console.error('Invalid cart items:', invalidItems);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating cart items:', error);
      return false;
    }
  };

  const handleCheckout = async () => {
    if (disabled) {
      toast.error('Checkout is currently unavailable');
      return;
    }

    if (!items || items.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    if (!session || !session.user) {
      toast.error('You must be signed in to checkout');
      setIsLoading(false);
      return;
    }

    if (!validateCartItems(items)) {
      toast.error('Some items in your cart are invalid. Please refresh and try again.');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    try {
      // Log for debugging
      console.log('Starting Stripe checkout with items:', 
        items.map(item => ({
          id: item.product.id,
          name: item.product.name,
          price: item.product.price,
          quantity: item.quantity
        }))
      );

      // Call our API to create a Stripe checkout session
      const response = await fetch('/api/stripe/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items,
          orderId,
          customerEmail: session.user.email || customerEmail,
          shippingInfo,
          discountInfo
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      console.log('Stripe checkout response:', data);

      // If we have a URL, redirect to Stripe's hosted checkout
      if (data.url) {
        console.log('Redirecting to Stripe checkout URL:', data.url);
        console.log('Cancel URL will be:', data.cancel_url || 'Not provided');

        // Save cart state before redirect
        try {
          if (typeof window !== 'undefined') {
            const cartState = JSON.stringify(items);
            sessionStorage.setItem('pendingCheckout', cartState);
          }
        } catch (storageError) {
          console.error('Error saving cart state:', storageError);
          // Continue with redirect even if storage fails
        }

        // Use window.location.assign for more reliable redirection
        window.location.assign(data.url);
        return; // Important: stop execution here
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error: any) {
      console.error('Stripe checkout error:', error);
      toast.error(error.message || 'An error occurred during checkout');
      if (onError) {
        onError(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleCheckout}
      disabled={isLoading || disabled}
      className={`w-full bg-black hover:bg-gray-800 text-white py-3 px-4 rounded-md flex items-center justify-center ${className}`}
    >
      {isLoading ? (
        <div className="flex items-center">
          <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-white rounded-full" />
          Processing...
        </div>
      ) : (
        <>
          <CreditCard className="mr-2 h-5 w-5" />
          {buttonText}
        </>
      )}
    </button>
  );
}
