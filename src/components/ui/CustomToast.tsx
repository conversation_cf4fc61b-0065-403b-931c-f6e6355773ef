'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast, Toast, ToastPosition } from 'react-hot-toast';
import { Check, X, AlertCircle, ShoppingBag, Heart, Info } from 'lucide-react';

type CustomToastProps = {
  t: Toast;
  message: string | React.ReactNode;
  type: 'success' | 'error' | 'info' | 'cart' | 'wishlist' | 'warning';
};

export const customToast = {
  success: (message: string | React.ReactNode) => {
    return toast.custom((t) => (
      <CustomToast t={t} message={message} type="success" />
    ));
  },
  error: (message: string | React.ReactNode) => {
    return toast.custom((t) => (
      <CustomToast t={t} message={message} type="error" />
    ));
  },
  info: (message: string | React.ReactNode) => {
    return toast.custom((t) => (
      <CustomToast t={t} message={message} type="info" />
    ));
  },
  warning: (message: string | React.ReactNode) => {
    return toast.custom((t) => (
      <CustomToast t={t} message={message} type="warning" />
    ));
  },
  cart: (message: string | React.ReactNode) => {
    return toast.custom((t) => (
      <CustomToast t={t} message={message} type="cart" />
    ));
  },
  wishlist: (message: string | React.ReactNode) => {
    return toast.custom((t) => (
      <CustomToast t={t} message={message} type="wishlist" />
    ));
  },
  loading: (message: string | React.ReactNode) => {
    return toast.loading(String(message));
  },
  dismiss: (toastId: string) => {
    toast.dismiss(toastId);
  }
};

const getIconByType = (type: CustomToastProps['type']) => {
  switch (type) {
    case 'success':
      return <Check className="w-5 h-5" />;
    case 'error':
      return <AlertCircle className="w-5 h-5" />;
    case 'cart':
      return <ShoppingBag className="w-5 h-5" />;
    case 'wishlist':
      return <Heart className="w-5 h-5" />;
    case 'info':
    case 'warning':
    default:
      return <Info className="w-5 h-5" />;
  }
};

const getColorsByType = (type: CustomToastProps['type']) => {
  switch (type) {
    case 'success':
      return 'bg-green-50 border-green-200 text-green-800';
    case 'error':
      return 'bg-red-50 border-red-200 text-red-800';
    case 'cart':
      return 'bg-blue-50 border-blue-200 text-blue-800';
    case 'wishlist':
      return 'bg-purple-50 border-purple-200 text-purple-800';
    case 'info':
    case 'warning':
    default:
      return 'bg-gray-50 border-gray-200 text-gray-800';
  }
};

const getIconColorsByType = (type: CustomToastProps['type']) => {
  switch (type) {
    case 'success':
      return 'bg-green-100 text-green-600';
    case 'error':
      return 'bg-red-100 text-red-600';
    case 'cart':
      return 'bg-blue-100 text-blue-600';
    case 'wishlist':
      return 'bg-purple-100 text-purple-600';
    case 'info':
    case 'warning':
    default:
      return 'bg-gray-100 text-gray-600';
  }
};

export default function CustomToast({ t, message, type }: CustomToastProps) {
  const colors = getColorsByType(type);
  const iconColors = getIconColorsByType(type);
  const icon = getIconByType(type);

  return (
    <AnimatePresence>
      {t.visible && (
        <motion.div
          initial={{ opacity: 0, y: -20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 20, scale: 0.95 }}
          transition={{ duration: 0.3 }}
          className={`flex items-start p-4 mb-3 rounded-lg shadow-lg border ${colors} max-w-md w-full mx-auto`}
          style={{ position: 'relative' }}
        >
          <div className={`flex-shrink-0 p-2 rounded-full ${iconColors} mr-3`}>
            {icon}
          </div>
          <div className="flex-1 mr-2">
            {typeof message === 'string' ? (
              <p className="text-sm font-medium">{message}</p>
            ) : (
              message
            )}
          </div>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={18} />
          </button>
          
          {/* Progress bar */}
          <motion.div
            initial={{ width: '100%' }}
            animate={{ width: '0%' }}
            transition={{ duration: t.duration ? t.duration / 1000 : 3 }}
            className={`absolute bottom-0 left-0 h-1 ${type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : type === 'cart' ? 'bg-blue-500' : type === 'wishlist' ? 'bg-purple-500' : 'bg-gray-500'}`}
            style={{ borderRadius: '0 0 0.5rem 0.5rem' }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
} 