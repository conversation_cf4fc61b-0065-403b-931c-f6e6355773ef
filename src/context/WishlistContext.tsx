'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { WishlistContextType, WishlistItem } from '@/types/wishlist';
import { User } from '@supabase/supabase-js';
import toast from 'react-hot-toast';

const WishlistContext = createContext<WishlistContextType | null>(null);

export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};

const LOCAL_KEY = 'shop-maimi-guest-wishlist';

export const WishlistProvider = ({ children }: { children: React.ReactNode }) => {
  const supabase = createClientComponentClient();
  const [user, setUser] = useState<User | null>(null);
  const [wishlist, setWishlist] = useState<WishlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
    };
    loadSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_, session) => {
      setUser(session?.user || null);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase]);

  const saveGuestWishlist = (items: WishlistItem[]) => {
    localStorage.setItem(LOCAL_KEY, JSON.stringify(items));
  };

  const fetchWishlist = useCallback(async () => {
    setIsLoading(true);
    try {
      if (user) {
        const { data, error } = await supabase
          .from('wishlists')
          .select('id, product_id, created_at')
          .eq('user_id', user.id);

        if (error) throw error;
        setWishlist(data || []);
      } else {
        const stored = localStorage.getItem(LOCAL_KEY);
        setWishlist(stored ? JSON.parse(stored) : []);
      }
    } catch (err) {
      console.error('Wishlist fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user, supabase]);

  useEffect(() => {
    if (user !== undefined) {
      fetchWishlist();
    }
  }, [user, fetchWishlist]);

  const addToWishlist = async (productId: string) => {
    try {
      if (!productId) return false;

      if (wishlist.some((item) => item.product_id === productId)) {
        toast('Already in wishlist');
        return true;
      }

      if (user) {
        const { data, error: checkError } = await supabase
          .from('wishlists')
          .select('id')
          .eq('user_id', user.id)
          .eq('product_id', productId)
          .single();

        if (checkError && checkError.code !== 'PGRST116') throw checkError;
        if (data) {
          toast('Already in wishlist');
          return true;
        }

        const { error } = await supabase
          .from('wishlists')
          .insert({ user_id: user.id, product_id: productId });

        if (error) throw error;

        await fetchWishlist();
      } else {
        const newItem: WishlistItem = {
          id: `local-${Date.now()}`,
          product_id: productId,
          created_at: new Date().toISOString(),
        };
        const updated = [...wishlist, newItem];
        saveGuestWishlist(updated);
        setWishlist(updated);
      }

      toast.success('Added to wishlist');
      return true;
    } catch (err) {
      console.error('Add to wishlist error:', err);
      toast.error('Failed to add to wishlist');
      return false;
    }
  };

  const removeFromWishlist = async (productId: string) => {
    try {
      if (user) {
        await supabase
          .from('wishlists')
          .delete()
          .eq('user_id', user.id)
          .eq('product_id', productId);
        await fetchWishlist();
      } else {
        const updated = wishlist.filter(item => item.product_id !== productId);
        saveGuestWishlist(updated);
        setWishlist(updated);
      }
      toast.success('Removed from wishlist');
    } catch (err) {
      console.error('Remove from wishlist error:', err);
      toast.error('Failed to remove');
    }
  };

  const isInWishlist = (productId: string) => {
    return wishlist.some(item => item.product_id === productId);
  };

  const clearWishlist = async () => {
    try {
      if (user) {
        await supabase.from('wishlists').delete().eq('user_id', user.id);
      } else {
        localStorage.removeItem(LOCAL_KEY);
      }
      setWishlist([]);
      toast.success('Wishlist cleared');
    } catch (err) {
      console.error('Clear wishlist error:', err);
      toast.error('Failed to clear wishlist');
    }
  };

  const mergeLocalWishlistWithDB = async () => {
    if (!user) return;
    const stored = localStorage.getItem(LOCAL_KEY);
    if (stored) {
      const localItems: WishlistItem[] = JSON.parse(stored);
      const dbItems = wishlist.map((item) => item.product_id);
      const itemsToAdd = localItems.filter(
        (item) => !dbItems.includes(item.product_id)
      );
      if (itemsToAdd.length > 0) {
        const { error } = await supabase.from('wishlists').insert(
          itemsToAdd.map((item) => ({
            user_id: user.id,
            product_id: item.product_id,
          }))
        );
        if (error) {
          console.error('Merge wishlist error:', error);
          return;
        }
        localStorage.removeItem(LOCAL_KEY);
        await fetchWishlist();
      }
    }
  };

  return (
    <WishlistContext.Provider
      value={{
        wishlist: { items: wishlist, isLoading, error: null },
        addToWishlist,
        removeFromWishlist,
        isInWishlist,
        clearWishlist,
        refreshWishlist: fetchWishlist,
        mergeLocalWishlistWithDB,
      }}
    >
      {children}
    </WishlistContext.Provider>
  );
};

export default WishlistProvider;