import * as Sentry from '@sentry/nextjs';

export async function register() {
  // Only enable Sentry in production or when explicitly enabled
  if (process.env.NODE_ENV === 'production' || process.env.ENABLE_SENTRY === 'true') {
    console.log('Initializing Sentry instrumentation...');
    
    if (process.env.NEXT_RUNTIME === 'nodejs') {
      await import('../sentry.server.config');
    }

    if (process.env.NEXT_RUNTIME === 'edge') {
      await import('../sentry.edge.config');
    }
  } else {
    console.log('Sentry disabled for development');
  }
}

export const onRequestError = Sentry.captureRequestError;
