/**
 * Auth retry utility with exponential backoff to handle rate limits
 */

interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
}

export class AuthRateLimitError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthRateLimitError';
  }
}

export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2
  } = options;

  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      
      // Check if this is a rate limit error
      const isRateLimit = error.message?.includes('rate limit') || 
                         error.message?.includes('Too Many Requests') ||
                         error.status === 429;
      
      if (!isRateLimit || attempt === maxRetries) {
        // If it's not a rate limit error or we've exhausted retries, throw the error
        if (isRateLimit) {
          throw new AuthRateLimitError('Authentication rate limit exceeded. Please wait before retrying.');
        }
        throw error;
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay);
      
      console.log(`[AuthRetry] Rate limit hit, waiting ${delay}ms before retry ${attempt + 1}/${maxRetries}`);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

// Rate limiting tracker to prevent too many auth operations
class RateLimitTracker {
  private attempts: number[] = [];
  private readonly windowMs: number;
  private readonly maxAttempts: number;

  constructor(windowMs = 60000, maxAttempts = 5) {
    this.windowMs = windowMs;
    this.maxAttempts = maxAttempts;
  }

  canAttempt(): boolean {
    const now = Date.now();
    
    // Remove old attempts outside the window
    this.attempts = this.attempts.filter(time => now - time < this.windowMs);
    
    return this.attempts.length < this.maxAttempts;
  }

  recordAttempt(): void {
    this.attempts.push(Date.now());
  }

  getTimeUntilReset(): number {
    if (this.attempts.length === 0) return 0;
    
    const oldestAttempt = Math.min(...this.attempts);
    const resetTime = oldestAttempt + this.windowMs;
    
    return Math.max(0, resetTime - Date.now());
  }
}

// Global rate limit tracker for auth operations
export const authRateLimitTracker = new RateLimitTracker();

export function checkAuthRateLimit(): void {
  if (!authRateLimitTracker.canAttempt()) {
    const waitTime = authRateLimitTracker.getTimeUntilReset();
    throw new AuthRateLimitError(
      `Too many authentication attempts. Please wait ${Math.ceil(waitTime / 1000)} seconds before retrying.`
    );
  }
  
  authRateLimitTracker.recordAttempt();
}