import { User } from '@supabase/supabase-js';
import { Database } from './database.types';
import { supabase } from './supabase-browser';
import { retryWithBackoff, checkAuthRateLimit, AuthRateLimitError } from './auth-retry';

export type Profile = Database['public']['Tables']['profiles']['Row'];

// Re-export for convenience
export { AuthRateLimitError };

/**
 * Sign up a new user with email and password
 */
export async function signUp(email: string, password: string, fullName: string) {
  // Split full name into first and last name
  const nameParts = fullName.trim().split(' ');
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
        first_name: firstName,
        last_name: lastName,
      },
    },
  });

  if (error) {
    throw error;
  }

  if (data.user) {
    await ensureUserProfile(data.user.id, email, fullName, firstName, lastName);
  }

  return data;
}

/**
 * Sign in a user with email and password with rate limiting protection
 */
export async function signIn(email: string, password: string) {
  // Check rate limiting before attempting
  checkAuthRateLimit();
  
  return retryWithBackoff(async () => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw error;
    }

    // Ensure user profile exists after login
    if (data.user) {
      // Get user metadata to extract name information
      const fullName = data.user.user_metadata?.full_name || '';
      const firstName = data.user.user_metadata?.first_name || '';
      const lastName = data.user.user_metadata?.last_name || '';
      
      await ensureUserProfile(
        data.user.id, 
        email, 
        fullName, 
        firstName, 
        lastName
      );
    }

    return data;
  }, {
    maxRetries: 2,
    baseDelay: 2000, // Start with 2 second delay for auth rate limits
    maxDelay: 8000   // Max 8 second delay
  });
}

/**
 * Sign out the current user
 */
export async function signOut() {
  const { error } = await supabase.auth.signOut();
  if (error) {
    throw error;
  }
  
  // Force a page reload to clear any cached state
  if (typeof window !== 'undefined') {
    window.location.href = '/';
  }
}

/**
 * Get the current user session with rate limiting protection
 */
export async function getSession() {
  try {
    return retryWithBackoff(async () => {
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        console.error('Error getting session:', error);
        throw error;
      }
      return data.session;
    }, {
      maxRetries: 1,
      baseDelay: 1000
    });
  } catch (error) {
    console.error('Unexpected error getting session:', error);
    return null;
  }
}

/**
 * Get the current user
 */
export async function getUser(): Promise<User | null> {
  try {
    const { data } = await supabase.auth.getUser();
    return data?.user || null;
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
}

/**
 * Get a user's profile by ID
 */
export async function getUserProfile(userId: string): Promise<Profile | null> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      
      // If no profile found, try to create one
      if (error.code === 'PGRST116') {
        console.log('Profile not found, attempting to create one...');
      
        const { data: userData } = await supabase.auth.getUser();
        if (userData?.user?.email) {
          // Get user metadata to extract name information
          const fullName = userData.user.user_metadata?.full_name || '';
          const firstName = userData.user.user_metadata?.first_name || '';
          const lastName = userData.user.user_metadata?.last_name || '';
          
          // Create a profile for the user
          return await ensureUserProfile(
            userId, 
            userData.user.email, 
            fullName, 
            firstName, 
            lastName
          );
        }
      }
      return null;
    }

    return data;
  } catch (error) {
    console.error('Unexpected error fetching profile:', error);
    return null;
  }
}

/**
 * Check if the current user is an admin
 */
export async function isAdmin(userId: string): Promise<boolean> {
  console.log('Checking admin status for user ID:', userId);
  
  if (!userId) {
    console.warn('isAdmin called without userId');
    return false;
  }
  
  try {
    // Check the profile table for is_admin flag - this is the primary source of truth
    const profile = await getUserProfile(userId);
    console.log('User profile for admin check:', profile);

    return profile?.is_admin === true;
  } catch (error) {
    console.error('Error in isAdmin check:', error);
    return false;
  }
}

/**
 * Send a password reset email
 */
export async function resetPassword(email: string) {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/auth/reset-password`,
  });

  if (error) {
    throw error;
  }
}

/**
 * Update the user's password
 */
export async function updatePassword(password: string) {
  const { error } = await supabase.auth.updateUser({
    password,
  });

  if (error) {
    throw error;
  }
}

/**
 * Update the user's profile
 */
export async function updateProfile(userId: string, profile: Partial<Profile>) {
  if (!userId) {
    throw new Error('User ID is required to update profile');
  }
  
  console.log('updateProfile: Starting update with data:', profile);
  
  // Update the profile in the database
  const { data, error } = await supabase
    .from('profiles')
    .update({
      ...profile,
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId)
    .select('*')
    .maybeSingle();

  if (error) {
    console.error('Error updating profile:', error);
    throw error;
  }
  
  // Also update the user metadata
  if (profile.first_name || profile.last_name || profile.full_name || profile.street_address || profile.city || profile.state || profile.postal_code || profile.country) {
    await supabase.auth.updateUser({
      data: {
        first_name: profile.first_name,
        last_name: profile.last_name,
        full_name: profile.full_name,
        street_address: profile.street_address,
        city: profile.city,
        state: profile.state,
        postal_code: profile.postal_code,
        country: profile.country,
      },
    });
  }
  
  return data;
}

/**
 * Ensure a user profile exists
 */
export async function ensureUserProfile(
  userId: string, 
  email: string, 
  fullName?: string, 
  firstName?: string, 
  lastName?: string
): Promise<Profile> {
  console.log('Ensuring profile exists for user:', userId);
  
  if (!userId) {
    throw new Error('Cannot create profile: User ID is required');
  }
  
  // First check if profile already exists
  const { data: existingProfile, error: checkError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
    
  if (checkError && checkError.code !== 'PGRST116') {
    console.error('Error checking for existing profile:', checkError);
  }
    
  if (existingProfile) {
    console.log('Profile already exists:', existingProfile);
    
    // If profile exists but name fields are missing, update them
    if ((firstName || lastName || fullName) && 
        (!existingProfile.first_name || !existingProfile.last_name || !existingProfile.full_name)) {
      console.log('Updating existing profile with name information');
      
      const { data: updatedProfile, error: updateError } = await supabase
        .from('profiles')
        .update({
          full_name: fullName || existingProfile.full_name,
          first_name: firstName || existingProfile.first_name,
          last_name: lastName || existingProfile.last_name,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();
      
      if (updateError) {
        console.error('Error updating profile with name:', updateError);
      } else if (updatedProfile) {
        console.log('Profile updated with name information:', updatedProfile);
        return updatedProfile;
      }
    }
    
    return existingProfile;
  }
  
  // Profile doesn't exist, create it
  console.log('Creating new profile for user:', userId);
  
  // Set admin flag to false by default - will be set to true in profile if user is admin
  const isAdmin = false;
  
  // Create the profile
  const { data: newProfile, error } = await supabase
    .from('profiles')
    .upsert({
      id: userId,
      email: email,
      full_name: fullName || '',
      first_name: firstName || '',
      last_name: lastName || '',
      street_address: '',
      city: '',
      state: '',
      postal_code: '',
      country: '',
      is_admin: isAdmin,
      updated_at: new Date().toISOString(),
    })
    .select()
    .maybeSingle();
    
  if (error) {
    console.error('Error creating user profile:', error);
    throw error;
  }
  
  if (!newProfile) {
    throw new Error('Failed to create user profile');
  }
  
  return newProfile;
}

// Admin registration function
export async function registerAdmin(email: string, password: string) {
  // First check if the email is valid
  if (!email) {
    throw new Error('Email is required');
  }
  
  // Validate password
  if (!password || password.length < 6) {
    throw new Error('Password must be at least 6 characters');
  }
  
  // Sign up the user
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        is_admin: true, // Set admin flag in user metadata
        role: 'admin',  // Add role for additional verification
      },
    },
  });

  if (error) throw error;
  
  if (!data.user) {
    throw new Error('User registration failed');
  }

  console.log('User registered successfully:', data.user.id);
  
  // Use upsert to handle both insert and update cases
  const { error: upsertError } = await supabase
    .from('profiles')
    .upsert({
      id: data.user.id,
      email: email,
      is_admin: true,
      updated_at: new Date().toISOString(),
    }, { 
      onConflict: 'id',
      ignoreDuplicates: false // Update the record if it exists
    });
    
  if (upsertError) {
    console.error('Error upserting profile:', upsertError);
    throw upsertError;
  }
  
  // Verify the admin flag was set correctly
  const { data: verifiedProfile, error: verifyError } = await supabase
    .from('profiles')
    .select('is_admin')
    .eq('id', data.user.id)
    .single();
    
  if (verifyError) {
    console.error('Error verifying profile:', verifyError);
    throw verifyError;
  }
  
  if (!verifiedProfile || !verifiedProfile.is_admin) {
    console.error('Admin flag not set correctly in profile');
    // Try to fix it
    const { error: fixError } = await supabase
      .from('profiles')
      .update({ is_admin: true })
      .eq('id', data.user.id);
      
    if (fixError) {
      console.error('Error fixing admin flag:', fixError);
      throw new Error('Failed to set admin privileges correctly');
    }
  }
  
  console.log('Admin status verified:', verifiedProfile?.is_admin);
  
  return data;
}
