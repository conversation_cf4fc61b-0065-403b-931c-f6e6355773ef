export async function uploadToCloudinary(file: File) {
  try {
    // First, convert the file to base64
    const base64 = await fileToBase64(file);

    // Upload to our API route
    const response = await fetch('/api/upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ image: base64 }),
    });

    if (!response.ok) {
      throw new Error('Upload failed');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Upload error:', error);
    throw error;
  }
}

function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
}

export async function generate3DModel(imageUrl: string) {
  try {
    const response = await fetch('/api/generate3d', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('3D model generation failed');
    }

    return await response.json();
  } catch (error) {
    console.error('3D model generation error:', error);
    throw error;
  }
}
