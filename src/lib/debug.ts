/**
 * Debug Utility
 * Provides consistent logging across the application
 */

// Enable this to see debug logs in production
const DEBUG_IN_PRODUCTION = false;

// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development' || DEBUG_IN_PRODUCTION;

/**
 * Create a logger for a specific component or page
 * @param component The name of the component or page
 * @returns An object with logging methods
 */
export const createLogger = (component: string) => {
  return {
    /**
     * Log information messages
     * @param message The message to log
     * @param data Optional data to include
     */
    info: (message: string, data?: any) => {
      if (isDev) {
        if (data !== undefined) {
          console.log(`[${component}] ${message}`, data);
        } else {
          console.log(`[${component}] ${message}`);
        }
      }
    },
    
    /**
     * Log warning messages
     * @param message The warning message
     * @param data Optional data to include
     */
    warn: (message: string, data?: any) => {
      if (isDev) {
        if (data !== undefined) {
          console.warn(`[${component}] ${message}`, data);
        } else {
          console.warn(`[${component}] ${message}`);
        }
      }
    },
    
    /**
     * Log error messages
     * @param message The error message
     * @param error Optional error object
     */
    error: (message: string, error?: any) => {
      // Always log errors, even in production
      if (error !== undefined) {
        console.error(`[${component}] ${message}`, error);
      } else {
        console.error(`[${component}] ${message}`);
      }
    },
    
    /**
     * Log a function call with its parameters
     * @param functionName The name of the function
     * @param params The parameters passed to the function
     */
    call: (functionName: string, params?: any) => {
      if (isDev) {
        if (params !== undefined) {
          console.log(`[${component}] ${functionName}() called with:`, params);
        } else {
          console.log(`[${component}] ${functionName}() called`);
        }
      }
    }
  };
}; 