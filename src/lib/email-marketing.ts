import supabaseAdmin from '@/lib/supabaseAdmin';
import { emailNotificationService } from '@/lib/email-notifications';

export interface Campaign {
  id: string;
  name: string;
  subject: string;
  content: string;
  template_type: 'promotional' | 'newsletter' | 'announcement' | 'welcome';
  target_audience: 'all_users' | 'customers' | 'subscribers' | 'custom';
  scheduled_at?: string;
  sent_at?: string;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'cancelled';
  created_at: string;
  updated_at: string;
  stats?: {
    total_recipients: number;
    sent_count: number;
    failed_count: number;
  };
}

export interface CampaignRecipient {
  id: string;
  campaign_id: string;
  user_id: string;
  email: string;
  status: 'pending' | 'sent' | 'failed';
  sent_at?: string;
  error_message?: string;
}

export class EmailMarketingService {
  private supabase;

  constructor() {
    this.supabase = supabaseAdmin;
  }

  // Create a new email campaign
  async createCampaign(campaignData: {
    name: string;
    subject: string;
    content: string;
    template_type: 'promotional' | 'newsletter' | 'announcement' | 'welcome';
    target_audience: 'all_users' | 'customers' | 'subscribers' | 'custom';
    custom_recipients?: string[];
    scheduled_at?: string;
  }): Promise<Campaign | null> {
    try {
      console.log('Creating email campaign:', campaignData.name);

      // Insert campaign
      const { data: campaign, error: campaignError } = await (this.supabase as any)
        .from('email_campaigns')
        .insert({
          name: campaignData.name,
          subject: campaignData.subject,
          content: campaignData.content,
          template_type: campaignData.template_type,
          target_audience: campaignData.target_audience,
          scheduled_at: campaignData.scheduled_at,
          status: campaignData.scheduled_at ? 'scheduled' : 'draft'
        })
        .select()
        .single();

      if (campaignError) {
        console.error('Error creating campaign:', campaignError);
        return null;
      }

      // Add recipients based on target audience
      await this.addRecipientsToEmail((campaign as any).id, campaignData.target_audience, campaignData.custom_recipients);

      console.log('Campaign created successfully:', (campaign as any).id);
      return campaign as Campaign;
    } catch (error) {
      console.error('Error in createCampaign:', error);
      return null;
    }
  }

  // Add recipients to a campaign based on target audience
  private async addRecipientsToEmail(
    campaignId: string,
    targetAudience: string,
    customRecipients?: string[]
  ): Promise<void> {
    try {
      let recipients: Array<{ email: string; user_id?: string }> = [];

      switch (targetAudience) {
        case 'all_users':
          // Get all users with email addresses
          const { data: allUsers, error: allUsersError } = await this.supabase
            .from('profiles')
            .select('id, email')
            .not('email', 'is', null);

          if (allUsersError) throw allUsersError;
          recipients = allUsers?.map(user => ({ email: user.email, user_id: user.id })) || [];
          break;

        case 'customers':
          // Get users who have made orders - first get order user IDs
          const { data: orderUsers, error: orderUsersError } = await this.supabase
            .from('orders')
            .select('user_id')
            .not('user_id', 'is', null);

          if (orderUsersError) throw orderUsersError;
          
          const userIds = [...new Set(orderUsers?.map(order => order.user_id).filter((id): id is string => Boolean(id)) || [])];
          
          if (userIds.length > 0) {
            const { data: customers, error: customersError } = await this.supabase
              .from('profiles')
              .select('id, email')
              .not('email', 'is', null)
              .in('id', userIds);

            if (customersError) throw customersError;
            recipients = customers?.map(user => ({ email: user.email, user_id: user.id })) || [];
          }
          break;

        case 'subscribers':
          // Get users who have opted in for marketing emails
          const { data: subscribers, error: subscribersError } = await this.supabase
            .from('profiles')
            .select('id, email')
            .not('email', 'is', null)
            .eq('marketing_emails_enabled', true);

          if (subscribersError) throw subscribersError;
          recipients = subscribers?.map(user => ({ email: user.email, user_id: user.id })) || [];
          break;

        case 'custom':
          if (customRecipients && customRecipients.length > 0) {
            recipients = customRecipients.map(email => ({ email }));
          }
          break;
      }

      // Insert recipients into campaign_recipients table
      if (recipients.length > 0) {
        const recipientData = recipients.map(recipient => ({
          campaign_id: campaignId,
          user_id: recipient.user_id || null,
          email: recipient.email,
          status: 'pending' as const
        }));

        const { error: recipientsError } = await (this.supabase as any)
          .from('campaign_recipients')
          .insert(recipientData);

        if (recipientsError) throw recipientsError;

        console.log(`Added ${recipients.length} recipients to campaign ${campaignId}`);
      }
    } catch (error) {
      console.error('Error adding recipients to campaign:', error);
      throw error;
    }
  }

  // Send a campaign immediately
  async sendCampaign(campaignId: string): Promise<boolean> {
    try {
      console.log('Sending campaign:', campaignId);

      // Get campaign details
      const { data: campaign, error: campaignError } = await (this.supabase as any)
        .from('email_campaigns')
        .select('*')
        .eq('id', campaignId)
        .single();

      if (campaignError || !campaign) {
        console.error('Campaign not found:', campaignError);
        return false;
      }

      if ((campaign as any).status !== 'draft' && (campaign as any).status !== 'scheduled') {
        console.error('Campaign cannot be sent, status:', (campaign as any).status);
        return false;
      }

      // Update campaign status to sending
      await (this.supabase as any)
        .from('email_campaigns')
        .update({ status: 'sending' })
        .eq('id', campaignId);

      // Get recipients
      const { data: recipients, error: recipientsError } = await (this.supabase as any)
        .from('campaign_recipients')
        .select('*')
        .eq('campaign_id', campaignId)
        .eq('status', 'pending');

      if (recipientsError || !recipients) {
        console.error('Error fetching recipients:', recipientsError);
        return false;
      }

      console.log(`Sending to ${recipients.length} recipients`);

      // Send emails to all recipients
      let sentCount = 0;
      let failedCount = 0;

      for (const recipient of recipients) {
        try {
          const emailHtml = this.generateCampaignEmailTemplate(campaign, (recipient as any).email);

          const success = await emailNotificationService.getInstance()
            .sendUserNotification({
              to: (recipient as any).email,
              subject: (campaign as any).subject,
              message: emailHtml,
              type: (campaign as any).template_type
            });

          if (success) {
            // Update recipient status to sent
            await (this.supabase as any)
              .from('campaign_recipients')
              .update({
                status: 'sent',
                sent_at: new Date().toISOString()
              })
              .eq('id', (recipient as any).id);
            sentCount++;
          } else {
            // Update recipient status to failed
            await (this.supabase as any)
              .from('campaign_recipients')
              .update({
                status: 'failed',
                error_message: 'Email delivery failed'
              })
              .eq('id', (recipient as any).id);
            failedCount++;
          }
        } catch (error) {
          console.error(`Error sending to ${(recipient as any).email}:`, error);
          await (this.supabase as any)
            .from('campaign_recipients')
            .update({
              status: 'failed',
              error_message: error instanceof Error ? error.message : 'Unknown error'
            })
            .eq('id', (recipient as any).id);
          failedCount++;
        }
      }

      // Update campaign with final status and stats
      await (this.supabase as any)
        .from('email_campaigns')
        .update({
          status: 'sent',
          sent_at: new Date().toISOString(),
          stats: {
            total_recipients: recipients.length,
            sent_count: sentCount,
            failed_count: failedCount
          }
        })
        .eq('id', campaignId);

      console.log(`Campaign sent: ${sentCount} successful, ${failedCount} failed`);
      return true;
    } catch (error) {
      console.error('Error sending campaign:', error);
      
      // Update campaign status to failed
      await (this.supabase as any)
        .from('email_campaigns')
        .update({ status: 'draft' })
        .eq('id', campaignId);
      
      return false;
    }
  }

  // Generate email template for campaign
  private generateCampaignEmailTemplate(campaign: any, recipientEmail: string): string {
    const typeEmoji = this.getTypeEmoji(campaign.template_type);
    const typeColor = this.getTypeColor(campaign.template_type);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${campaign.subject}</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">🛍️ Treasures of Maimi</h1>
            <p style="margin: 8px 0 0 0; opacity: 0.9;">${this.getTemplateTypeLabel(campaign.template_type)}</p>
          </div>

          <!-- Content -->
          <div style="padding: 30px 20px;">
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
              <span style="font-size: 24px; margin-right: 10px;">${typeEmoji}</span>
              <div>
                <h2 style="margin: 0; color: ${typeColor}; font-size: 20px;">${campaign.subject}</h2>
              </div>
            </div>

            <div style="font-size: 16px; line-height: 1.6; color: #333;">
              ${campaign.content}
            </div>

            <div style="margin-top: 30px; text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL || 'https://treasuresofmaimi.com'}" 
                 style="display: inline-block; background-color: ${typeColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;">
                Shop Now
              </a>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="font-size: 14px; color: #666; margin: 0;">
                You received this email because you're subscribed to our updates.
                <br>
                <a href="${process.env.NEXT_PUBLIC_SITE_URL || 'https://treasuresofmaimi.com'}/unsubscribe?email=${encodeURIComponent(recipientEmail)}" style="color: #666;">Unsubscribe</a>
              </p>
            </div>
          </div>

          <!-- Footer -->
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
            <p style="margin: 0; font-size: 14px; color: #666;">
              © ${new Date().getFullYear()} Treasures of Maimi. All rights reserved.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Get type emoji
  private getTypeEmoji(type: string): string {
    switch (type) {
      case 'promotional': return '🎉';
      case 'newsletter': return '📧';
      case 'announcement': return '📢';
      case 'welcome': return '👋';
      default: return '📧';
    }
  }

  // Get type color
  private getTypeColor(type: string): string {
    switch (type) {
      case 'promotional': return '#fd7e14';
      case 'newsletter': return '#007bff';
      case 'announcement': return '#28a745';
      case 'welcome': return '#6f42c1';
      default: return '#007bff';
    }
  }

  // Get template type label
  private getTemplateTypeLabel(type: string): string {
    switch (type) {
      case 'promotional': return 'Special Offer';
      case 'newsletter': return 'Newsletter';
      case 'announcement': return 'Announcement';
      case 'welcome': return 'Welcome';
      default: return 'Newsletter';
    }
  }

  // Get all campaigns
  async getCampaigns(): Promise<Campaign[]> {
    try {
      const { data: campaigns, error } = await (this.supabase as any)
        .from('email_campaigns')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching campaigns:', error);
        return [];
      }

      return (campaigns || []) as Campaign[];
    } catch (error) {
      console.error('Error in getCampaigns:', error);
      return [];
    }
  }

  // Get campaign by ID
  async getCampaign(campaignId: string): Promise<Campaign | null> {
    try {
      const { data: campaign, error } = await (this.supabase as any)
        .from('email_campaigns')
        .select('*')
        .eq('id', campaignId)
        .single();

      if (error) {
        console.error('Error fetching campaign:', error);
        return null;
      }

      return campaign as Campaign;
    } catch (error) {
      console.error('Error in getCampaign:', error);
      return null;
    }
  }

  // Delete campaign
  async deleteCampaign(campaignId: string): Promise<boolean> {
    try {
      const { error } = await (this.supabase as any)
        .from('email_campaigns')
        .delete()
        .eq('id', campaignId);

      if (error) {
        console.error('Error deleting campaign:', error);
        return false;
      }

      console.log('Campaign deleted successfully:', campaignId);
      return true;
    } catch (error) {
      console.error('Error in deleteCampaign:', error);
      return false;
    }
  }
}

// Singleton instance
let _emailMarketingService: EmailMarketingService | null = null;

export const emailMarketingService = {
  getInstance(): EmailMarketingService {
    if (!_emailMarketingService) {
      _emailMarketingService = new EmailMarketingService();
    }
    return _emailMarketingService;
  }
};