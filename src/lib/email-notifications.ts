import supabaseAdmin from '@/lib/supabaseAdmin';

// Email configuration
const RESEND_API_KEY = process.env.RESEND_API_KEY;
const FROM_EMAIL = '<EMAIL>'; // Using Resend's default verified domain for testing

export interface EmailNotificationData {
  type: string;
  title: string;
  message: string;
  data?: any;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export class EmailNotificationService {
  private supabase;

  constructor() {
    this.supabase = supabaseAdmin;
  }

  // Send email notification to admin
  async sendAdminNotification(notification: EmailNotificationData): Promise<boolean> {
    try {
      console.log('Email Service: Starting sendAdminNotification with:', notification);

      // Get admin users with email addresses
      console.log('Fetching admin users with email addresses...');
      const { data: adminUsers, error: profileError } = await this.supabase
        .from('profiles')
        .select('id, email, first_name, last_name')
        .eq('is_admin', true)
        .not('email', 'is', null);

      console.log('Admin users query result:', { adminUsers, profileError });
      console.log('Number of admin users found:', adminUsers?.length || 0);

      if (profileError) {
        console.error('Error fetching admin profiles:', profileError);
        return false;
      }

      if (!adminUsers || adminUsers.length === 0) {
        console.log('No admin users found with email addresses');
        return false;
      }

      // Check notification preferences for each admin
      const adminUsersWithPrefs = [];
      for (const admin of adminUsers) {
        console.log('Checking preferences for admin:', admin.id, 'notification type:', notification.type);

        const { data: prefs, error: prefsError } = await this.supabase
          .from('admin_notification_preferences')
          .select('email_enabled')
          .eq('user_id', admin.id)
          .eq('notification_type', notification.type as any)
          .maybeSingle();

        console.log('Preferences result:', { prefs, prefsError });

        if (!prefsError && prefs && prefs.email_enabled) {
          adminUsersWithPrefs.push(admin);
        }
      }

      const finalAdminUsers = adminUsersWithPrefs;

      if (!finalAdminUsers || finalAdminUsers.length === 0) {
        console.log('No admin users found with email notifications enabled for type:', notification.type);
        return false;
      }

      // Send email to each admin
      const emailPromises = finalAdminUsers.map(admin =>
        this.sendEmail(
          admin.email,
          notification.title,
          this.generateEmailContent(notification, admin),
          notification.priority
        )
      );

      const results = await Promise.all(emailPromises);
      return results.some(result => result); // Return true if at least one succeeded

    } catch (error) {
      console.error('Error sending admin email notification:', error);
      return false;
    }
  }

  // Send individual email using Resend API
  private async sendEmail(
    to: string, 
    subject: string, 
    htmlContent: string, 
    priority: string = 'medium'
  ): Promise<boolean> {
    try {
      // Check if Resend API key is configured
      if (!RESEND_API_KEY) {
        console.error('Email Service: RESEND_API_KEY is not configured');
        return false;
      }

      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: FROM_EMAIL,
          to: to,
          subject: `[${priority.toUpperCase()}] ${subject}`,
          html: htmlContent,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Resend API error:', errorData);
        return false;
      }

      const responseData = await response.json();
      console.log('Email sent successfully:', responseData.id);
      return true;

    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  // Generate HTML email content based on notification type
  private generateEmailContent(notification: EmailNotificationData, admin: any): string {
    const adminName = admin.first_name || 'Admin';
    const priorityColor = this.getPriorityColor(notification.priority);
    const priorityEmoji = this.getPriorityEmoji(notification.priority);

    let actionButton = '';
    let additionalInfo = '';

    // Generate type-specific content
    switch (notification.type) {
      case 'new_order':
        actionButton = `<a href="https://treasuresofmaimi.com/admin/orders/${notification.data?.order_id}" 
                         style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 16px 0;">
                         View Order Details
                       </a>`;
        additionalInfo = `
          <div style="background-color: #f8f9fa; padding: 16px; border-radius: 8px; margin: 16px 0;">
            <h3 style="margin: 0 0 8px 0; color: #333;">Order Information:</h3>
            <p style="margin: 4px 0;"><strong>Order Number:</strong> ${notification.data?.order_number || 'N/A'}</p>
            <p style="margin: 4px 0;"><strong>Total Amount:</strong> €${notification.data?.total_amount || '0.00'}</p>
            <p style="margin: 4px 0;"><strong>Customer Email:</strong> ${notification.data?.customer_email || 'N/A'}</p>
          </div>
        `;
        break;

      case 'new_message':
        actionButton = `<a href="https://treasuresofmaimi.com/admin/messages" 
                         style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 16px 0;">
                         View Messages
                       </a>`;
        additionalInfo = `
          <div style="background-color: #f8f9fa; padding: 16px; border-radius: 8px; margin: 16px 0;">
            <h3 style="margin: 0 0 8px 0; color: #333;">Message Details:</h3>
            <p style="margin: 4px 0;"><strong>From:</strong> ${notification.data?.sender_name || 'Unknown'} (${notification.data?.sender_email || 'N/A'})</p>
            <p style="margin: 4px 0;"><strong>Subject:</strong> ${notification.data?.subject || 'N/A'}</p>
            <p style="margin: 8px 0 0 0;"><strong>Message:</strong></p>
            <div style="background-color: white; padding: 12px; border-left: 4px solid #007bff; margin: 8px 0;">
              ${notification.data?.message || 'No message content'}
            </div>
          </div>
        `;
        break;

      case 'low_inventory':
        actionButton = `<a href="https://treasuresofmaimi.com/admin/products/${notification.data?.product_id}" 
                         style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 16px 0;">
                         View Product
                       </a>`;
        break;

      case 'new_bag_request':
        actionButton = `<a href="https://treasuresofmaimi.com/admin/bag-requests" 
                         style="background-color: #6f42c1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 16px 0;">
                         View Bag Requests
                       </a>`;
        break;
    }

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${notification.title}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
          
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">🛍️ Treasures of Maimi</h1>
            <p style="margin: 8px 0 0 0; opacity: 0.9;">Admin Notification</p>
          </div>

          <!-- Content -->
          <div style="padding: 30px 20px;">
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
              <span style="font-size: 24px; margin-right: 10px;">${priorityEmoji}</span>
              <div>
                <h2 style="margin: 0; color: ${priorityColor}; font-size: 20px;">${notification.title}</h2>
                <span style="background-color: ${priorityColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                  ${notification.priority || 'medium'} priority
                </span>
              </div>
            </div>

            <p style="font-size: 16px; margin-bottom: 20px;">Hi ${adminName},</p>
            
            <p style="font-size: 16px; margin-bottom: 20px;">${notification.message}</p>

            ${additionalInfo}

            ${actionButton}

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="font-size: 14px; color: #666; margin: 0;">
                This notification was sent automatically from your Treasures of Maimi admin panel.
                <br>
                You can manage your notification preferences in the admin settings.
              </p>
            </div>
          </div>

          <!-- Footer -->
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
            <p style="margin: 0; font-size: 14px; color: #666;">
              © ${new Date().getFullYear()} Treasures of Maimi. All rights reserved.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Get priority color for styling
  private getPriorityColor(priority?: string): string {
    switch (priority) {
      case 'urgent': return '#dc3545';
      case 'high': return '#fd7e14';
      case 'medium': return '#007bff';
      case 'low': return '#28a745';
      default: return '#007bff';
    }
  }

  // Get priority emoji
  private getPriorityEmoji(priority?: string): string {
    switch (priority) {
      case 'urgent': return '🚨';
      case 'high': return '⚠️';
      case 'medium': return '📢';
      case 'low': return 'ℹ️';
      default: return '📢';
    }
  }

  // Send email notification to user
  async sendUserNotification(notification: {
    to: string;
    subject: string;
    message: string;
    type?: string;
  }): Promise<boolean> {
    try {
      console.log('Email Service: Sending user notification to:', notification.to);

      // Check if Resend API key is configured
      if (!RESEND_API_KEY) {
        console.error('Email Service: RESEND_API_KEY is not configured');
        return false;
      }

      const emailHtml = this.generateUserEmailTemplate(notification);

      // Send email using Resend API
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RESEND_API_KEY}`,
        },
        body: JSON.stringify({
          from: FROM_EMAIL,
          to: [notification.to],
          subject: notification.subject,
          html: emailHtml,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Failed to send user email via Resend:', errorText);
        return false;
      }

      const result = await response.json();
      console.log('User email sent successfully via Resend:', result);
      return true;
    } catch (error) {
      console.error('Error sending user email notification:', error);
      return false;
    }
  }

  // Generate HTML template for user notifications
  private generateUserEmailTemplate(notification: {
    to: string;
    subject: string;
    message: string;
    type?: string;
  }): string {
    const typeEmoji = this.getTypeEmoji(notification.type);
    const typeColor = this.getTypeColor(notification.type);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${notification.subject}</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">🛍️ Treasures of Maimi</h1>
            <p style="margin: 8px 0 0 0; opacity: 0.9;">Update Notification</p>
          </div>

          <!-- Content -->
          <div style="padding: 30px 20px;">
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
              <span style="font-size: 24px; margin-right: 10px;">${typeEmoji}</span>
              <div>
                <h2 style="margin: 0; color: ${typeColor}; font-size: 20px;">${notification.subject}</h2>
              </div>
            </div>

            <p style="font-size: 16px; margin-bottom: 20px;">Hello,</p>
            
            <p style="font-size: 16px; margin-bottom: 20px;">${notification.message}</p>

            <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 6px; border-left: 4px solid ${typeColor};">
              <p style="margin: 0; font-size: 14px; color: #666;">
                You can view more details and manage your preferences in your account dashboard.
              </p>
            </div>

            <div style="margin-top: 30px; text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL || 'https://treasuresofmaimi.com'}/account" 
                 style="display: inline-block; background-color: ${typeColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;">
                View Account
              </a>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="font-size: 14px; color: #666; margin: 0;">
                This notification was sent from Treasures of Maimi.
                <br>
                You can manage your notification preferences in your account settings.
              </p>
            </div>
          </div>

          <!-- Footer -->
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
            <p style="margin: 0; font-size: 14px; color: #666;">
              © ${new Date().getFullYear()} Treasures of Maimi. All rights reserved.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Get type emoji for user notifications
  private getTypeEmoji(type?: string): string {
    switch (type) {
      case 'order_update': return '📦';
      case 'bag_request_update': return '👜';
      case 'welcome': return '👋';
      case 'promotional': return '🎉';
      default: return '📢';
    }
  }

  // Get type color for user notifications
  private getTypeColor(type?: string): string {
    switch (type) {
      case 'order_update': return '#007bff';
      case 'bag_request_update': return '#28a745';
      case 'welcome': return '#6f42c1';
      case 'promotional': return '#fd7e14';
      default: return '#007bff';
    }
  }
}

// Singleton instance
let _emailNotificationService: EmailNotificationService | null = null;

export const emailNotificationService = {
  getInstance(): EmailNotificationService {
    if (!_emailNotificationService) {
      _emailNotificationService = new EmailNotificationService();
    }
    return _emailNotificationService;
  }
};
