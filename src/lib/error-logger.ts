'use client';

import * as Sentry from '@sentry/nextjs';

interface ErrorDetails {
  message: string;
  stack?: string;
  componentStack?: string;
  type?: string;
  timestamp: string;
  url: string;
  userAgent: string;
  deviceInfo: {
    platform: string;
    viewport: string;
    memory?: string;
    connection?: string;
    storageAvailable: boolean;
  };
}

class ErrorLogger {
  private static instance: ErrorLogger;
  private isInitialized = false;

  private constructor() {
    if (typeof window !== 'undefined') {
      this.setupGlobalHandlers();
    }
  }

  public static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger();
    }
    return ErrorLogger.instance;
  }

  private setupGlobalHandlers() {
    if (this.isInitialized) return;

    // Handle uncaught errors
    window.onerror = (message, source, lineno, colno, error) => {
      this.logError({
        message: message.toString(),
        stack: error?.stack,
        type: 'uncaught-error',
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: window.navigator.userAgent,
        deviceInfo: this.getDeviceInfo()
      });
    };

    // Handle unhandled promise rejections
    window.onunhandledrejection = (event) => {
      this.logError({
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        type: 'unhandled-rejection',
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: window.navigator.userAgent,
        deviceInfo: this.getDeviceInfo()
      });
    };

    // Handle React render errors
    window.addEventListener('error', (event) => {
      if (event.error?.name === 'ChunkLoadError') {
        this.logError({
          message: 'Failed to load JavaScript chunk',
          type: 'chunk-load-error',
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: window.navigator.userAgent,
          deviceInfo: this.getDeviceInfo()
        });
      }
    });

    this.isInitialized = true;
  }

  private getDeviceInfo() {
    const deviceInfo = {
      platform: window.navigator.platform,
      viewport: `${window.innerWidth}x${window.innerHeight}`,
      memory: (navigator as any).deviceMemory,
      connection: (navigator as any).connection?.effectiveType,
      storageAvailable: false
    };

    try {
      const testKey = '_test_storage_';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      deviceInfo.storageAvailable = true;
    } catch (e) {
      deviceInfo.storageAvailable = false;
    }

    return deviceInfo;
  }

  public logError(errorDetails: ErrorDetails) {
    // Log to console for development
    console.error('🚨 Error caught:', errorDetails);

    // Send to Sentry
    Sentry.withScope((scope) => {
      scope.setExtra('errorDetails', errorDetails);
      scope.setTag('errorType', errorDetails.type || 'unknown');
      scope.setTag('deviceInfo', JSON.stringify(errorDetails.deviceInfo));
      
      if (errorDetails.message.includes('localStorage')) {
        scope.setTag('storage', 'localStorage-error');
      }
      
      if (errorDetails.message.includes('hydration')) {
        scope.setTag('react', 'hydration-error');
      }

      Sentry.captureException(new Error(errorDetails.message));
    });

    // You can add more logging destinations here (e.g., your own API endpoint)
  }

  public init() {
    if (typeof window !== 'undefined' && !this.isInitialized) {
      this.setupGlobalHandlers();
    }
  }
}

export const errorLogger = ErrorLogger.getInstance();

// Initialize immediately if in browser
if (typeof window !== 'undefined') {
  errorLogger.init();
} 