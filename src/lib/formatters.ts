/**
 * Utility functions for formatting various data types
 */

/**
 * Format a number as currency (USD by default)
 * @param amount - The amount to format
 * @param currency - The currency code (default: 'USD')
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number | null | undefined, currency = 'USD'): string => {
  if (amount === null || amount === undefined) return '$0.00';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Format a date string to a readable format
 * @param dateString - ISO date string
 * @param options - Format options
 * @returns Formatted date string
 */
export const formatDate = (
  dateString: string | null | undefined,
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  }
): string => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', options).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

/**
 * Format a product condition to a readable string
 * @param conditionId - The condition ID or code
 * @returns Readable condition string
 */
export const formatCondition = (conditionId: string | number | null | undefined): string => {
  if (conditionId === null || conditionId === undefined) return 'Unknown';
  
  const conditions: Record<string, string> = {
    '1': 'New',
    '2': 'Like New',
    '3': 'Very Good',
    '4': 'Good',
    '5': 'Acceptable',
    'new': 'New',
    'like_new': 'Like New',
    'very_good': 'Very Good',
    'good': 'Good',
    'acceptable': 'Acceptable'
  };
  
  return conditions[conditionId.toString()] || 'Unknown';
};

/**
 * Format a number with commas for thousands
 * @param num - The number to format
 * @returns Formatted number string
 */
export const formatNumber = (num: number | null | undefined): string => {
  if (num === null || num === undefined) return '0';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};
