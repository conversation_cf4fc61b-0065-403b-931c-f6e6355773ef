import { Database } from './database.types';

/**
 * Formats a Cloudinary URL consistently across the application
 * @param url The URL or path to format
 * @returns A properly formatted Cloudinary URL or empty string if no URL provided
 */
export const getCloudinaryUrl = (url: string | null | undefined): string => {
  if (!url) return '';

  // If the URL is already a full Cloudinary URL, return it as is
  if (url.includes('res.cloudinary.com')) {
    return url;
  }

  // Remove leading slash if present
  const cleanUrl = url.startsWith('/') ? url.slice(1) : url;

  // Format as a Cloudinary URL with the correct cloud name
  return `https://res.cloudinary.com/treasures-of-maimi/image/upload/v1/${cleanUrl}`;
};

/**
 * Gets the image URL from a product media item
 * @param mediaItems Array of media items from the database
 * @returns Formatted Cloudinary URL or empty string
 */
export const getProductImageUrl = (
  mediaItems?: Database['public']['Tables']['product_media']['Row'][] | null | { url: string }[]
): string => {
  if (!mediaItems || mediaItems.length === 0) return '';
  return getCloudinaryUrl(mediaItems[0]?.url);
};
