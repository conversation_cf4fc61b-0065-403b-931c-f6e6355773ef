/**
 * Input validation utilities for Shop Maimi
 * Provides sanitization and validation for user inputs to prevent security vulnerabilities
 */

// Email validation
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 255;
}

// Product validation
export function validateProduct(product: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!product.name || typeof product.name !== 'string' || product.name.trim().length === 0 || product.name.length > 255) {
    errors.push('Product name is required and must be 1-255 characters');
  }
  
  if (product.price === undefined || typeof product.price !== 'number' || product.price < 0 || product.price > 99999.99) {
    errors.push('Product price must be a valid number between 0 and 99999.99');
  }
  
  if (product.description && (typeof product.description !== 'string' || product.description.length > 5000)) {
    errors.push('Product description must be a string with max 5000 characters');
  }
  
  const validConditions = ['excellent', 'very_good', 'good', 'fair'];
  if (product.condition && !validConditions.includes(product.condition)) {
    errors.push('Product condition must be one of: excellent, very_good, good, fair');
  }
  
  if (product.quantity !== undefined && (typeof product.quantity !== 'number' || product.quantity < 0 || product.quantity > 1000)) {
    errors.push('Product quantity must be a number between 0 and 1000');
  }
  
  return { valid: errors.length === 0, errors };
}

// Order validation
export function validateOrder(order: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!order.items || !Array.isArray(order.items) || order.items.length === 0 || order.items.length > 50) {
    errors.push('Order must have 1-50 items');
  } else {
    order.items.forEach((item: any, index: number) => {
      if (!isValidUUID(item.product_id)) {
        errors.push(`Item ${index + 1}: Invalid product ID`);
      }
      if (typeof item.quantity !== 'number' || item.quantity < 1 || item.quantity > 10) {
        errors.push(`Item ${index + 1}: Quantity must be between 1 and 10`);
      }
    });
  }
  
  if (order.customer_email && !validateEmail(order.customer_email)) {
    errors.push('Invalid customer email');
  }
  
  return { valid: errors.length === 0, errors };
}

// Search validation
export function validateSearchParams(params: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (params.query && (typeof params.query !== 'string' || params.query.length > 100)) {
    errors.push('Search query must be a string with max 100 characters');
  }
  
  if (params.min_price !== undefined && (typeof params.min_price !== 'number' || params.min_price < 0)) {
    errors.push('Min price must be a positive number');
  }
  
  if (params.max_price !== undefined && (typeof params.max_price !== 'number' || params.max_price < 0)) {
    errors.push('Max price must be a positive number');
  }
  
  const validSorts = ['price_asc', 'price_desc', 'newest', 'oldest', 'name_asc', 'name_desc'];
  if (params.sort && !validSorts.includes(params.sort)) {
    errors.push('Invalid sort parameter');
  }
  
  return { valid: errors.length === 0, errors };
}

// Discount code validation
export function validateDiscountCode(code: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!code.code || typeof code.code !== 'string' || code.code.trim().length === 0 || code.code.length > 50) {
    errors.push('Discount code is required and must be 1-50 characters');
  }
  
  const validTypes = ['percentage', 'fixed_amount'];
  if (!validTypes.includes(code.discount_type)) {
    errors.push('Discount type must be either "percentage" or "fixed_amount"');
  }
  
  if (typeof code.discount_value !== 'number' || code.discount_value <= 0 || code.discount_value > 999999.99) {
    errors.push('Discount value must be a positive number');
  }
  
  if (code.discount_type === 'percentage' && code.discount_value > 100) {
    errors.push('Percentage discount cannot exceed 100%');
  }
  
  return { valid: errors.length === 0, errors };
}

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export function sanitizeHtml(input: string): string {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Sanitize SQL-like input to prevent injection
 */
export function sanitizeSqlInput(input: string): string {
  return input
    .replace(/['";\\]/g, '') // Remove dangerous SQL characters
    .replace(/--/g, '') // Remove SQL comments
    .replace(/\/\*/g, '') // Remove SQL block comment start
    .replace(/\*\//g, '') // Remove SQL block comment end
    .trim();
}

/**
 * Validate and sanitize search query
 */
export function sanitizeSearchQuery(query: string): string {
  return query
    .trim()
    .substring(0, 100) // Limit length
    .replace(/[<>'"]/g, '') // Remove potentially dangerous characters
    .replace(/\s+/g, ' '); // Normalize whitespace
}

/**
 * Rate limiting helper - simple in-memory store
 * In production, use Redis or similar persistent store
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string, 
  maxRequests: number = 100, 
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): { allowed: boolean; remainingRequests: number; resetTime: number } {
  const now = Date.now();
  const record = rateLimitStore.get(identifier);
  
  if (!record || now > record.resetTime) {
    // First request or window expired
    const resetTime = now + windowMs;
    rateLimitStore.set(identifier, { count: 1, resetTime });
    return { allowed: true, remainingRequests: maxRequests - 1, resetTime };
  }
  
  if (record.count >= maxRequests) {
    // Rate limit exceeded
    return { allowed: false, remainingRequests: 0, resetTime: record.resetTime };
  }
  
  // Increment count
  record.count++;
  rateLimitStore.set(identifier, record);
  
  return { 
    allowed: true, 
    remainingRequests: maxRequests - record.count, 
    resetTime: record.resetTime 
  };
}

/**
 * Get client IP address from request
 */
export function getClientIP(request: Request): string {
  // Check various headers for real IP
  const headers = request.headers;
  return (
    headers.get('x-forwarded-for')?.split(',')[0].trim() ||
    headers.get('x-real-ip') ||
    headers.get('cf-connecting-ip') ||
    headers.get('x-client-ip') ||
    'unknown'
  );
}

/**
 * Validate UUID format
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Validate and normalize price
 */
export function validatePrice(price: any): number {
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  
  if (isNaN(numPrice) || numPrice < 0 || numPrice > 99999.99) {
    throw new Error('Invalid price value');
  }
  
  return Math.round(numPrice * 100) / 100; // Round to 2 decimal places
}

/**
 * Validate and normalize quantity
 */
export function validateQuantity(quantity: any): number {
  const numQuantity = typeof quantity === 'string' ? parseInt(quantity) : quantity;
  
  if (isNaN(numQuantity) || numQuantity < 1 || numQuantity > 1000) {
    throw new Error('Invalid quantity value');
  }
  
  return numQuantity;
}

// Error types for consistent error handling
export class ValidationError extends Error {
  constructor(public field: string, message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class RateLimitError extends Error {
  constructor(public resetTime: number) {
    super('Rate limit exceeded');
    this.name = 'RateLimitError';
  }
}