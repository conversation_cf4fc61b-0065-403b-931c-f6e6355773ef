import { createClient } from '@/lib/supabase-browser';

export interface NotificationData {
  id: string;
  type: string;
  title: string;
  message: string;
  data: Record<string, any>;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed';
  created_at: string;
  read_at?: string;
}

export interface PushSubscription {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}

class NotificationService {
  private supabase = createClient();
  private swRegistration: ServiceWorkerRegistration | null = null;

  // Initialize service worker and request permissions
  async initialize(): Promise<boolean> {
    try {
      // Check if service workers are supported
      if (!('serviceWorker' in navigator)) {
        console.warn('Service workers not supported');
        return false;
      }

      // Check if push messaging is supported
      if (!('PushManager' in window)) {
        console.warn('Push messaging not supported');
        return false;
      }

      // Register service worker
      this.swRegistration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered:', this.swRegistration);

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage);

      return true;
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
      return false;
    }
  }

  // Request notification permission
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('Notifications not supported');
      return 'denied';
    }

    let permission = Notification.permission;

    if (permission === 'default') {
      permission = await Notification.requestPermission();
    }

    return permission;
  }

  // Subscribe to push notifications
  async subscribeToPush(): Promise<PushSubscription | null> {
    try {
      if (!this.swRegistration) {
        throw new Error('Service worker not registered');
      }

      const permission = await this.requestPermission();
      if (permission !== 'granted') {
        throw new Error('Notification permission denied');
      }

      // Get VAPID public key from environment
      const vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;
      
      if (!vapidPublicKey) {
        throw new Error('VAPID public key not configured');
      }

      const subscription = await this.swRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(vapidPublicKey)
      });

      // Save subscription to database
      await this.savePushSubscription(subscription);

      return {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: this.arrayBufferToBase64(subscription.getKey('auth')!)
        }
      };
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  }

  // Save push subscription to database
  private async savePushSubscription(subscription: globalThis.PushSubscription): Promise<void> {
    const { data: { user } } = await this.supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const subscriptionData = {
      user_id: user.id,
      endpoint: subscription.endpoint,
      p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
      auth: this.arrayBufferToBase64(subscription.getKey('auth')!),
      user_agent: navigator.userAgent
    };

    const { error } = await this.supabase
      .from('push_subscriptions')
      .upsert(subscriptionData, { onConflict: 'user_id,endpoint' });

    if (error) {
      console.error('Failed to save push subscription:', error);
      throw error;
    }
  }

  // Get user notifications
  async getNotifications(limit = 50): Promise<NotificationData[]> {
    const { data, error } = await this.supabase
      .from('notifications')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Failed to fetch notifications:', error);
      return [];
    }

    return data || [];
  }

  // Get unread notification count
  async getUnreadCount(): Promise<number> {
    const { data, error } = await this.supabase.rpc('get_unread_notification_count');
    
    if (error) {
      console.error('Failed to get unread count:', error);
      return 0;
    }

    return data || 0;
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<boolean> {
    const { data, error } = await this.supabase.rpc('mark_notification_read', {
      p_notification_id: notificationId
    });

    if (error) {
      console.error('Failed to mark notification as read:', error);
      return false;
    }

    return data || false;
  }

  // Subscribe to real-time notifications
  subscribeToNotifications(callback: (notification: NotificationData) => void) {
    let subscription: any = null;

    // Get current user ID asynchronously
    this.supabase.auth.getUser().then(({ data: { user } }: { data: { user: any } }) => {
      if (!user) return;

      // Create a unique channel name to avoid conflicts
      const channelName = `notifications_${user.id}_${Date.now()}`;

      subscription = this.supabase
        .channel(channelName)
        .on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${user.id}`
          },
          (payload: any) => {
            console.log('New notification received:', payload);
            callback(payload.new as NotificationData);
          }
        )
        .subscribe();
    });

    // Return subscription with proper unsubscribe
    return {
      unsubscribe: () => {
        if (subscription) {
          subscription.unsubscribe();
        }
      }
    };
  }

  // Handle service worker messages
  private handleServiceWorkerMessage = (event: MessageEvent) => {
    console.log('Message from service worker:', event.data);
    
    if (event.data.type === 'NOTIFICATION_CLICKED') {
      // Handle notification click
      window.location.href = event.data.url;
    }
  };

  // Utility functions
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }


}

export const notificationService = new NotificationService();
