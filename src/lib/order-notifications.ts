import { createClient } from './supabase-browser';

// Define interfaces for type safety
interface OrderProfile {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
}

interface OrderWithTracking {
  id: string;
  order_number: string;
  tracking_number: string;
  carrier?: string;
  tracking_url?: string;
  profiles?: OrderProfile;
}

/**
 * Send a tracking notification email to the customer
 * @param orderId The ID of the order
 */
export async function sendTrackingNotification(orderId: string): Promise<boolean> {
  try {
    const supabase = createClient();
    
    // Fetch order details with customer information
    const { data, error } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        tracking_number,
        carrier,
        tracking_url,
        profiles:user_id (
          id,
          email,
          first_name,
          last_name
        )
      `)
      .eq('id', orderId)
      .single();
    
    if (error) {
      console.error('Error fetching order for notification:', error);
      return false;
    }
    
    // Make sure data exists and is not an error object
    if (!data) {
      console.error('No order data found');
      return false;
    }
    
    const order = data as unknown as OrderWithTracking;
    
    if (!order || !order.tracking_number) {
      console.error('Order not found or missing tracking information');
      return false;
    }
    
    const customerEmail = order.profiles?.email;
    const customerName = order.profiles?.first_name || 'Customer';
    
    if (!customerEmail) {
      console.error('Customer email not found');
      return false;
    }
    
    // Create email content
    const subject = `Your Order #${order.order_number} Has Been Shipped!`;
    
    let trackingInfo = `Tracking Number: ${order.tracking_number}`;
    if (order.carrier) {
      trackingInfo += `\nCarrier: ${order.carrier}`;
    }
    if (order.tracking_url) {
      trackingInfo += `\nTrack your shipment: ${order.tracking_url}`;
    }
    
    const emailContent = `
      Hi ${customerName},
      
      Great news! Your order #${order.order_number} has been shipped!
      
      ${trackingInfo}
      
      If you have any questions about your order, please don't hesitate to contact us.
      
      Thanks for shopping with Treasures of Maimi!
      
      Best regards,
      The Treasures of Maimi Team
    `;
    
    // Send the email
    // This is a placeholder - replace with your actual email sending implementation
    console.log('Would send email to:', customerEmail);
    console.log('Subject:', subject);
    console.log('Content:', emailContent);
    
    // Update the order to mark notification as sent
    const { error: updateError } = await supabase
      .from('orders')
      .update({
        updated_at: new Date().toISOString(),
        notes: JSON.stringify({
          notification_sent: true,
          notification_sent_at: new Date().toISOString()
        })
      })
      .eq('id', orderId);
    
    if (updateError) {
      console.error('Error updating notification status:', updateError);
    }
    
    return true;
  } catch (error) {
    console.error('Error sending tracking notification:', error);
    return false;
  }
}

/**
 * Generate a tracking URL based on carrier and tracking number
 * @param carrier The shipping carrier
 * @param trackingNumber The tracking number
 */
export function generateTrackingUrl(carrier: string, trackingNumber: string): string {
  // Map of common carriers and their tracking URL formats
  const carrierUrls: Record<string, string> = {
    'ValenciaPost': `https://valenciapost.com/track/${trackingNumber}`,
    'MaimiDispatch': `https://maimidispatch.com/tracking?number=${trackingNumber}`,
    'DHL': `https://www.dhl.com/en/express/tracking.html?AWB=${trackingNumber}`,
    'FedEx': `https://www.fedex.com/fedextrack/?trknbr=${trackingNumber}`,
    'UPS': `https://www.ups.com/track?tracknum=${trackingNumber}`,
    'USPS': `https://tools.usps.com/go/TrackConfirmAction?tLabels=${trackingNumber}`,
  };
  
  // Return the URL for the specified carrier, or a generic one if not found
  return carrierUrls[carrier] || `https://www.google.com/search?q=${carrier}+tracking+${trackingNumber}`;
}
