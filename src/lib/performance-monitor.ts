/**
 * Performance monitoring utilities for tracking and optimizing app performance
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'navigation' | 'resource' | 'custom';
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'development' || process.env.ENABLE_PERFORMANCE_MONITORING === 'true';
    
    if (this.isEnabled && typeof window !== 'undefined') {
      this.initializeMonitoring();
    }
  }

  private initializeMonitoring() {
    // Monitor Core Web Vitals
    this.observeWebVitals();
    
    // Monitor resource loading
    this.observeResourceTiming();
    
    // Monitor navigation timing
    this.observeNavigationTiming();
    
    // Monitor long tasks
    this.observeLongTasks();
  }

  private observeWebVitals() {
    // Largest Contentful Paint (LCP)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.recordMetric('LCP', lastEntry.startTime, 'navigation');
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay (FID)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        this.recordMetric('FID', entry.processingStart - entry.startTime, 'navigation');
      });
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      this.recordMetric('CLS', clsValue, 'navigation');
    }).observe({ entryTypes: ['layout-shift'] });
  }

  private observeResourceTiming() {
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        // Track slow resources (>1s)
        if (entry.duration > 1000) {
          this.recordMetric(`Slow Resource: ${entry.name}`, entry.duration, 'resource');
        }
      });
    }).observe({ entryTypes: ['resource'] });
  }

  private observeNavigationTiming() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      this.recordMetric('DNS Lookup', navigation.domainLookupEnd - navigation.domainLookupStart, 'navigation');
      this.recordMetric('TCP Connection', navigation.connectEnd - navigation.connectStart, 'navigation');
      this.recordMetric('Request', navigation.responseStart - navigation.requestStart, 'navigation');
      this.recordMetric('Response', navigation.responseEnd - navigation.responseStart, 'navigation');
      this.recordMetric('DOM Processing', navigation.domComplete - navigation.domContentLoadedEventStart, 'navigation');
      this.recordMetric('Load Complete', navigation.loadEventEnd - navigation.loadEventStart, 'navigation');
    });
  }

  private observeLongTasks() {
    if ('PerformanceObserver' in window) {
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.recordMetric('Long Task', entry.duration, 'custom');
          console.warn(`🐌 Long task detected: ${entry.duration}ms`);
        });
      }).observe({ entryTypes: ['longtask'] });
    }
  }

  private recordMetric(name: string, value: number, type: PerformanceMetric['type']) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type,
    };

    this.metrics.push(metric);

    // Log performance issues in development
    if (process.env.NODE_ENV === 'development') {
      if (type === 'navigation') {
        if (name === 'LCP' && value > 2500) {
          console.warn(`🚨 Poor LCP: ${value}ms (should be < 2.5s)`);
        }
        if (name === 'FID' && value > 100) {
          console.warn(`🚨 Poor FID: ${value}ms (should be < 100ms)`);
        }
        if (name === 'CLS' && value > 0.1) {
          console.warn(`🚨 Poor CLS: ${value} (should be < 0.1)`);
        }
      }
    }

    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
  }

  public startTimer(name: string): () => void {
    if (!this.isEnabled) return () => {};

    const startTime = performance.now();
    return () => {
      const duration = performance.now() - startTime;
      this.recordMetric(name, duration, 'custom');
      
      if (duration > 100) {
        console.warn(`⏱️ Slow operation: ${name} took ${duration.toFixed(2)}ms`);
      }
    };
  }

  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  public getMetricsByType(type: PerformanceMetric['type']): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.type === type);
  }

  public clearMetrics(): void {
    this.metrics = [];
  }

  public generateReport(): string {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      summary: {
        totalMetrics: this.metrics.length,
        navigationMetrics: this.getMetricsByType('navigation').length,
        resourceMetrics: this.getMetricsByType('resource').length,
        customMetrics: this.getMetricsByType('custom').length,
      },
    };

    return JSON.stringify(report, null, 2);
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for common performance measurements
export const measureAsync = async <T>(name: string, fn: () => Promise<T>): Promise<T> => {
  const endTimer = performanceMonitor.startTimer(name);
  try {
    const result = await fn();
    return result;
  } finally {
    endTimer();
  }
};

export const measureSync = <T>(name: string, fn: () => T): T => {
  const endTimer = performanceMonitor.startTimer(name);
  try {
    return fn();
  } finally {
    endTimer();
  }
};

// React hook for measuring component render time
export const useMeasureRender = (componentName: string) => {
  if (typeof window === 'undefined') return;

  const endTimer = performanceMonitor.startTimer(`${componentName} Render`);

  // Use useEffect to measure render completion
  if (typeof window !== 'undefined' && 'useEffect' in globalThis) {
    const { useEffect } = require('react');
    useEffect(() => {
      endTimer();
    });
  }
};

// Export types for external use
export type { PerformanceMetric };
