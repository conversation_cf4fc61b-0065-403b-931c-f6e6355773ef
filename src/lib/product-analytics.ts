import { createClient } from './supabase-browser';
import { debounce } from 'lodash';

// Types for analytics data
export type DailyProductView = {
  view_day: string;
  product_id: string;
  product_name: string;
  views: number;
};

export type TopViewedProduct = {
  id: string;
  name: string;
  views: number;
};

export type ViewsPerDay = {
  view_day: string;
  total_views: number;
};

// Add new types for product views
export type ProductViewWithUser = {
  id: string;
  product_id: string;
  product_name: string;
  product_image_url: string | null;
  viewed_at: string;
  user: {
    id: string;
    email: string;
    first_name: string | null;
    last_name: string | null;
  } | null;
};

/**
 * Track a product view in the database
 * @param productId The ID of the product being viewed
 */
export async function trackProductView(productId: string) {
  try {
    const supabase = createClient();

    // Get the current user session
    const { data: { session } } = await supabase.auth.getSession();
    const userId = session?.user?.id;

    const insertData: any = {
      product_id: productId,
      viewed_at: new Date().toISOString(),
    };

    // Only add user_id if the user is authenticated
    if (userId) {
      insertData.user_id = userId;
    }

    const { error } = await supabase.from('product_views').insert([insertData]);

    if (error) {
      console.error('Error tracking product view:', error);
    }
  } catch (error) {
    console.error('Unexpected error tracking product view:', error);
  }
}

/**
 * Get performance insights per product based on views and orders
 */
export async function getProductPerformanceInsights(): Promise<
  {
    id: string;
    name: string;
    views: number;
    orders: number;
    conversion_rate: number;
    insight: string;
  }[]
> {
  try {
    const supabase = createClient();

    // Fetch all product views
    const { data: viewsData, error: viewsError } = await supabase
      .from('product_views')
      .select('product_id');

    // Fetch all order items
    const { data: ordersData, error: ordersError } = await supabase
      .from('order_items')
      .select('product_id');

    // Fetch all products
    const { data: productsData, error: productsError } = await supabase
      .from('products')
      .select('id, name');

    if (viewsError || ordersError || productsError) {
      console.error('Error fetching insights data:', viewsError || ordersError || productsError);
      return [];
    }

    const viewsMap: Record<string, number> = {};
    viewsData?.forEach((view: any) => {
      if (view.product_id) {
        viewsMap[view.product_id] = (viewsMap[view.product_id] || 0) + 1;
      }
    });

    const ordersMap: Record<string, number> = {};
    ordersData?.forEach((order: any) => {
      if (order.product_id) {
        ordersMap[order.product_id] = (ordersMap[order.product_id] || 0) + 1;
      }
    });

    return (productsData || []).map((p: any) => {
      const views = viewsMap[p.id] || 0;
      const orders = ordersMap[p.id] || 0;
      const conversionRate = views > 0 ? (orders / views) * 100 : 0;

      let insight = '';
      if (views >= 30 && orders === 0) {
        insight = 'High interest, but no orders — consider updating price or description.';
      } else if (conversionRate >= 10) {
        insight = 'Strong performer with high conversion.';
      } else if (views > 0 && conversionRate < 2) {
        insight = 'Many views but low conversion — review product listing.';
      } else if (views === 0) {
        insight = 'No recent views — consider promoting.';
      }

      return {
        id: p.id,
        name: p.name,
        views,
        orders,
        conversion_rate: parseFloat(conversionRate.toFixed(2)),
        insight,
      };
    });
  } catch (error) {
    console.error('Failed to fetch product performance insights:', error);
    return [];
  }
}

/**
 * Debounced version of trackProductView to prevent multiple views
 * from being recorded when a user is browsing the same product
 */
export const debouncedTrackProductView = debounce(trackProductView, 5000, {
  leading: true,   // Track the view immediately when first seen
  trailing: false  // Don't track again at the end of the debounce period
});

/**
 * Get daily product views for the last 7 days
 * For creating line charts showing daily trends for top products
 */
export async function getDailyProductViews(): Promise<DailyProductView[]> {
  try {
    const supabase = createClient();
    
    // Use direct SQL query instead of RPC to avoid TypeScript errors
    const { data, error } = await supabase.from('product_views')
      .select(`
        product_id,
        products!product_views_product_id_fkey (
          id,
          name
        ),
        viewed_at
      `)
      .gte('viewed_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());
    
    if (error) {
      console.error('Error fetching daily product views:', error.message);
      return [];
    }
    
    if (!data || !Array.isArray(data)) return [];
    
    // Process the data to get daily views per product
    const viewsByDay = data.reduce((acc: Record<string, Record<string, { count: number, name: string }>>, view: any) => {
      const day = new Date(view.viewed_at).toISOString().split('T')[0];
      const productId = view.product_id;
      const productName = view.products?.name || 'Unknown Product';
      
      if (!acc[day]) {
        acc[day] = {};
      }
      
      if (!acc[day][productId]) {
        acc[day][productId] = { count: 0, name: productName };
      }
      
      acc[day][productId].count++;
      
      return acc;
    }, {});
    
    // Convert to the expected format
    const result: DailyProductView[] = [];
    
    Object.entries(viewsByDay).forEach(([day, products]) => {
      Object.entries(products).forEach(([productId, data]) => {
        result.push({
          view_day: day,
          product_id: productId,
          product_name: data.name,
          views: data.count
        });
      });
    });
    
    return result;
  } catch (error) {
    console.error('Failed to get daily product views:', error);
    return [];
  }
}

/**
 * Get top viewed products for the specified number of days
 * @param days Number of days to look back (default: 7)
 */
export async function getTopViewedProducts(days: number = 7): Promise<TopViewedProduct[]> {
  try {
    const supabase = createClient();
    
    // Use direct SQL query instead of RPC to avoid TypeScript errors
    const { data, error } = await supabase.from('product_views')
      .select(`
        product_id,
        products!product_views_product_id_fkey (
          id,
          name
        )
      `)
      .gte('viewed_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString());
    
    if (error) {
      console.error('Error fetching top viewed products:', error.message);
      return [];
    }
    
    if (!data || !Array.isArray(data)) return [];
    
    // Count views per product
    const viewsByProduct = data.reduce((acc: Record<string, { id: string, name: string, count: number }>, view: any) => {
      const productId = view.product_id;
      const productName = view.products?.name || 'Unknown Product';
      
      if (!acc[productId]) {
        acc[productId] = { id: productId, name: productName, count: 0 };
      }
      
      acc[productId].count++;
      
      return acc;
    }, {});
    
    // Convert to array and sort by count
    const result = Object.values(viewsByProduct)
      .map(item => ({
        id: item.id,
        name: item.name,
        views: item.count
      }))
      .sort((a, b) => b.views - a.views);
    
    return result;
  } catch (error) {
    console.error('Failed to get top viewed products:', error);
    return [];
  }
}

/**
 * Get views per day for all products combined
 * @param days Number of days to look back (default: 14)
 */
export async function getViewsPerDay(days: number = 14): Promise<ViewsPerDay[]> {
  try {
    const supabase = createClient();
    
    // Use direct SQL query instead of RPC to avoid TypeScript errors
    const { data, error } = await supabase.from('product_views')
      .select('viewed_at')
      .gte('viewed_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString());
    
    if (error) {
      console.error('Error fetching views per day:', error.message);
      return [];
    }
    
    if (!data || !Array.isArray(data)) return [];
    
    // Count views per day
    const viewsByDay = data.reduce((acc: Record<string, number>, view: any) => {
      const day = new Date(view.viewed_at).toISOString().split('T')[0];
      
      if (!acc[day]) {
        acc[day] = 0;
      }
      
      acc[day]++;
      
      return acc;
    }, {});
    
    // Convert to array and sort by day
    const result = Object.entries(viewsByDay)
      .map(([day, count]) => ({
        view_day: day,
        total_views: count
      }))
      .sort((a, b) => a.view_day.localeCompare(b.view_day));
    
    return result;
  } catch (error) {
    console.error('Failed to get views per day:', error);
    return [];
  }
}

/**
 * Get the most recent product views with user info
 * @param limit Number of views to return (default: 10)
 */
export async function getRecentProductViews(limit: number = 10): Promise<ProductViewWithUser[]> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('product_views')
      .select(`
        id,
        product_id,
        viewed_at,
        user_id,
        products!product_views_product_id_fkey (
          id,
          name,
          product_media!product_media_product_id_fkey (
            url,
            is_main
          )
        ),
        profiles!product_views_user_id_fkey (
          id,
          email,
          first_name,
          last_name
        )
      `)
      .order('viewed_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent product views:', error);
      return [];
    }

    return (data || []).map((view: any) => {
      // Find the main image URL from product media
      const mainImage = view.products?.product_media?.find((media: any) => media.is_main)?.url ||
                       view.products?.product_media?.[0]?.url;

      return {
        id: view.id,
        product_id: view.product_id || '',
        product_name: view.products?.name || 'Unknown Product',
        product_image_url: mainImage || null,
        viewed_at: view.viewed_at || new Date().toISOString(),
        user: view.profiles ? {
          id: view.profiles.id,
          email: view.profiles.email,
          first_name: view.profiles.first_name,
          last_name: view.profiles.last_name
        } : null
      };
    });
  } catch (error) {
    console.error('Failed to fetch recent product views:', error);
    return [];
  }
}
