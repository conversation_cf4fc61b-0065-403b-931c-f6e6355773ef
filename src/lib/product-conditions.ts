/**
 * Type definitions for product conditions from the Supabase database
 */

export type ProductCondition = {
  value: string;
  label: string;
  description: string | null;
};

// These types match the product_conditions table in Supabase
export const CONDITION_VALUES = ['N', 'S', 'A', 'B', 'C', 'D'] as const;
export type ConditionValue = typeof CONDITION_VALUES[number];

// This function will be used to get condition details from the database
export const getConditionLabel = (value: string | null): string => {
  if (!value) return 'Unknown';
  return `${value} - Condition`; // Fallback if not found in database
};

// This function will be used to get condition description from the database
export const getConditionDescription = (value: string | null): string => {
  if (!value) return 'No condition information available';
  return 'Product condition details'; // Fallback if not found in database
};

// Format condition value to a user-friendly string
export const formatCondition = (value: string | null): string => {
  if (!value) return 'Unknown';
  
  const conditionMap: Record<string, string> = {
    'N': 'New',
    'S': 'Like New',
    'A': 'Excellent',
    'B': 'Good',
    'C': 'Fair',
    'D': 'Poor'
  };
  
  return conditionMap[value] || `Condition ${value}`;
};