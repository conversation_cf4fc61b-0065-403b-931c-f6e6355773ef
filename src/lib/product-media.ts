import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from './database.types';

export interface ProductMediaItem {
  id: string;
  product_id: string | null;
  url: string;
  alt?: string | null;
  type: string;
  position: number | null;
  created_at: string | null;
}

/**
 * Fetches media items for a product from the database
 * @param productId The ID of the product to fetch media for
 * @returns An array of ProductMediaItem objects
 */
export async function getProductMedia(productId: string): Promise<ProductMediaItem[]> {
  const supabase = createClientComponentClient<Database>();
  
  try {
    const { data, error } = await supabase
      .from('product_media')
      .select('*')
      .eq('product_id', productId)
      .order('position', { ascending: true });
      
    if (error) {
      console.error('Error fetching product media:', error);
      return [];
    }
    
    // If no media is found, return an empty array
    if (!data || data.length === 0) {
      return [];
    }
    
    return data as ProductMediaItem[];
  } catch (error) {
    console.error('Exception fetching product media:', error);
    return [];
  }
}

/**
 * Adds a new media item to a product
 * @param productId The ID of the product to add media to
 * @param url The URL of the media item
 * @param altText Optional alt text for the media item
 * @param mediaType The type of media (default: 'image')
 * @returns The newly created ProductMediaItem or null if creation failed
 */
export async function addProductMedia(
  productId: string,
  url: string,
  altText?: string,
  mediaType: string = 'image'
): Promise<ProductMediaItem | null> {
  const supabase = createClientComponentClient<Database>();
  
  try {
    // Get the highest position value for existing media
    const { data: existingMedia } = await supabase
      .from('product_media')
      .select('position')
      .eq('product_id', productId)
      .order('position', { ascending: false })
      .limit(1);
      
    const nextPosition = existingMedia && existingMedia.length > 0 && existingMedia[0].position !== null
      ? (existingMedia[0].position + 1) 
      : 1;
    
    // Insert the new media item
    const { data, error } = await supabase
      .from('product_media')
      .insert({
        product_id: productId,
        url,
        alt: altText || null,
        type: mediaType,
        position: nextPosition
      })
      .select()
      .single();
      
    if (error) {
      console.error('Error adding product media:', error);
      return null;
    }
    
    return data as ProductMediaItem;
  } catch (error) {
    console.error('Exception adding product media:', error);
    return null;
  }
}

/**
 * Updates an existing media item
 * @param mediaId The ID of the media item to update
 * @param updates The fields to update
 * @returns The updated ProductMediaItem or null if update failed
 */
export async function updateProductMedia(
  mediaId: string,
  updates: Partial<Omit<ProductMediaItem, 'id' | 'created_at'>>
): Promise<ProductMediaItem | null> {
  const supabase = createClientComponentClient<Database>();
  
  try {
    // Update the media item
    const { data, error } = await supabase
      .from('product_media')
      .update(updates)
      .eq('id', mediaId)
      .select()
      .single();
      
    if (error) {
      console.error('Error updating product media:', error);
      return null;
    }
    
    return data as ProductMediaItem;
  } catch (error) {
    console.error('Exception updating product media:', error);
    return null;
  }
}

/**
 * Deletes a media item
 * @param mediaId The ID of the media item to delete
 * @returns True if deletion was successful, false otherwise
 */
export async function deleteProductMedia(mediaId: string): Promise<boolean> {
  const supabase = createClientComponentClient<Database>();
  
  try {
    const { error } = await supabase
      .from('product_media')
      .delete()
      .eq('id', mediaId);
      
    if (error) {
      console.error('Error deleting product media:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Exception deleting product media:', error);
    return false;
  }
}
