// import webpush from 'web-push'; // Commented out for now
import { createClient } from '@/lib/supabase-server';

// Configure web-push with VAPID keys
const vapidPublicKey = process.env.VAPID_PUBLIC_KEY;
const vapidPrivateKey = process.env.VAPID_PRIVATE_KEY;
const vapidEmail = process.env.VAPID_EMAIL;

// webpush.setVapidDetails(
//   `mailto:${vapidEmail}`,
//   vapidPublicKey,
//   vapidPrivateKey
// );

export interface PushNotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  data?: Record<string, any>;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  requireInteraction?: boolean;
}

export class PushNotificationService {
  private supabase = createClient();

  // Send push notification to specific user
  async sendToUser(userId: string, payload: PushNotificationPayload): Promise<boolean> {
    try {
      // Get user's active push subscriptions
      const { data: subscriptions, error } = await this.supabase
        .from('push_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        console.error('Error fetching push subscriptions:', error);
        return false;
      }

      if (!subscriptions || subscriptions.length === 0) {
        console.log('No active push subscriptions found for user:', userId);
        return false;
      }

      // Send to all user's devices
      const sendPromises = subscriptions.map(async (subscription) => {
        try {
          const pushSubscription = {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.p256dh,
              auth: subscription.auth
            }
          };

          // TODO: Implement actual push notification sending
          // await webpush.sendNotification(
          //   pushSubscription,
          //   JSON.stringify(payload)
          // );
          console.log('Would send push notification:', payload);

          // Log successful delivery
          await this.logDelivery(subscription.id, 'push', 'success');
          return true;
        } catch (error: any) {
          console.error('Error sending push notification:', error);

          // Log failed delivery
          await this.logDelivery(subscription.id, 'push', 'failed', error.message);

          // If subscription is invalid, deactivate it
          if (error.statusCode === 410 || error.statusCode === 404) {
            await this.deactivateSubscription(subscription.id);
          }
          
          return false;
        }
      });

      const results = await Promise.all(sendPromises);
      return results.some(result => result); // Return true if at least one succeeded
    } catch (error) {
      console.error('Error in sendToUser:', error);
      return false;
    }
  }

  // Send push notification to all admin users
  async sendToAllAdmins(payload: PushNotificationPayload): Promise<boolean> {
    try {
      // Get all admin users
      const { data: adminUsers, error } = await this.supabase
        .from('profiles')
        .select('id')
        .eq('is_admin', true);

      if (error) {
        console.error('Error fetching admin users:', error);
        return false;
      }

      if (!adminUsers || adminUsers.length === 0) {
        console.log('No admin users found');
        return false;
      }

      // Send to all admin users
      const sendPromises = adminUsers.map(admin => this.sendToUser(admin.id, payload));
      const results = await Promise.all(sendPromises);
      
      return results.some(result => result); // Return true if at least one succeeded
    } catch (error) {
      console.error('Error in sendToAllAdmins:', error);
      return false;
    }
  }

  // Send notification for new order
  async sendNewOrderNotification(orderId: string): Promise<boolean> {
    try {
      // Get order details
      const { data: order, error } = await this.supabase
        .from('orders')
        .select('order_number, total_amount, customer_email')
        .eq('id', orderId)
        .single();

      if (error || !order) {
        console.error('Error fetching order details:', error);
        return false;
      }

      const payload: PushNotificationPayload = {
        title: '🛍️ New Order Received!',
        body: `Order #${order.order_number || 'N/A'} for €${order.total_amount} from ${order.customer_email || 'Unknown'}`,
        icon: '/icons/icon-192.png',
        badge: '/icons/icon-192.png',
        tag: `new-order-${orderId}`,
        data: {
          type: 'new_order',
          order_id: orderId,
          order_number: order.order_number,
          url: `/admin/orders/${orderId}`
        },
        actions: [
          {
            action: 'view',
            title: 'View Order',
            icon: '/icons/icon-192.png'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ],
        requireInteraction: true
      };

      return await this.sendToAllAdmins(payload);
    } catch (error) {
      console.error('Error sending new order notification:', error);
      return false;
    }
  }

  // Send notification for new bag request
  async sendNewBagRequestNotification(requestId: string): Promise<boolean> {
    try {
      // Get bag request details
      const { data: request, error } = await this.supabase
        .from('wardrobe_items')
        .select(`
          name,
          brand,
          category,
          profiles!inner(email)
        `)
        .eq('id', requestId)
        .single();

      if (error || !request) {
        console.error('Error fetching bag request details:', error);
        return false;
      }

      const payload: PushNotificationPayload = {
        title: '👜 New Bag Request',
        body: `New request for ${request.brand || ''} ${request.name || 'Unknown bag'} from ${request.profiles?.email || 'Unknown user'}`,
        icon: '/icons/icon-192.png',
        badge: '/icons/icon-192.png',
        tag: `new-bag-request-${requestId}`,
        data: {
          type: 'new_bag_request',
          request_id: requestId,
          url: '/admin/bag-requests'
        },
        actions: [
          {
            action: 'view',
            title: 'View Request',
            icon: '/icons/icon-192.png'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ],
        requireInteraction: false
      };

      return await this.sendToAllAdmins(payload);
    } catch (error) {
      console.error('Error sending new bag request notification:', error);
      return false;
    }
  }

  // Send notification for low inventory
  async sendLowInventoryNotification(productId: string): Promise<boolean> {
    try {
      // Get product details
      const { data: product, error } = await this.supabase
        .from('products')
        .select('name, quantity, price')
        .eq('id', productId)
        .single();

      if (error || !product) {
        console.error('Error fetching product details:', error);
        return false;
      }

      const urgencyLevel = product.quantity === 0 ? 'URGENT' : product.quantity === 1 ? 'HIGH' : 'MEDIUM';
      const emoji = product.quantity === 0 ? '🚨' : '⚠️';

      const payload: PushNotificationPayload = {
        title: `${emoji} Low Inventory Alert`,
        body: `${urgencyLevel}: "${product.name}" has only ${product.quantity} item(s) remaining`,
        icon: '/icons/icon-192.png',
        badge: '/icons/icon-192.png',
        tag: `low-inventory-${productId}`,
        data: {
          type: 'low_inventory',
          product_id: productId,
          url: `/admin/products/${productId}`
        },
        actions: [
          {
            action: 'view',
            title: 'View Product',
            icon: '/icons/icon-192.png'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ],
        requireInteraction: product.quantity === 0
      };

      return await this.sendToAllAdmins(payload);
    } catch (error) {
      console.error('Error sending low inventory notification:', error);
      return false;
    }
  }

  // Log delivery attempt
  private async logDelivery(
    subscriptionId: string, 
    method: string, 
    status: string, 
    errorMessage?: string
  ): Promise<void> {
    try {
      await this.supabase
        .from('notification_delivery_log')
        .insert({
          notification_id: subscriptionId, // Using subscription ID as reference
          delivery_method: method,
          status,
          error_message: errorMessage,
          delivered_at: status === 'success' ? new Date().toISOString() : null
        });
    } catch (error) {
      console.error('Error logging delivery:', error);
    }
  }

  // Deactivate invalid subscription
  private async deactivateSubscription(subscriptionId: string): Promise<void> {
    try {
      await this.supabase
        .from('push_subscriptions')
        .update({ is_active: false })
        .eq('id', subscriptionId);
    } catch (error) {
      console.error('Error deactivating subscription:', error);
    }
  }
}

let _pushNotificationService: PushNotificationService | null = null;

export const pushNotificationService = {
  getInstance(): PushNotificationService {
    if (!_pushNotificationService) {
      _pushNotificationService = new PushNotificationService();
    }
    return _pushNotificationService;
  }
};
