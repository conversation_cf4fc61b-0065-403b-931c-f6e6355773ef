import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from './database.types';

/**
 * Session utilities to handle authentication synchronization issues
 */

export async function ensureValidSession() {
  const supabase = createClientComponentClient<Database>();
  
  try {
    // Force refresh the session to sync with server
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('[SessionUtils] Session error:', error);
      return null;
    }
    
    if (!session) {
      console.warn('[SessionUtils] No session found');
      return null;
    }
    
    // Verify the session is valid by making a simple auth check
    const { data: user, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('[SessionUtils] User verification failed:', userError);
      return null;
    }
    
    console.log('[SessionUtils] Valid session confirmed for:', user.user.email);
    return session;
    
  } catch (error) {
    console.error('[SessionUtils] Session check failed:', error);
    return null;
  }
}

export async function makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
  // Ensure we have a valid session before making the request
  const session = await ensureValidSession();
  
  if (!session) {
    throw new Error('No valid session available');
  }
  
  // Add session token to headers
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${session.access_token}`,
    ...options.headers,
  };
  
  return fetch(url, {
    ...options,
    headers,
    credentials: 'include',
  });
}