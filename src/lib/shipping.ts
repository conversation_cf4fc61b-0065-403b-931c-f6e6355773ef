/**
 * Shipping calculator that determines shipping costs based on location
 */

export type Region = 'valencia' | 'spain' | 'eu' | 'us' | 'international';

export type ShippingInfo = {
  cost: number;
  currency: string;
  estimatedDays: string;
  description: string;
};

// Base shipping rates by region (in EUR)
const SHIPPING_RATES: Record<Region, ShippingInfo> = {
  valencia: {
    cost: 5,
    currency: 'EUR',
    estimatedDays: '1-2',
    description: 'Local delivery within Valencia'
  },
  spain: {
    cost: 8,
    currency: 'EUR',
    estimatedDays: '2-3',
    description: 'Standard shipping within Spain'
  },
  eu: {
    cost: 15,
    currency: 'EUR',
    estimatedDays: '3-5',
    description: 'European Union shipping'
  },
  us: {
    cost: 25,
    currency: 'EUR',
    estimatedDays: '7-10',
    description: 'United States shipping'
  },
  international: {
    cost: 35,
    currency: 'EUR',
    estimatedDays: '10-15',
    description: 'International shipping'
  }
};

// Determine region from country code
export function getRegionFromCountry(countryCode: string): Region {
  if (!countryCode) return 'international';
  
  const code = countryCode.toUpperCase();
  
  if (code === 'ES') {
    // Further refinement for Spain/Valencia would require city info
    return 'spain';
  }
  
  // EU countries
  const euCountries = [
    'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 
    'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 
    'PL', 'PT', 'RO', 'SK', 'SI', 'SE'
  ];
  
  if (euCountries.includes(code)) {
    return 'eu';
  }
  
  if (code === 'US') {
    return 'us';
  }
  
  return 'international';
}

// Further refinement for Valencia based on city and postal code
export function isValencia(city?: string, postalCode?: string): boolean {
  if (!city || !postalCode) return false;
  
  const normalizedCity = city.toLowerCase().trim();
  const normalizedPostal = postalCode.trim();
  
  // Valencia city postal codes start with 46
  return (normalizedCity === 'valencia' || normalizedCity.includes('valencia')) && 
         normalizedPostal.startsWith('46');
}

// Get default shipping info for initial state
export function getDefaultShippingInfo(): ShippingInfo {
  // Default to Spain shipping rates as the most common case
  return SHIPPING_RATES.spain;
}

// Calculate shipping cost
export function calculateShipping(
  subtotal: number,
  countryCode: string,
  city?: string,
  postalCode?: string
): ShippingInfo {
  // Default to international if no location provided
  if (!countryCode) {
    return SHIPPING_RATES.international;
  }

  // Check if it's Valencia first
  if (countryCode.toUpperCase() === 'ES' && isValencia(city, postalCode)) {
    return SHIPPING_RATES.valencia;
  }

  // Otherwise get region based on country
  const region = getRegionFromCountry(countryCode);
  return SHIPPING_RATES[region];
}