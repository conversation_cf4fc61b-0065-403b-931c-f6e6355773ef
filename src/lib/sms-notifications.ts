import supabaseAdmin from '@/lib/supabaseAdmin';

// Twilio configuration
const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN;
const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER;

export interface SMSNotificationData {
  type: string;
  title: string;
  message: string;
  data?: any;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export class SMSNotificationService {
  private supabase;

  constructor() {
    this.supabase = supabaseAdmin;
  }

  // Send SMS notification to admin
  async sendAdminNotification(notification: SMSNotificationData): Promise<boolean> {
    try {
      console.log('SMS Service: Starting sendAdminNotification with:', notification);

      // Check if Twi<PERSON> is configured
      if (!TWILIO_ACCOUNT_SID || !TWILIO_AUTH_TOKEN || !TWILIO_PHONE_NUMBER) {
        console.log('<PERSON><PERSON><PERSON> not configured, skipping SMS notification');
        return false;
      }

      console.log('<PERSON><PERSON><PERSON> configured successfully');

      // Get admin users with phone numbers
      console.log('Fetching admin users with phone numbers...');
      const { data: adminUsers, error: profileError } = await this.supabase
        .from('profiles')
        .select('id, phone_number, first_name, last_name')
        .eq('is_admin', true)
        .not('phone_number', 'is', null);

      console.log('Admin users query result:', { adminUsers, profileError });
      console.log('Number of admin users found:', adminUsers?.length || 0);

      if (profileError) {
        console.error('Error fetching admin profiles:', profileError);
        return false;
      }

      if (!adminUsers || adminUsers.length === 0) {
        console.log('No admin users found with phone numbers');
        return false;
      }

      // Check notification preferences for each admin
      const adminUsersWithPrefs = [];
      for (const admin of adminUsers) {
        console.log('Checking preferences for admin:', admin.id, 'notification type:', notification.type);

        const { data: prefs, error: prefsError } = await this.supabase
          .from('admin_notification_preferences')
          .select('push_enabled')
          .eq('user_id', admin.id)
          .eq('notification_type', notification.type as any)
          .maybeSingle();

        console.log('Preferences result:', { prefs, prefsError });

        if (!prefsError && prefs && prefs.push_enabled) {
          adminUsersWithPrefs.push(admin);
        }
      }

      const error = null; // Reset error for the rest of the function
      const finalAdminUsers = adminUsersWithPrefs;

      if (!finalAdminUsers || finalAdminUsers.length === 0) {
        console.log('No admin users found with SMS notifications enabled for type:', notification.type);
        return false;
      }

      // Send SMS to each admin
      const smsPromises = finalAdminUsers
        .filter(admin => admin.phone_number) // Filter out admins without phone numbers
        .map(admin =>
          this.sendSMS(
            admin.phone_number!,
            this.generateSMSContent(notification, admin),
            notification.priority
          )
        );

      const results = await Promise.all(smsPromises);
      return results.some(result => result); // Return true if at least one succeeded

    } catch (error) {
      console.error('Error sending admin SMS notification:', error);
      return false;
    }
  }

  // Send individual SMS using Twilio API
  private async sendSMS(
    to: string, 
    message: string, 
    priority: string = 'medium'
  ): Promise<boolean> {
    try {
      // Format phone number (ensure it starts with +)
      const formattedTo = to.startsWith('+') ? to : `+${to}`;
      
      const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/Messages.json`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${Buffer.from(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          From: TWILIO_PHONE_NUMBER!,
          To: formattedTo,
          Body: message,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Twilio API error:', errorData);
        return false;
      }

      const responseData = await response.json();
      console.log('SMS sent successfully:', responseData.sid);
      return true;

    } catch (error) {
      console.error('Error sending SMS:', error);
      return false;
    }
  }

  // Generate SMS content based on notification type
  private generateSMSContent(notification: SMSNotificationData, admin: any): string {
    const adminName = admin.first_name || 'Admin';
    const priorityEmoji = this.getPriorityEmoji(notification.priority);
    
    let content = `${priorityEmoji} Treasures of Maimi Alert\n\n`;
    content += `Hi ${adminName},\n\n`;
    content += `${notification.title}\n`;
    content += `${notification.message}\n\n`;

    // Add type-specific information
    switch (notification.type) {
      case 'new_order':
        if (notification.data?.order_number) {
          content += `Order: ${notification.data.order_number}\n`;
        }
        if (notification.data?.total_amount) {
          content += `Amount: €${notification.data.total_amount}\n`;
        }
        content += `View: https://treasuresofmaimi.com/admin/orders\n`;
        break;

      case 'new_message':
        if (notification.data?.sender_name) {
          content += `From: ${notification.data.sender_name}\n`;
        }
        if (notification.data?.subject) {
          content += `Subject: ${notification.data.subject}\n`;
        }
        content += `View: https://treasuresofmaimi.com/admin/messages\n`;
        break;

      case 'low_inventory':
        content += `Check inventory: https://treasuresofmaimi.com/admin/products\n`;
        break;

      case 'new_bag_request':
        content += `View requests: https://treasuresofmaimi.com/admin/bag-requests\n`;
        break;
    }

    // Keep SMS under 160 characters if possible, or split into multiple messages
    if (content.length > 160) {
      // Truncate and add "..." if too long
      content = content.substring(0, 157) + '...';
    }

    return content;
  }

  // Get priority emoji
  private getPriorityEmoji(priority?: string): string {
    switch (priority) {
      case 'urgent': return '🚨';
      case 'high': return '⚠️';
      case 'medium': return '📱';
      case 'low': return 'ℹ️';
      default: return '📱';
    }
  }

  // Send SMS notification to user
  async sendUserNotification(notification: {
    to: string;
    message: string;
    type?: string;
  }): Promise<boolean> {
    try {
      console.log('SMS Service: Sending user notification to:', notification.to);

      // Check if Twilio is configured
      if (!TWILIO_ACCOUNT_SID || !TWILIO_AUTH_TOKEN || !TWILIO_PHONE_NUMBER) {
        console.error('SMS Service: Twilio credentials not configured');
        return false;
      }

      // Validate phone number
      if (!SMSNotificationService.validatePhoneNumber(notification.to)) {
        console.error('SMS Service: Invalid phone number format:', notification.to);
        return false;
      }

      // Send SMS using Twilio API
      const result = await this.sendSMS(notification.to, notification.message);
      console.log(`User SMS notification sent to ${notification.to}:`, result);
      return result;
    } catch (error) {
      console.error('Error sending user SMS notification:', error);
      return false;
    }
  }

  // Validate phone number format
  static validatePhoneNumber(phoneNumber: string): boolean {
    // Basic international phone number validation
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber.replace(/[\s\-\(\)]/g, ''));
  }

  // Format phone number for display
  static formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters except +
    const cleaned = phoneNumber.replace(/[^\d+]/g, '');
    
    // Ensure it starts with +
    if (!cleaned.startsWith('+')) {
      return `+${cleaned}`;
    }
    
    return cleaned;
  }
}

// Singleton instance
let _smsNotificationService: SMSNotificationService | null = null;

export const smsNotificationService = {
  getInstance(): SMSNotificationService {
    if (!_smsNotificationService) {
      _smsNotificationService = new SMSNotificationService();
    }
    return _smsNotificationService;
  }
};
