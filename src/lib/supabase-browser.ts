import { createPagesBrowserClient } from '@supabase/auth-helpers-nextjs'
import type { Database } from './database.types'

// Create a singleton instance to avoid multiple client creations
let supabaseInstance: any = null;

export const supabase = (() => {
  if (!supabaseInstance) {
    supabaseInstance = createPagesBrowserClient<Database>({
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!,
      supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      options: {
        realtime: {
          // Optimize realtime connection to reduce warnings
          params: {
            eventsPerSecond: 10,
          },
        },
        global: {
          headers: {
            'X-Client-Info': 'treasures-of-maimi-web'
          }
        }
      },
    });
  }
  return supabaseInstance!; // Non-null assertion since we always create it above
})();

// Also export createClient for backwards compatibility
export function createClient() {
  return supabase;
}
