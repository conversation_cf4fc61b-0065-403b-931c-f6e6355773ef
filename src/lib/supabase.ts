import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from './database.types';

type TypedSupabaseClient = SupabaseClient<Database>;

/**
 * Enhanced product conditions utility functions
 */

// Fetch all product conditions from the database
export function fetchProductConditions(supabase: TypedSupabaseClient) {
  return supabase
    .from('product_conditions')
    .select('*')
    .order('value');
}

// Format a condition value to a human-readable string
export function formatCondition(value: string | null): string {
  if (!value) return 'Unknown';
  
  // Convert camelCase or snake_case to Title Case with spaces
  return value
    .replace(/_/g, ' ')
    .replace(/([A-Z])/g, ' $1')
    .replace(/^\w/, c => c.toUpperCase())
    .trim();
}

// Format a condition code to its display text
export function formatConditionCode(condition: string | undefined): string {
  if (!condition) return 'Not specified';
  
  // If it's already in the format "X - Description", return it as is
  if (condition.includes(' - ')) return condition;
  
  return condition;
}

// Get the description for a condition code
export async function getConditionDescription(supabase: TypedSupabaseClient, condition: string | undefined): Promise<string> {
  if (!condition) return '';
  
  // Extract just the code if it's in the format "X - Description"
  const code = condition.includes(' - ') ? condition.split(' - ')[0] : condition;
  
  const { data } = await supabase
    .from('product_conditions')
    .select('description')
    .eq('code', code)
    .single();
    
  return data?.description || '';
}

/**
 * User profile utilities
 */

// Fetch the current user's profile
export function fetchUserProfile(supabase: TypedSupabaseClient, userId: string) {
  return supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
}

// Check if a user is an admin
export async function isUserAdmin(supabase: TypedSupabaseClient, userId: string): Promise<boolean> {
  const { data } = await supabase
    .from('profiles')
    .select('is_admin')
    .eq('id', userId)
    .single();
    
  return data?.is_admin || false;
}

/**
 * Product utilities
 */

// Fetch a product by slug
export function fetchProductBySlug(supabase: TypedSupabaseClient, slug: string) {
  return supabase
    .from('products')
    .select(`
      *,
      product_media(*),
      categories(*),
      product_conditions(*)
    `)
    .eq('slug', slug)
    .single();
}

// Fetch all products with optional filters
export function fetchProducts(
  supabase: TypedSupabaseClient,
  options: {
    categoryId?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) {
  const { 
    categoryId, 
    limit = 10, 
    offset = 0, 
    sortBy = 'created_at', 
    sortOrder = 'desc' 
  } = options;
  
  let query = supabase
    .from('products')
    .select(`
      *,
      product_media(*)
    `)
    .order(sortBy, { ascending: sortOrder === 'asc' })
    .range(offset, offset + limit - 1);
    
  if (categoryId) {
    query = query.eq('category_id', categoryId);
  }
  
  return query;
}
