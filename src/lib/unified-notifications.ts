import { emailNotificationService } from '@/lib/email-notifications';
import { smsNotificationService } from '@/lib/sms-notifications';
import { whatsappNotificationService } from '@/lib/whatsapp-notifications';

export interface UnifiedNotificationData {
  type: string;
  title: string;
  message: string;
  data?: any;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export class UnifiedNotificationService {
  // Send notification through all enabled channels
  static async sendAdminNotification(notification: UnifiedNotificationData): Promise<{
    email: boolean;
    sms: boolean;
    whatsapp: boolean;
  }> {
    const results = {
      email: false,
      sms: false,
      whatsapp: false
    };

    try {
      // Send email notification
      const emailPromise = emailNotificationService.getInstance()
        .sendAdminNotification(notification)
        .then(success => {
          results.email = success;
          return success;
        })
        .catch(error => {
          console.error('Email notification failed:', error);
          return false;
        });

      // Send SMS notification
      const smsPromise = smsNotificationService.getInstance()
        .sendAdminNotification(notification)
        .then(success => {
          results.sms = success;
          return success;
        })
        .catch(error => {
          console.error('SMS notification failed:', error);
          return false;
        });

      // Send WhatsApp notification
      const whatsappPromise = whatsappNotificationService.getInstance()
        .sendAdminNotification(notification)
        .then(success => {
          results.whatsapp = success;
          return success;
        })
        .catch(error => {
          console.error('WhatsApp notification failed:', error);
          return false;
        });

      // Wait for all notifications to complete
      await Promise.all([emailPromise, smsPromise, whatsappPromise]);

      console.log('Unified notification results:', results);
      return results;

    } catch (error) {
      console.error('Error in unified notification service:', error);
      return results;
    }
  }

  // Send notification for new order
  static async sendNewOrderNotification(orderData: {
    order_id: string;
    order_number?: string;
    total_amount?: number;
    customer_email?: string;
  }): Promise<boolean> {
    const notification: UnifiedNotificationData = {
      type: 'new_order',
      title: 'New Order Received',
      message: `Order #${orderData.order_number || 'N/A'} for €${orderData.total_amount || '0.00'} from ${orderData.customer_email || 'Unknown'}`,
      data: orderData,
      priority: 'high'
    };

    const results = await this.sendAdminNotification(notification);
    return results.email || results.sms || results.whatsapp;
  }

  // Send notification for order status change
  static async sendOrderStatusChangeNotification(orderData: {
    order_id: string;
    order_number?: string;
    old_status: string;
    new_status: string;
    total_amount?: number;
    customer_email?: string;
  }): Promise<boolean> {
    try {
      // Check for recent duplicate notifications (within last 5 minutes)
      const fiveMinutesAgo = new Date();
      fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5);
      
      const { data: existingNotifications } = await this.supabase
        .from('notifications')
        .select('id')
        .eq('type', 'order_status_change')
        .gte('created_at', fiveMinutesAgo.toISOString())
        .ilike('message', `%Order #${orderData.order_number || 'N/A'}%`)
        .ilike('message', `%${orderData.new_status}%`);
      
      if (existingNotifications && existingNotifications.length > 0) {
        console.log(`🚫 Skipping duplicate order status notification for Order #${orderData.order_number}`);
        return true; // Return true to indicate it was handled (already exists)
      }
      
      const notification: UnifiedNotificationData = {
        type: 'order_status_change',
        title: 'Order Status Updated',
        message: `Order #${orderData.order_number || 'N/A'} status changed from ${orderData.old_status} to ${orderData.new_status}`,
        data: orderData,
        priority: 'medium'
      };

      const results = await this.sendAdminNotification(notification);
      return results.email || results.sms || results.whatsapp;
    } catch (error) {
      console.error('Error in sendOrderStatusChangeNotification:', error);
      return false;
    }
  }

  // Send notification for new contact message
  static async sendNewMessageNotification(messageData: {
    message_id: string;
    sender_name: string;
    sender_email: string;
    subject: string;
    message: string;
  }): Promise<boolean> {
    const notification: UnifiedNotificationData = {
      type: 'new_message',
      title: 'New Contact Message',
      message: `New message from ${messageData.sender_name} (${messageData.sender_email}): ${messageData.subject}`,
      data: messageData,
      priority: 'medium'
    };

    const results = await this.sendAdminNotification(notification);
    return results.email || results.sms || results.whatsapp;
  }

  // Send notification for new bag request
  static async sendNewBagRequestNotification(requestData: {
    request_id: string;
    requester_name?: string;
    requester_email?: string;
    bag_details?: string;
  }): Promise<boolean> {
    const notification: UnifiedNotificationData = {
      type: 'new_bag_request',
      title: 'New Bag Request',
      message: `New bag request from ${requestData.requester_name || 'Unknown'}: ${requestData.bag_details || 'No details provided'}`,
      data: requestData,
      priority: 'medium'
    };

    const results = await this.sendAdminNotification(notification);
    return results.email || results.sms || results.whatsapp;
  }

  // Send notification for low inventory
  static async sendLowInventoryNotification(productData: {
    product_id: string;
    product_name: string;
    current_quantity: number;
    threshold?: number;
  }): Promise<boolean> {
    const urgencyLevel = productData.current_quantity === 0 ? 'URGENT' : 
                        productData.current_quantity === 1 ? 'HIGH' : 'MEDIUM';
    
    const notification: UnifiedNotificationData = {
      type: 'low_inventory',
      title: 'Low Inventory Alert',
      message: `${urgencyLevel}: "${productData.product_name}" has only ${productData.current_quantity} item(s) remaining`,
      data: productData,
      priority: productData.current_quantity === 0 ? 'urgent' : 
               productData.current_quantity === 1 ? 'high' : 'medium'
    };

    const results = await this.sendAdminNotification(notification);
    return results.email || results.sms || results.whatsapp;
  }

  // Send notification for payment received
  static async sendPaymentReceivedNotification(paymentData: {
    order_id: string;
    order_number?: string;
    amount: number;
    payment_method?: string;
    customer_email?: string;
  }): Promise<boolean> {
    const notification: UnifiedNotificationData = {
      type: 'payment_received',
      title: 'Payment Received',
      message: `Payment of €${paymentData.amount} received for order #${paymentData.order_number || 'N/A'}`,
      data: paymentData,
      priority: 'medium'
    };

    const results = await this.sendAdminNotification(notification);
    return results.email || results.sms || results.whatsapp;
  }

  // Send notification for refund request
  static async sendRefundRequestNotification(refundData: {
    order_id: string;
    order_number?: string;
    amount: number;
    reason?: string;
    customer_email?: string;
  }): Promise<boolean> {
    const notification: UnifiedNotificationData = {
      type: 'refund_requested',
      title: 'Refund Request',
      message: `Refund request for €${refundData.amount} on order #${refundData.order_number || 'N/A'}`,
      data: refundData,
      priority: 'high'
    };

    const results = await this.sendAdminNotification(notification);
    return results.email || results.sms || results.whatsapp;
  }

  // Send notification for new contact message
  static async sendContactMessageNotification(contactData: {
    message_id: string;
    sender_name: string;
    sender_email: string;
    subject: string;
    message: string;
  }): Promise<boolean> {
    const notification: UnifiedNotificationData = {
      type: 'new_message',
      title: 'New Contact Message',
      message: `New message from ${contactData.sender_name} (${contactData.sender_email}): ${contactData.subject}`,
      data: contactData,
      priority: 'medium'
    };

    const results = await this.sendAdminNotification(notification);
    return results.email || results.sms || results.whatsapp;
  }

  // Send system alert notification
  static async sendSystemAlertNotification(alertData: {
    alert_type: string;
    message: string;
    severity?: 'low' | 'medium' | 'high' | 'urgent';
    details?: any;
  }): Promise<boolean> {
    const notification: UnifiedNotificationData = {
      type: 'system_alert',
      title: `System Alert: ${alertData.alert_type}`,
      message: alertData.message,
      data: alertData.details,
      priority: alertData.severity || 'medium'
    };

    const results = await this.sendAdminNotification(notification);
    return results.email || results.sms || results.whatsapp;
  }

  // Send bag request status update notification to user
  static async sendBagRequestStatusNotification(statusData: {
    user_email: string;
    user_name: string;
    bag_name: string;
    old_status: string;
    new_status: string;
    notes?: string;
    user_phone?: string;
  }): Promise<boolean> {
    const statusLabels: Record<string, string> = {
      pending: 'under review',
      approved: 'approved',
      rejected: 'declined',
      in_progress: 'in progress'
    };

    const statusMessage = statusData.new_status === 'approved' 
      ? `Great news! Your bag request for "${statusData.bag_name}" has been approved. We'll be in touch soon with more details.`
      : statusData.new_status === 'rejected'
      ? `We've reviewed your bag request for "${statusData.bag_name}" and unfortunately cannot fulfill it at this time. ${statusData.notes ? `Note: ${statusData.notes}` : ''}`
      : `Your bag request for "${statusData.bag_name}" status has been updated to ${statusLabels[statusData.new_status] || statusData.new_status}.`;

    const smsMessage = statusData.new_status === 'approved' 
      ? `🎉 Good news! Your "${statusData.bag_name}" request has been approved. Check your email for details.`
      : statusData.new_status === 'rejected'
      ? `Your "${statusData.bag_name}" request was declined. Check your email for details.`
      : `Your "${statusData.bag_name}" request status: ${statusLabels[statusData.new_status] || statusData.new_status}`;

    try {
      // Send email notification to user
      const emailSuccess = await emailNotificationService.getInstance()
        .sendUserNotification({
          to: statusData.user_email,
          subject: `Bag Request Update - ${statusData.bag_name}`,
          message: statusMessage,
          type: 'bag_request_update'
        });

      // Send SMS notification to user if phone number is provided
      let smsSuccess = false;
      if (statusData.user_phone) {
        smsSuccess = await smsNotificationService.getInstance()
          .sendUserNotification({
            to: statusData.user_phone,
            message: smsMessage,
            type: 'bag_request_update'
          });
      }

      console.log(`Bag request status notification sent - Email: ${emailSuccess}, SMS: ${smsSuccess}`);
      return emailSuccess || smsSuccess;
    } catch (error) {
      console.error('Error sending bag request status notification:', error);
      return false;
    }
  }

  // Send order status update notification to user
  static async sendOrderStatusNotification(orderData: {
    user_email: string;
    user_name: string;
    order_number: string;
    old_status: string;
    new_status: string;
    tracking_url?: string;
    user_phone?: string;
  }): Promise<boolean> {
    const statusLabels: Record<string, string> = {
      pending: 'confirmed',
      processing: 'being prepared',
      shipped: 'shipped',
      delivered: 'delivered',
      cancelled: 'cancelled'
    };

    let statusMessage = `Your order #${orderData.order_number} has been ${statusLabels[orderData.new_status] || orderData.new_status}.`;
    
    if (orderData.new_status === 'shipped' && orderData.tracking_url) {
      statusMessage += ` You can track your package here: ${orderData.tracking_url}`;
    }

    // SMS message (shorter version)
    let smsMessage = `Order #${orderData.order_number} ${statusLabels[orderData.new_status] || orderData.new_status}`;
    if (orderData.new_status === 'shipped') {
      smsMessage += '. Check email for tracking details.';
    }

    try {
      // Send email notification to user
      const emailSuccess = await emailNotificationService.getInstance()
        .sendUserNotification({
          to: orderData.user_email,
          subject: `Order Update - #${orderData.order_number}`,
          message: statusMessage,
          type: 'order_update'
        });

      // Send SMS notification to user if phone number is provided
      let smsSuccess = false;
      if (orderData.user_phone) {
        smsSuccess = await smsNotificationService.getInstance()
          .sendUserNotification({
            to: orderData.user_phone,
            message: smsMessage,
            type: 'order_update'
          });
      }

      console.log(`Order status notification sent - Email: ${emailSuccess}, SMS: ${smsSuccess}`);
      return emailSuccess || smsSuccess;
    } catch (error) {
      console.error('Error sending order status notification:', error);
      return false;
    }
  }
}

// Export singleton instance
export const unifiedNotificationService = UnifiedNotificationService;
