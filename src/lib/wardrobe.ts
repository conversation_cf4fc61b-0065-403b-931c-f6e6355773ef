import { createClient } from './supabase-browser';
import { Database } from './database.types';

export type WardrobeItem = Database['public']['Tables']['wardrobe_items']['Row'];
export type WishlistItem = {
  id: string;
  product_id: string;
  user_id: string;
  created_at: string;
  product: {
    id: string;
    name: string;
    price: number;
    image_url: string;
    slug: string;
    sale_price: number | null;
    category_name: string | null;
    brand: string | null;
  };
};

/**
 * Get all wardrobe items for a user
 */
export async function getWardrobeItems(userId: string) {
  const supabase = createClient();
  const { data, error } = await supabase
    .from('wardrobe_items')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching wardrobe items:', error);
    return [];
  }

  return data || [];
}

/**
 * Add a new wardrobe item via API
 */
export async function addWardrobeItem(userId: string, item: Omit<WardrobeItem, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
  console.log('[Wardrobe] Creating bag request via API:', {
    userId,
    itemData: item
  });
  
  try {
    const response = await fetch('/api/bag-requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(item)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create bag request');
    }

    const result = await response.json();
    console.log('[Wardrobe] Successfully created bag request:', result.data);
    return result.data;
  } catch (error) {
    console.error('[Wardrobe] Error creating bag request:', error);
    throw error;
  }
}

/**
 * Delete a wardrobe item
 */
export async function deleteWardrobeItem(itemId: string) {
  const supabase = createClient();
  const { error } = await supabase
    .from('wardrobe_items')
    .delete()
    .eq('id', itemId);

  if (error) {
    throw error;
  }

  return true;
}

/**
 * Get all wishlist items for a user
 */
export async function getWishlistItems(
  userId: string, 
  options?: { select?: string }
) {
  console.log(`Fetching wishlist items for user ${userId}`);
  const supabase = createClient();
  
  let selectQuery = options?.select || `
    id,
    product_id,
    user_id,
    created_at,
    product:products(
      id,
      name,
      slug,
      price,
      product_media (
        url,
        type,
        is_main
      )
    )
  `;
  
  // Execute the query with all conditions at once
  const { data, error } = await supabase
    .from('wishlists')
    .select(selectQuery)
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching wishlist items:', error);
    return [];
  }

  // Transform the data to match the expected WishlistItem structure
  const transformedData = data?.map((item: any) => {
    const product = item.product;
    let mainImage = '';

    if (product?.product_media && product.product_media.length > 0) {
      const mainMedia = product.product_media.find((media: any) => media.is_main);
      mainImage = mainMedia?.url || product.product_media[0].url;
    }

    return {
      id: item.id,
      product_id: item.product_id,
      user_id: item.user_id,
      created_at: item.created_at,
      product: product ? {
        id: product.id,
        name: product.name,
        slug: product.slug,
        price: product.price,
        image_url: mainImage,
        sale_price: null,
        category_name: null,
        brand: null
      } : undefined
    };
  }) || [];

  console.log(`Retrieved ${transformedData.length} wishlist items:`, transformedData);
  return transformedData;
}

/**
 * Add a product to wishlist
 */
export async function addToWishlist(userId: string, productId: string) {
  const supabase = createClient();
  const { data, error } = await supabase
    .from('wishlists')
    .insert({
      user_id: userId,
      product_id: productId
    })
    .select()
    .single();

  if (error && error.code !== '23505') { // Ignore unique violation errors
    throw error;
  }

  return data;
}

/**
 * Remove a product from wishlist
 */
export async function removeFromWishlist(userId: string, productId: string) {
  console.log(`Attempting to remove product ${productId} from wishlist for user ${userId}`);
  const supabase = createClient();
  
  // First, verify the item exists
  const { data: existingItem, error: checkError } = await supabase
    .from('wishlists')
    .select('id')
    .match({
      user_id: userId,
      product_id: productId
    })
    .single();
    
  console.log('Existing wishlist item check:', existingItem, checkError);
  
  if (checkError && checkError.code !== 'PGRST116') {
    console.error('Error checking wishlist item:', checkError);
    throw checkError;
  }
  
  if (!existingItem) {
    console.warn('Wishlist item not found, nothing to delete');
    return false;
  }
  
  const { error } = await supabase
    .from('wishlists')
    .delete()
    .match({
      user_id: userId,
      product_id: productId
    });

  if (error) {
    console.error('Error deleting wishlist item:', error);
    throw error;
  }

  console.log('Successfully removed item from wishlist');
  return true;
}

/**
 * Get order history for a user
 */
export async function getOrderHistory(userId: string) {
  const supabase = createClient();
  
  try {
    console.log('Fetching order history for user:', userId);
    
    // Get the orders with their items and product details
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        *,
        items:order_items (
          id,
          quantity,
          price,
          product:products (
            id,
            name,
            slug,
            product_media (
              id,
              url,
              is_main
            )
          )
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (ordersError) {
      console.error('Error fetching orders:', ordersError);
      return [];
    }

    if (!orders || orders.length === 0) {
      console.log('No orders found for user');
      return [];
    }

    console.log('Raw orders data:', JSON.stringify(orders, null, 2));

    // Transform the orders data
    const transformedOrders = orders.map((order: any) => {
      // Ensure items is an array
      const orderItems = order.items || [];
      console.log(`Processing order ${order.id} with ${orderItems.length} items`);

      // Calculate total from items if total_amount is not set
      const calculatedTotal = orderItems.reduce((sum: any, item: any) => {
        const price = Number(item.price) || 0;
        const quantity = Number(item.quantity) || 0;
        return sum + (price * quantity);
      }, 0);

      // Transform each order item
      const transformedItems = orderItems.map((item: any) => {
        console.log('Processing item:', item);
        
        // Get the main image or first available image
        const productMedia = item.product?.product_media || [];
        const mainImage = productMedia.find((media: any) => media.is_main) || productMedia[0];
        
        return {
          id: item.id,
          quantity: Number(item.quantity) || 1,
          price: Number(item.price) || 0,
          product: item.product ? {
            id: item.product.id,
            name: item.product.name,
            slug: item.product.slug,
            image_url: mainImage?.url || ''
          } : null
        };
      });

      return {
        id: order.id,
        order_number: order.order_number,
        status: order.status || 'pending',
        total_amount: Number(order.total_amount) || calculatedTotal,
        created_at: order.created_at,
        updated_at: order.updated_at,
        payment_intent: order.payment_intent,
        payment_provider: order.payment_provider,
        carrier: order.carrier,
        tracking_url: order.tracking_url,
        items: transformedItems
      };
    });

    console.log('Transformed orders:', JSON.stringify(transformedOrders, null, 2));
    return transformedOrders;

  } catch (error) {
    console.error('Error in getOrderHistory:', error);
    return [];
  }
}

/**
 * Get all wardrobe items for admin
 */
export async function getWardrobeItemsForAdmin() {
  const supabase = createClient();
  
  try {
    console.log('Fetching wardrobe items for admin...');
    
    const { data, error } = await supabase
      .from('wardrobe_items')
      .select(`
        *,
        profiles:user_id(
          id,
          first_name,
          last_name,
          email
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching bag requests for admin:', error);
      throw error;
    }

    console.log(`Successfully fetched ${data?.length || 0} wardrobe items`);
    return data || [];
  } catch (error) {
    console.error('Error in getWardrobeItemsForAdmin:', error);
    return [];
  }
}

/**
 * Update wardrobe item status
 */
export async function updateWardrobeItemStatus(itemId: string, status: string) {
  const supabase = createClient();
  const { data, error } = await supabase
    .from('wardrobe_items')
    .update({ status, updated_at: new Date().toISOString() })
    .eq('id', itemId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}
