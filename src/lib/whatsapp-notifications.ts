import { createClient } from '@/lib/supabase-server';

// Twilio WhatsApp configuration
const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN;
const TWILIO_WHATSAPP_NUMBER = process.env.TWILIO_WHATSAPP_NUMBER || 'whatsapp:+***********'; // Twilio Sandbox number

export interface WhatsAppNotificationData {
  type: string;
  title: string;
  message: string;
  data?: any;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export class WhatsAppNotificationService {
  private supabase;

  constructor() {
    this.supabase = createClient();
  }

  // Send WhatsApp notification to admin
  async sendAdminNotification(notification: WhatsAppNotificationData): Promise<boolean> {
    try {
      // Check if <PERSON><PERSON><PERSON> is configured
      if (!TWILIO_ACCOUNT_SID || !TWILIO_AUTH_TOKEN) {
        console.log('<PERSON><PERSON><PERSON> not configured, skipping WhatsApp notification');
        return false;
      }

      // Get admin users with WhatsApp notifications enabled and phone numbers
      const { data: adminUsers, error } = await this.supabase
        .from('profiles')
        .select(`
          id,
          phone_number,
          first_name,
          last_name,
          admin_notification_preferences!inner(
            push_enabled
          )
        `)
        .eq('is_admin', true)
        .eq('admin_notification_preferences.notification_type', notification.type as any)
        .eq('admin_notification_preferences.push_enabled', true)
        .not('phone_number', 'is', null);

      if (error || !adminUsers || adminUsers.length === 0) {
        console.log('No admin users found with WhatsApp notifications enabled for type:', notification.type);
        return false;
      }

      // Send WhatsApp message to each admin
      const whatsappPromises = adminUsers
        .filter(admin => admin.phone_number) // Filter out admins without phone numbers
        .map(admin =>
          this.sendWhatsAppMessage(
            admin.phone_number!,
            this.generateWhatsAppContent(notification, admin),
            notification.priority
          )
        );

      const results = await Promise.all(whatsappPromises);
      return results.some(result => result); // Return true if at least one succeeded

    } catch (error) {
      console.error('Error sending admin WhatsApp notification:', error);
      return false;
    }
  }

  // Send individual WhatsApp message using Twilio API
  private async sendWhatsAppMessage(
    to: string, 
    message: string, 
    priority: string = 'medium'
  ): Promise<boolean> {
    try {
      // Format phone number for WhatsApp (must include whatsapp: prefix)
      const formattedTo = to.startsWith('whatsapp:') ? to : `whatsapp:${to.startsWith('+') ? to : `+${to}`}`;
      
      const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/Messages.json`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${Buffer.from(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          From: TWILIO_WHATSAPP_NUMBER!,
          To: formattedTo,
          Body: message,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Twilio WhatsApp API error:', errorData);
        return false;
      }

      const responseData = await response.json();
      console.log('WhatsApp message sent successfully:', responseData.sid);
      return true;

    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      return false;
    }
  }

  // Generate WhatsApp content based on notification type
  private generateWhatsAppContent(notification: WhatsAppNotificationData, admin: any): string {
    const adminName = admin.first_name || 'Admin';
    const priorityEmoji = this.getPriorityEmoji(notification.priority);
    
    let content = `${priorityEmoji} *Treasures of Maimi Alert*\n\n`;
    content += `Hi ${adminName}! 👋\n\n`;
    content += `*${notification.title}*\n`;
    content += `${notification.message}\n\n`;

    // Add type-specific information with better formatting
    switch (notification.type) {
      case 'new_order':
        content += `📦 *Order Details:*\n`;
        if (notification.data?.order_number) {
          content += `• Order: ${notification.data.order_number}\n`;
        }
        if (notification.data?.total_amount) {
          content += `• Amount: €${notification.data.total_amount}\n`;
        }
        if (notification.data?.customer_email) {
          content += `• Customer: ${notification.data.customer_email}\n`;
        }
        content += `\n🔗 View Order: https://treasuresofmaimi.com/admin/orders\n`;
        break;

      case 'new_message':
        content += `💬 *Message Details:*\n`;
        if (notification.data?.sender_name) {
          content += `• From: ${notification.data.sender_name}\n`;
        }
        if (notification.data?.sender_email) {
          content += `• Email: ${notification.data.sender_email}\n`;
        }
        if (notification.data?.subject) {
          content += `• Subject: ${notification.data.subject}\n`;
        }
        content += `\n🔗 View Messages: https://treasuresofmaimi.com/admin/messages\n`;
        break;

      case 'low_inventory':
        content += `📉 *Inventory Alert*\n`;
        content += `Please check your inventory levels.\n`;
        content += `\n🔗 View Products: https://treasuresofmaimi.com/admin/products\n`;
        break;

      case 'new_bag_request':
        content += `👜 *New Bag Request*\n`;
        content += `A customer has submitted a new bag request.\n`;
        content += `\n🔗 View Requests: https://treasuresofmaimi.com/admin/bag-requests\n`;
        break;

      case 'order_status_change':
        content += `📋 *Order Status Update*\n`;
        if (notification.data?.order_number) {
          content += `• Order: ${notification.data.order_number}\n`;
        }
        if (notification.data?.old_status && notification.data?.new_status) {
          content += `• Status: ${notification.data.old_status} → ${notification.data.new_status}\n`;
        }
        content += `\n🔗 View Order: https://treasuresofmaimi.com/admin/orders\n`;
        break;
    }

    content += `\n---\n`;
    content += `💼 Treasures of Maimi Admin Panel`;

    return content;
  }

  // Get priority emoji
  private getPriorityEmoji(priority?: string): string {
    switch (priority) {
      case 'urgent': return '🚨';
      case 'high': return '⚠️';
      case 'medium': return '📢';
      case 'low': return 'ℹ️';
      default: return '📢';
    }
  }

  // Send a test WhatsApp message to verify setup
  async sendTestMessage(phoneNumber: string): Promise<boolean> {
    try {
      const testMessage = `🧪 *Test Message from Treasures of Maimi*\n\nHi! This is a test WhatsApp notification from your admin panel.\n\nIf you received this message, WhatsApp notifications are working correctly! ✅\n\n---\n💼 Treasures of Maimi Admin Panel`;
      
      return await this.sendWhatsAppMessage(phoneNumber, testMessage, 'low');
    } catch (error) {
      console.error('Error sending test WhatsApp message:', error);
      return false;
    }
  }

  // Validate WhatsApp phone number format
  static validateWhatsAppNumber(phoneNumber: string): boolean {
    // WhatsApp numbers should be in international format
    const whatsappRegex = /^(\+?[1-9]\d{1,14}|whatsapp:\+?[1-9]\d{1,14})$/;
    return whatsappRegex.test(phoneNumber.replace(/[\s\-\(\)]/g, ''));
  }

  // Format phone number for WhatsApp
  static formatWhatsAppNumber(phoneNumber: string): string {
    // Remove all non-digit characters except +
    let cleaned = phoneNumber.replace(/[^\d+]/g, '');
    
    // Remove whatsapp: prefix if present
    if (cleaned.startsWith('whatsapp:')) {
      cleaned = cleaned.replace('whatsapp:', '');
    }
    
    // Ensure it starts with +
    if (!cleaned.startsWith('+')) {
      cleaned = `+${cleaned}`;
    }
    
    return cleaned;
  }
}

// Singleton instance
let _whatsappNotificationService: WhatsAppNotificationService | null = null;

export const whatsappNotificationService = {
  getInstance(): WhatsAppNotificationService {
    if (!_whatsappNotificationService) {
      _whatsappNotificationService = new WhatsAppNotificationService();
    }
    return _whatsappNotificationService;
  }
};
