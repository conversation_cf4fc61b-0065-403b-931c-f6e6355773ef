// Temporarily disabled middleware for faster development startup
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from './lib/database.types';

// Simple in-memory cache to reduce auth requests
const sessionCache = new Map<string, { session: any; timestamp: number }>();
const adminCache = new Map<string, { isAdmin: boolean; timestamp: number }>();
const CACHE_DURATION = 30000; // 30 seconds
const ADMIN_CACHE_DURATION = 60000; // 1 minute for admin status

const publicRoutes = [
  '/',
  '/product/[slug]',
  '/auth/admin/login',
  '/auth/admin/register',
];

const authRoutes = [
  '/auth/login',
  '/auth/admin/login',
  '/auth/admin/register',
];

const protectedRoutes = [
  '/admin',
  '/admin/*',
  '/checkout',
  '/checkout/*',
  '/account',
  '/account/*',
];

export async function middleware(request: NextRequest) {
  // Create a response object
  const response = NextResponse.next();

  const { pathname } = request.nextUrl;

  // Skip middleware for static assets and API routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/images') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico' ||
    pathname === '/robots.txt' ||
    pathname === '/sitemap.xml'
  ) {
    return response;
  }

  // Force /admin/login → /auth/admin/login
  if (pathname === '/admin/login') {
    return NextResponse.redirect(new URL('/auth/admin/login', request.url));
  }

  // Create the Supabase client only when needed
  const supabase = createMiddlewareClient<Database>({ req: request, res: response });

  try {
    // Get session with caching to reduce rate limiting
    let session = null;
    let sessionError = null;
    
    const authHeader = request.headers.get('authorization');
    const cacheKey = authHeader || 'anonymous';
    const cached = sessionCache.get(cacheKey);
    
    // Use cached session if available and fresh
    if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
      session = cached.session;
      console.log('[Middleware] Using cached session');
    } else {
      // Get fresh session with timeout
      const sessionPromise = supabase.auth.getSession();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Session timeout')), 3000) // Increased to 3s
      );

      try {
        const result = await Promise.race([sessionPromise, timeoutPromise]) as any;
        session = result.data?.session;
        sessionError = result.error;
        
        // Cache the result
        sessionCache.set(cacheKey, { session, timestamp: Date.now() });
        
        // Clean old cache entries periodically
        if (Math.random() < 0.1) { // 10% chance
          const cutoff = Date.now() - CACHE_DURATION;
          for (const [key, value] of sessionCache.entries()) {
            if (value.timestamp < cutoff) {
              sessionCache.delete(key);
            }
          }
        }
      } catch (timeoutError) {
        console.error('[Middleware] Session timeout - allowing request through');
        // On timeout, allow request through for better UX
        return response;
      }
    }

    // Reduce logging in development to only essential information
    if (process.env.NODE_ENV === 'development' && pathname.startsWith('/admin')) {
      console.log('[Middleware] Admin route access:', {
        sessionExists: !!session,
        pathname,
      });
    }

    /**
     * Handle public routes
     */
    if (publicRoutes.includes(pathname)) {
      return response;
    }

    /**
     * Handle auth routes when already logged in
     */
    if (pathname === '/auth/login' && session) {
      const redirectTarget = request.nextUrl.searchParams.get('redirect') || '/account';
      return NextResponse.redirect(new URL(redirectTarget, request.url));
    }

    if (pathname === '/auth/admin/login' && session) {
      return NextResponse.redirect(new URL('/admin/dashboard', request.url));
    }

    /**
     * Handle protected routes without session
     */
    const isProtectedRoute = protectedRoutes.some((route) => {
      if (route.endsWith('*')) {
        return pathname.startsWith(route.slice(0, -1));
      }
      return pathname === route;
    });

    if (isProtectedRoute && !session) {
      const loginPath = pathname.startsWith('/admin')
        ? '/auth/admin/login'
        : '/auth/login';
      const redirectUrl = new URL(loginPath, request.url);
      redirectUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(redirectUrl);
    }

    /**
     * Handle admin routes with optimized admin check
     */
    if (pathname.startsWith('/admin') && session) {
      // Skip admin check in development if NEXT_PUBLIC_SKIP_ADMIN_CHECK is true
      if (process.env.NEXT_PUBLIC_SKIP_ADMIN_CHECK === 'true' || process.env.NODE_ENV === 'development') {
        return response;
      }

      try {
        // Check if admin status is cached
        const adminCacheKey = session.user.id;
        const cachedAdmin = adminCache.get(adminCacheKey);
        
        let isAdmin = false;
        
        if (cachedAdmin && (Date.now() - cachedAdmin.timestamp) < ADMIN_CACHE_DURATION) {
          isAdmin = cachedAdmin.isAdmin;
          console.log('[Middleware] Using cached admin status');
        } else {
          // Check if admin status is cached in session metadata first
          const isAdminFromSession = session.user.user_metadata?.is_admin;

          if (isAdminFromSession) {
            isAdmin = true;
            adminCache.set(adminCacheKey, { isAdmin: true, timestamp: Date.now() });
          } else {
            // Only query database if not cached
            const profilePromise = supabase
              .from('profiles')
              .select('is_admin')
              .eq('id', session.user.id)
              .single();

            const profileTimeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Profile timeout')), 2000) // Increased timeout
            );

            try {
              const { data: profileData } = await Promise.race([profilePromise, profileTimeoutPromise]) as any;
              isAdmin = profileData?.is_admin || false;
              
              // Cache the admin status
              adminCache.set(adminCacheKey, { isAdmin, timestamp: Date.now() });
            } catch (profileError) {
              console.error('[Middleware] Profile check timeout - denying access for security');
              return NextResponse.redirect(new URL('/', request.url));
            }
          }
        }

        if (!isAdmin) {
          return NextResponse.redirect(new URL('/', request.url));
        }
      } catch (error) {
        console.error('[Middleware] Error checking admin status:', error);
        // In production, redirect on error for security
        if (process.env.NODE_ENV === 'production') {
          return NextResponse.redirect(new URL('/', request.url));
        }
        return response;
      }
    }

    return response;
  } catch (error) {
    console.error('[Middleware] Session error:', error);
    
    // If it's an admin route and we're in development, allow access
    if (pathname.startsWith('/admin') && process.env.NODE_ENV === 'development') {
      console.log('[Middleware] Allowing admin access in development due to session error');
      return response;
    }
    
    // For other routes, redirect to appropriate login
    const loginPath = pathname.startsWith('/admin') ? '/auth/admin/login' : '/';
    return NextResponse.redirect(new URL(loginPath, request.url));
  }
}

export const config = {
  matcher: [
    // Only match specific protected routes to reduce middleware execution
    '/admin/:path*',
    '/auth/:path*',
    '/checkout/:path*',
    '/account/:path*',
    // Exclude all static assets, API routes, images, and other non-page resources
    '/((?!api|_next|favicon.ico|manifest|icon|robots.txt|sitemap.xml|monitoring|images|.*\\.).*)',
  ],
};