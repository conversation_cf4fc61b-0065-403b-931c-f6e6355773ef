export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: string
          name: string
          description: string
          price: number
          category_id: string
          created_at: string
          updated_at: string
          main_image_url: string | null
        }
        Insert: {
          id?: string
          name: string
          description: string
          price: number
          category_id: string
          created_at?: string
          updated_at?: string
          main_image_url?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string
          price?: number
          category_id?: string
          created_at?: string
          updated_at?: string
          main_image_url?: string | null
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      product_views: {
        Row: {
          id: string
          product_id: string
          user_id: string | null
          viewed_at: string
        }
        Insert: {
          id?: string
          product_id: string
          user_id?: string | null
          viewed_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          user_id?: string | null
          viewed_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          first_name: string | null
          last_name: string | null
          email: string
          created_at: string
          updated_at: string
          is_admin: boolean
        }
        Insert: {
          id: string
          first_name?: string | null
          last_name?: string | null
          email: string
          created_at?: string
          updated_at?: string
          is_admin?: boolean
        }
        Update: {
          id?: string
          first_name?: string | null
          last_name?: string | null
          email?: string
          created_at?: string
          updated_at?: string
          is_admin?: boolean
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_daily_product_views: {
        Args: Record<string, never>
        Returns: {
          view_day: string
          product_id: string
          product_name: string
          views: number
        }[]
      }
      get_top_viewed_products: {
        Args: {
          days: number
        }
        Returns: {
          id: string
          name: string
          views: number
        }[]
      }
      get_views_per_day: {
        Args: {
          days: number
        }
        Returns: {
          view_day: string
          total_views: number
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
