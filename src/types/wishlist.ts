export interface WishlistProduct {
  id: string;
  name: string;
  slug: string;
  price: number;
  sale_price?: number | null;
  image_url?: string | null;
  category_name?: string | null;
  brand?: string | null;
}

export interface WishlistItem {
  id: string;
  product_id: string;
  user_id?: string;
  created_at: string | null;
  product?: WishlistProduct;
}

export interface WishlistState {
  items: WishlistItem[];
  isLoading: boolean;
  error: string | null;
}

export interface WishlistContextType {
  wishlist: WishlistState;
  addToWishlist: (productId: string) => Promise<boolean>;
  removeFromWishlist: (productId: string) => Promise<void>;
  isInWishlist: (productId: string) => boolean;
  clearWishlist: () => Promise<void>;
  refreshWishlist: () => Promise<void>;
  mergeLocalWishlistWithDB: (localItems: WishlistItem[]) => Promise<void>;
}
