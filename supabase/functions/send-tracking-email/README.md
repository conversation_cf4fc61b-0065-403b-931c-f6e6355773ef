# Send Tracking Email Edge Function

This Supabase Edge Function sends order tracking notification emails to customers using the Resend API.

## Prerequisites

1. Supabase CLI installed
2. Resend API key (provided: `re_iW1g7ivL_2WXd11E89qmjXBvU1qnRURzY`)
3. A verified domain in Resend

## Deployment Instructions

### 1. Deploy the Edge Function

```bash
supabase functions deploy send-tracking-email
```

### 2. Set the Resend API Key as a Secret

```bash
supabase secrets set RESEND_API_KEY=re_iW1g7ivL_2WXd11E89qmjXBvU1qnRURzY
```

### 3. Test the Function

You can test the function by calling it with sample data:

```bash
curl -X POST 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/send-tracking-email' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"order_number":"ORD123456","email":"<EMAIL>","tracking_number":"TRK123456","carrier":"DHL","tracking_url":"https://track.dhl.com/123456"}'
```

## Function Details

The function expects a JSON payload with the following fields:
- `order_number`: The order number
- `email`: Customer's email address
- `tracking_number`: Shipping tracking number
- `carrier`: Shipping carrier name (optional)
- `tracking_url`: URL to track the shipment (optional)

## Customization

You can customize the email template by modifying the HTML in the `body` variable in `index.ts`.

## Troubleshooting

If emails are not being sent:
1. Check that the Resend API key is correctly set
2. Verify that the FROM_EMAIL is from a verified domain in your Resend account
3. Check the function logs for any errors: `supabase functions logs send-tracking-email`
