import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY');
const FROM_EMAIL = '<EMAIL>'; // Replace with your verified domain in Resend

serve(async (req) => {
  const { order_number, email, tracking_number, carrier, tracking_url } = await req.json();

  const body = `
    <p>Hi there,</p>
    <p>Your order <strong>${order_number}</strong> has been shipped!</p>
    <p>Carrier: ${carrier || '—'}</p>
    <p>Tracking Number: <strong>${tracking_number}</strong></p>
    ${
      tracking_url
        ? `<p><a href="${tracking_url}" target="_blank">Track your shipment</a></p>`
        : ''
    }
    <p>Thank you for shopping with Treasures of Maimi!</p>
  `;

  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${RESEND_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      from: FROM_EMAIL,
      to: email,
      subject: `Your order ${order_number} is on the way!`,
      html: body,
    }),
  });

  if (!response.ok) {
    const err = await response.json();
    console.error('Resend error:', err);
    return new Response('Failed to send email', { status: 500 });
  }

  return new Response('Email sent successfully', { status: 200 });
});
