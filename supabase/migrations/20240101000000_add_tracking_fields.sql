-- Add tracking number, carrier, and tracking URL columns to orders table
ALTER TABLE IF EXISTS "public"."orders" 
ADD COLUMN IF NOT EXISTS "tracking_number" text,
ADD COLUMN IF NOT EXISTS "carrier" text,
ADD COLUMN IF NOT EXISTS "tracking_url" text;

-- Add comment to explain the purpose of these columns
COMMENT ON COLUMN "public"."orders"."tracking_number" IS 'Shipping tracking number for the order';
COMMENT ON COLUMN "public"."orders"."carrier" IS 'Shipping carrier name (e.g., ValenciaPost, DHL)';
COMMENT ON COLUMN "public"."orders"."tracking_url" IS 'URL to track the shipment';

-- Create index on tracking_number for faster lookups
CREATE INDEX IF NOT EXISTS "orders_tracking_number_idx" ON "public"."orders" ("tracking_number");
