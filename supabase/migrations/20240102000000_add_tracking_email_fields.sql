-- Add fields to track email delivery status
ALTER TABLE IF EXISTS "public"."orders" 
ADD COLUMN IF NOT EXISTS "tracking_email_sent" BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS "tracking_email_sent_at" TIMESTAMP WITH TIME ZONE;

-- Add comment to explain the purpose of these columns
COMMENT ON COLUMN "public"."orders"."tracking_email_sent" IS 'Whether a tracking notification email was sent to the customer';
COMMENT ON COLUMN "public"."orders"."tracking_email_sent_at" IS 'When the tracking notification email was sent';
