-- Enable RLS
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;

-- Add foreign key constraint
ALTER TABLE wishlists 
ADD CONSTRAINT wishlists_product_id_fkey 
FOREIGN KEY (product_id) 
REFERENCES products(id) 
ON DELETE CASCADE;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own wishlist items" ON wishlists;
DROP POLICY IF EXISTS "Users can add items to their wishlist" ON wishlists;
DROP POLICY IF EXISTS "Users can delete their wishlist items" ON wishlists;

-- Create comprehensive policies
CREATE POLICY "Users can view their own wishlist items"
ON wishlists FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can add items to their wishlist"
ON wishlists FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their wishlist items"
ON wishlists FOR DELETE
USING (auth.uid() = user_id);

-- Force RLS
ALTER TABLE wishlists FORCE ROW LEVEL SECURITY;

-- Refresh Supabase metadata
SELECT * FROM wishlists LIMIT 1; 