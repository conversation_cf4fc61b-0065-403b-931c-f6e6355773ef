-- First, migrate any existing data
CREATE TABLE IF NOT EXISTS wishlists (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  UNIQUE(user_id, product_id)
);

-- Copy data if old table exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'wishlist') THEN
    INSERT INTO wishlists (id, user_id, product_id, created_at)
    SELECT id, user_id, product_id, created_at FROM wishlist;
    
    -- Drop the old table
    DROP TABLE wishlist;
  END IF;
END $$; 