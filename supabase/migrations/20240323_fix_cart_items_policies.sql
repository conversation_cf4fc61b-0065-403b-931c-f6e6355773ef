-- Enable RLS
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can insert their cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can update their cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can delete their cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can view their cart items" ON cart_items;

-- Create proper policies
CREATE POLICY "Users can insert their cart items"
ON cart_items
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their cart items"
ON cart_items
FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their cart items"
ON cart_items
FOR DELETE
USING (auth.uid() = user_id);

CREATE POLICY "Users can view their cart items"
ON cart_items
FOR SELECT
USING (auth.uid() = user_id);

-- Add unique constraint for user_id and product_id
ALTER TABLE cart_items
DROP CONSTRAINT IF EXISTS unique_user_product;

ALTER TABLE cart_items
ADD CONSTRAINT unique_user_product UNIQUE (user_id, product_id); 