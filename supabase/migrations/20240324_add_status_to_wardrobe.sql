-- Add status column to wardrobe_items table
ALTER TABLE wardrobe_items
ADD COLUMN IF NOT EXISTS status text DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS priority text DEFAULT 'medium';

-- Update existing rows to have 'pending' status and 'medium' priority
UPDATE wardrobe_items
SET status = 'pending',
    priority = 'medium'
WHERE status IS NULL OR priority IS NULL;

-- Enable RLS
ALTER TABLE wardrobe_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

-- Create policies for wardrobe_items
CREATE POLICY "Users can insert their wardrobe items"
ON wardrobe_items
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their wardrobe items"
ON wardrobe_items
FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their wardrobe items"
ON wardrobe_items
FOR DELETE
USING (auth.uid() = user_id);

CREATE POLICY "Users can view their wardrobe items"
ON wardrobe_items
FOR SELECT
USING (auth.uid() = user_id);

-- Allow admins to view all wardrobe items
CREATE POLICY "Admins can view all wardrobe items"
ON wardrobe_items
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Create policies for orders
CREATE POLICY "Users can view their orders"
ON orders
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their orders"
ON orders
FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all orders"
ON orders
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

CREATE POLICY "Admins can update all orders"
ON orders
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Enable RLS for order_items table
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own order items
CREATE POLICY "Users can view their order items"
ON order_items
FOR SELECT
USING (
  EXISTS (
    SELECT 1
    FROM orders
    WHERE orders.id = order_items.order_id
    AND orders.user_id = auth.uid()
  )
);

-- Allow users to insert order items (for their own orders)
CREATE POLICY "Users can insert order items"
ON order_items
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM orders
    WHERE orders.id = order_items.order_id
    AND orders.user_id = auth.uid()
  )
);

-- Allow admins to view all order items
CREATE POLICY "Admins can view all order items"
ON order_items
FOR SELECT
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- Allow admins to insert/update order items
CREATE POLICY "Admins can manage order items"
ON order_items
FOR ALL
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
); 