-- Create notification management functions

-- Function to create a notification for all admins
CREATE OR REPLACE FUNCTION create_admin_notification(
  p_type notification_type,
  p_title TEXT,
  p_message TEXT,
  p_data JSONB DEFAULT '{}',
  p_priority notification_priority DEFAULT 'medium'
)
R<PERSON><PERSON>NS UUID AS $$
DECLARE
  admin_user_id UUID;
  notification_id UUID;
BEGIN
  -- Get all admin users
  FOR admin_user_id IN 
    SELECT id FROM profiles WHERE is_admin = true
  LOOP
    -- Create notification for each admin
    INSERT INTO notifications (user_id, type, title, message, data, priority)
    VALUES (admin_user_id, p_type, p_title, p_message, p_data, p_priority)
    RETURNING id INTO notification_id;
  END LOOP;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(p_notification_id UUID)
R<PERSON>URNS BOOLEAN AS $$
BEGIN
  UPDATE notifications 
  SET status = 'read', read_at = NOW()
  WHERE id = p_notification_id AND user_id = auth.uid();
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(p_user_id UUID DEFAULT auth.uid())
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM notifications
    WHERE user_id = p_user_id 
    AND status != 'read'
    AND (expires_at IS NULL OR expires_at > NOW())
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old notifications
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete notifications older than 30 days or expired
  DELETE FROM notifications
  WHERE (created_at < NOW() - INTERVAL '30 days')
     OR (expires_at IS NOT NULL AND expires_at < NOW());
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create order notification
CREATE OR REPLACE FUNCTION notify_new_order(p_order_id UUID)
RETURNS UUID AS $$
DECLARE
  order_info RECORD;
  notification_id UUID;
BEGIN
  -- Get order information
  SELECT 
    order_number,
    total_amount,
    customer_email,
    status
  INTO order_info
  FROM orders
  WHERE id = p_order_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Order not found: %', p_order_id;
  END IF;
  
  -- Create notification
  SELECT create_admin_notification(
    'new_order',
    'New Order Received',
    format('Order #%s for €%.2f from %s', 
           COALESCE(order_info.order_number, 'N/A'), 
           order_info.total_amount, 
           COALESCE(order_info.customer_email, 'Unknown')),
    jsonb_build_object(
      'order_id', p_order_id,
      'order_number', order_info.order_number,
      'total_amount', order_info.total_amount,
      'customer_email', order_info.customer_email
    ),
    'high'
  ) INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create bag request notification
CREATE OR REPLACE FUNCTION notify_new_bag_request(p_request_id UUID)
RETURNS UUID AS $$
DECLARE
  request_info RECORD;
  notification_id UUID;
BEGIN
  -- Get bag request information
  SELECT 
    w.name,
    w.brand,
    w.category,
    p.email as user_email
  INTO request_info
  FROM wardrobe_items w
  JOIN profiles p ON w.user_id = p.id
  WHERE w.id = p_request_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Bag request not found: %', p_request_id;
  END IF;
  
  -- Create notification
  SELECT create_admin_notification(
    'new_bag_request',
    'New Bag Request',
    format('New request for %s %s from %s', 
           COALESCE(request_info.brand, ''), 
           COALESCE(request_info.name, 'Unknown bag'),
           COALESCE(request_info.user_email, 'Unknown user')),
    jsonb_build_object(
      'request_id', p_request_id,
      'bag_name', request_info.name,
      'brand', request_info.brand,
      'category', request_info.category,
      'user_email', request_info.user_email
    ),
    'medium'
  ) INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create low inventory notification
CREATE OR REPLACE FUNCTION notify_low_inventory(p_product_id UUID)
RETURNS UUID AS $$
DECLARE
  product_info RECORD;
  notification_id UUID;
BEGIN
  -- Get product information
  SELECT 
    name,
    quantity,
    price
  INTO product_info
  FROM products
  WHERE id = p_product_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Product not found: %', p_product_id;
  END IF;
  
  -- Only notify if quantity is low (less than 3)
  IF product_info.quantity >= 3 THEN
    RETURN NULL;
  END IF;
  
  -- Create notification
  SELECT create_admin_notification(
    'low_inventory',
    'Low Inventory Alert',
    format('Product "%s" has only %s item(s) remaining', 
           product_info.name, 
           product_info.quantity),
    jsonb_build_object(
      'product_id', p_product_id,
      'product_name', product_info.name,
      'quantity', product_info.quantity,
      'price', product_info.price
    ),
    CASE 
      WHEN product_info.quantity = 0 THEN 'urgent'
      WHEN product_info.quantity = 1 THEN 'high'
      ELSE 'medium'
    END
  ) INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
