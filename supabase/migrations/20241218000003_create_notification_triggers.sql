-- Create triggers for automatic notifications

-- Trigger function for new orders
CREATE OR REPLACE FUNCTION trigger_new_order_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Only notify for new orders, not updates
  IF TG_OP = 'INSERT' THEN
    PERFORM notify_new_order(NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for order status changes
CREATE OR REPLACE FUNCTION trigger_order_status_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Only notify if status actually changed
  IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
    PERFORM create_admin_notification(
      'order_status_change',
      'Order Status Updated',
      format('Order #%s status changed from %s to %s', 
             COALESCE(NEW.order_number, 'N/A'), 
             OLD.status, 
             NEW.status),
      jsonb_build_object(
        'order_id', NEW.id,
        'order_number', NEW.order_number,
        'old_status', OLD.status,
        'new_status', NEW.status,
        'total_amount', NEW.total_amount
      ),
      'medium'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for new bag requests
CREATE OR REPLACE FUNCTION trigger_new_bag_request_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Only notify for new requests with pending status
  IF TG_OP = 'INSERT' AND NEW.status = 'pending' THEN
    PERFORM notify_new_bag_request(NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for bag request status changes
CREATE OR REPLACE FUNCTION trigger_bag_request_status_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Only notify if status actually changed and it's not a new record
  IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
    PERFORM create_admin_notification(
      'bag_request_status_change',
      'Bag Request Status Updated',
      format('Bag request for %s %s status changed from %s to %s', 
             COALESCE(NEW.brand, ''), 
             COALESCE(NEW.name, 'Unknown bag'),
             OLD.status, 
             NEW.status),
      jsonb_build_object(
        'request_id', NEW.id,
        'bag_name', NEW.name,
        'brand', NEW.brand,
        'old_status', OLD.status,
        'new_status', NEW.status,
        'user_id', NEW.user_id
      ),
      'low'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for low inventory
CREATE OR REPLACE FUNCTION trigger_low_inventory_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if quantity decreased and is now low
  IF TG_OP = 'UPDATE' AND NEW.quantity < OLD.quantity AND NEW.quantity <= 2 THEN
    PERFORM notify_low_inventory(NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for new contact messages
CREATE OR REPLACE FUNCTION trigger_new_message_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Only notify for new messages
  IF TG_OP = 'INSERT' THEN
    PERFORM create_admin_notification(
      'new_message',
      'New Contact Message',
      format('New message from %s: %s', 
             COALESCE(NEW.name, 'Unknown'),
             LEFT(NEW.message, 100) || CASE WHEN LENGTH(NEW.message) > 100 THEN '...' ELSE '' END),
      jsonb_build_object(
        'message_id', NEW.id,
        'sender_name', NEW.name,
        'sender_email', NEW.email,
        'subject', NEW.subject,
        'message_preview', LEFT(NEW.message, 200)
      ),
      'medium'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the actual triggers
DROP TRIGGER IF EXISTS notify_new_order ON orders;
CREATE TRIGGER notify_new_order
  AFTER INSERT ON orders
  FOR EACH ROW
  EXECUTE FUNCTION trigger_new_order_notification();

DROP TRIGGER IF EXISTS notify_order_status_change ON orders;
CREATE TRIGGER notify_order_status_change
  AFTER UPDATE ON orders
  FOR EACH ROW
  EXECUTE FUNCTION trigger_order_status_notification();

DROP TRIGGER IF EXISTS notify_new_bag_request ON wardrobe_items;
CREATE TRIGGER notify_new_bag_request
  AFTER INSERT ON wardrobe_items
  FOR EACH ROW
  EXECUTE FUNCTION trigger_new_bag_request_notification();

DROP TRIGGER IF EXISTS notify_bag_request_status_change ON wardrobe_items;
CREATE TRIGGER notify_bag_request_status_change
  AFTER UPDATE ON wardrobe_items
  FOR EACH ROW
  EXECUTE FUNCTION trigger_bag_request_status_notification();

DROP TRIGGER IF EXISTS notify_low_inventory ON products;
CREATE TRIGGER notify_low_inventory
  AFTER UPDATE ON products
  FOR EACH ROW
  EXECUTE FUNCTION trigger_low_inventory_notification();

-- Check if contact_messages table exists before creating trigger
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'contact_messages') THEN
    DROP TRIGGER IF EXISTS notify_new_message ON contact_messages;
    CREATE TRIGGER notify_new_message
      AFTER INSERT ON contact_messages
      FOR EACH ROW
      EXECUTE FUNCTION trigger_new_message_notification();
  END IF;
END $$;

-- Insert default notification preferences for existing admins
INSERT INTO admin_notification_preferences (user_id, notification_type, push_enabled, email_enabled, in_app_enabled)
SELECT 
  p.id,
  nt.type,
  true,
  true,
  true
FROM profiles p
CROSS JOIN (
  SELECT unnest(enum_range(NULL::notification_type)) as type
) nt
WHERE p.is_admin = true
ON CONFLICT (user_id, notification_type) DO NOTHING;
