-- Add missing fields to products table
ALTER TABLE products
ADD COLUMN IF NOT EXISTS image_url TEXT,
ADD COLUMN IF NOT EXISTS sale_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS brand TEXT;

-- Create a function to update products with default image URLs if they're null
CREATE OR REPLACE FUNCTION set_default_product_images()
RETURNS VOID AS $$
BEGIN
  UPDATE products
  SET image_url = 'https://via.placeholder.com/500x500?text=Product+Image'
  WHERE image_url IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT set_default_product_images();
