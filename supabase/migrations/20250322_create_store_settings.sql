-- Create store_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS store_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  store_name TEXT NOT NULL DEFAULT 'Treasures of Maimi',
  contact_email TEXT NOT NULL DEFAULT '<EMAIL>',
  currency TEXT NOT NULL DEFAULT 'EUR',
  shipping_policy TEXT NOT NULL DEFAULT 'Free shipping on orders over €50. Standard delivery takes 3-5 business days.',
  return_policy TEXT NOT NULL DEFAULT 'Returns accepted within 30 days of purchase. Items must be in original condition with tags attached.',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default settings if table is empty
INSERT INTO store_settings (id, store_name, contact_email, currency, shipping_policy, return_policy)
SELECT 
  uuid_generate_v4(),
  'Treasures of Maimi',
  '<EMAIL>',
  'EUR',
  'Free shipping on orders over €50. Standard delivery takes 3-5 business days.',
  'Returns accepted within 30 days of purchase. Items must be in original condition with tags attached.'
WHERE NOT EXISTS (SELECT 1 FROM store_settings);

-- Create trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_store_settings_timestamp ON store_settings;
CREATE TRIGGER update_store_settings_timestamp
BEFORE UPDATE ON store_settings
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();
