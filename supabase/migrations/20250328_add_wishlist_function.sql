-- Create a stored procedure to add items to the wishlist
-- This function will handle the insert operation and handle duplicates gracefully
CREATE OR REPLACE FUNCTION add_to_wishlist(p_product_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  v_exists BOOLEAN;
BEGIN
  -- Get the current user ID from the auth context
  v_user_id := auth.uid();
  
  -- Check if the user is authenticated
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;
  
  -- Check if the product exists
  IF NOT EXISTS (SELECT 1 FROM products WHERE id = p_product_id) THEN
    RAISE EXCEPTION 'Product not found';
  END IF;
  
  -- Check if the item is already in the wishlist
  SELECT EXISTS (
    SELECT 1 
    FROM wishlists 
    WHERE user_id = v_user_id AND product_id = p_product_id
  ) INTO v_exists;
  
  -- If it already exists, return success without inserting
  IF v_exists THEN
    RETURN TRUE;
  END IF;
  
  -- Insert the item into the wishlist
  INSERT INTO wishlists (user_id, product_id)
  VALUES (v_user_id, p_product_id);
  
  RETURN TRUE;
EXCEPTION
  WHEN unique_violation THEN
    -- Handle the case where the item was added concurrently
    RETURN TRUE;
  WHEN OTHERS THEN
    -- Log the error and return false
    RAISE NOTICE 'Error adding item to wishlist: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION add_to_wishlist(UUID) TO authenticated;
