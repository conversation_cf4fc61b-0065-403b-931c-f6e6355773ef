-- Critical Security Fixes for Shop Maimi Database
-- Fix missing RLS policies and security vulnerabilities
-- Date: 2025-06-17

-- Enable RLS on critical tables that were missing it
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE store_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipping_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON profiles;

DROP POLICY IF EXISTS "store_settings_admin_only" ON store_settings;
DROP POLICY IF EXISTS "shipping_addresses_user_policy" ON shipping_addresses;
DROP POLICY IF EXISTS "order_items_user_policy" ON order_items;
DROP POLICY IF EXISTS "product_media_public_select" ON product_media;
DROP POLICY IF EXISTS "product_media_admin_all" ON product_media;
DROP POLICY IF EXISTS "cart_items_user_policy" ON cart_items;
DROP POLICY IF EXISTS "wishlists_user_policy" ON wishlists;

-- 1. PROFILES TABLE - Critical: User personal data protection
CREATE POLICY "profiles_select_policy" ON profiles
    FOR SELECT USING (
        auth.uid() = id OR 
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

CREATE POLICY "profiles_update_policy" ON profiles
    FOR UPDATE USING (
        auth.uid() = id OR
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

CREATE POLICY "profiles_insert_policy" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 2. STORE_SETTINGS TABLE - Critical: Business configuration protection
CREATE POLICY "store_settings_admin_only" ON store_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- 3. SHIPPING_ADDRESSES TABLE - Critical: Customer address protection
CREATE POLICY "shipping_addresses_user_policy" ON shipping_addresses
    FOR ALL USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- 4. ORDER_ITEMS TABLE - Critical: Order manipulation prevention
CREATE POLICY "order_items_user_policy" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o 
            WHERE o.id = order_id AND (
                o.user_id = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM profiles p 
                    WHERE p.id = auth.uid() AND p.is_admin = true
                )
            )
        )
    );

-- Prevent direct order_items manipulation - must use secure functions
CREATE POLICY "order_items_admin_only_insert" ON order_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

CREATE POLICY "order_items_admin_only_update" ON order_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- 5. PRODUCT_MEDIA TABLE - Critical: Media management security
CREATE POLICY "product_media_public_select" ON product_media
    FOR SELECT USING (true);  -- Public read access for product images

CREATE POLICY "product_media_admin_all" ON product_media
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- 6. CART_ITEMS TABLE - Critical: Shopping cart security
CREATE POLICY "cart_items_user_policy" ON cart_items
    FOR ALL USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- 7. WISHLISTS TABLE - Critical: Wishlist privacy
CREATE POLICY "wishlists_user_policy" ON wishlists
    FOR ALL USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- 8. ORDERS TABLE - Enhanced security policies
DROP POLICY IF EXISTS "orders_user_policy" ON orders;
DROP POLICY IF EXISTS "orders_admin_policy" ON orders;

CREATE POLICY "orders_user_select" ON orders
    FOR SELECT USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- Prevent direct order manipulation by users
CREATE POLICY "orders_admin_modify" ON orders
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

CREATE POLICY "orders_admin_update" ON orders
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- 9. Create secure order creation function to prevent price manipulation
CREATE OR REPLACE FUNCTION create_secure_order(
    p_user_id UUID,
    p_items JSONB,
    p_shipping_cost DECIMAL DEFAULT 0,
    p_customer_email TEXT DEFAULT NULL
)
RETURNS TABLE(order_id UUID, total_amount DECIMAL)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_order_id UUID;
    v_total DECIMAL := 0;
    v_item JSONB;
    v_product_price DECIMAL;
BEGIN
    -- Validate user exists
    IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
        RAISE EXCEPTION 'Invalid user ID';
    END IF;
    
    -- Create the order
    INSERT INTO orders (
        user_id, 
        status, 
        payment_status,
        customer_email,
        created_at,
        updated_at
    ) VALUES (
        p_user_id,
        'pending',
        'pending',
        p_customer_email,
        NOW(),
        NOW()
    ) RETURNING id INTO v_order_id;
    
    -- Process each item and validate prices from database
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        -- Get actual product price from database (prevent price manipulation)
        SELECT price INTO v_product_price 
        FROM products 
        WHERE id = (v_item->>'product_id')::UUID;
        
        IF v_product_price IS NULL THEN
            RAISE EXCEPTION 'Product not found: %', v_item->>'product_id';
        END IF;
        
        -- Insert order item with validated price
        INSERT INTO order_items (
            order_id,
            product_id,
            quantity,
            price,
            created_at
        ) VALUES (
            v_order_id,
            (v_item->>'product_id')::UUID,
            (v_item->>'quantity')::INTEGER,
            v_product_price,  -- Use validated price from database
            NOW()
        );
        
        -- Add to total
        v_total := v_total + (v_product_price * (v_item->>'quantity')::INTEGER);
    END LOOP;
    
    -- Add shipping to total
    v_total := v_total + p_shipping_cost;
    
    -- Update order with calculated total
    UPDATE orders 
    SET total_amount = v_total, updated_at = NOW()
    WHERE id = v_order_id;
    
    RETURN QUERY SELECT v_order_id, v_total;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_secure_order TO authenticated;

-- 10. Create audit log table for security monitoring
CREATE TABLE IF NOT EXISTS security_audit_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    action TEXT NOT NULL,
    table_name TEXT,
    record_id TEXT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on audit log
ALTER TABLE security_audit_log ENABLE ROW LEVEL SECURITY;

-- Only admins can read audit logs
CREATE POLICY "audit_log_admin_only" ON security_audit_log
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- 11. Performance indexes for RLS policies
CREATE INDEX IF NOT EXISTS idx_profiles_admin ON profiles(is_admin) WHERE is_admin = true;
CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON profiles(id);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_shipping_addresses_user_id ON shipping_addresses(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_user_id ON cart_items(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlists_user_id ON wishlists(user_id);

-- 12. Create function to check admin status consistently
CREATE OR REPLACE FUNCTION is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT COALESCE(
        (SELECT is_admin FROM profiles WHERE id = user_id), 
        false
    );
$$;

GRANT EXECUTE ON FUNCTION is_admin TO authenticated, anon;

-- Migration completed: Critical security fixes including RLS policies, secure order creation, audit logging, and performance indexes