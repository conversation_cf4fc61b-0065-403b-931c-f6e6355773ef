-- Discount Code System for Shop Maimi
-- Summer 2025 Campaign and General Discount Management
-- Date: 2025-06-17

-- 1. Create discount_codes table
CREATE TABLE IF NOT EXISTS discount_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code TEXT UNIQUE NOT NULL,
    description TEXT,
    discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')),
    discount_value DECIMAL(10,2) NOT NULL CHECK (discount_value > 0),
    minimum_order_amount DECIMAL(10,2) DEFAULT 0,
    maximum_discount_amount DECIMAL(10,2), -- For percentage discounts
    usage_limit INTEGER, -- NULL = unlimited
    used_count INTEGER DEFAULT 0,
    valid_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    applies_to TEXT DEFAULT 'all' CHECK (applies_to IN ('all', 'specific_products', 'categories')),
    applicable_product_ids UUID[], -- Array of product IDs if applies_to = 'specific_products'
    applicable_category_ids UUID[], -- Array of category IDs if applies_to = 'categories'
    single_use_per_customer BOOLEAN DEFAULT false,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create discount_code_usage table to track usage
CREATE TABLE IF NOT EXISTS discount_code_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    discount_code_id UUID REFERENCES discount_codes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id),
    order_id UUID REFERENCES orders(id),
    discount_amount DECIMAL(10,2) NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    customer_email TEXT -- For guest checkouts
);

-- 3. Add discount fields to orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS discount_code_id UUID REFERENCES discount_codes(id);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10,2) DEFAULT 0;

-- 4. Enable RLS on new tables
ALTER TABLE discount_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE discount_code_usage ENABLE ROW LEVEL SECURITY;

-- 5. RLS Policies for discount_codes
-- Public can read active codes for validation
CREATE POLICY "discount_codes_public_read" ON discount_codes
    FOR SELECT USING (is_active = true AND valid_from <= NOW() AND (valid_until IS NULL OR valid_until >= NOW()));

-- Only admins can create, update, delete discount codes
CREATE POLICY "discount_codes_admin_all" ON discount_codes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- 6. RLS Policies for discount_code_usage
-- Users can see their own usage
CREATE POLICY "discount_usage_user_read" ON discount_code_usage
    FOR SELECT USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() AND p.is_admin = true
        )
    );

-- Only system can insert usage records
CREATE POLICY "discount_usage_system_insert" ON discount_code_usage
    FOR INSERT WITH CHECK (true); -- Will be handled by secure functions

-- 7. Create function to validate and apply discount code
CREATE OR REPLACE FUNCTION validate_and_apply_discount(
    p_code TEXT,
    p_user_id UUID DEFAULT NULL,
    p_customer_email TEXT DEFAULT NULL,
    p_order_total DECIMAL,
    p_order_items JSONB DEFAULT '[]'::jsonb
)
RETURNS TABLE(
    is_valid BOOLEAN,
    discount_id UUID,
    discount_amount DECIMAL,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_discount_code discount_codes%ROWTYPE;
    v_calculated_discount DECIMAL := 0;
    v_usage_count INTEGER;
    v_user_usage_count INTEGER := 0;
    v_error TEXT := NULL;
BEGIN
    -- Find the discount code
    SELECT * INTO v_discount_code
    FROM discount_codes
    WHERE code = UPPER(p_code) AND is_active = true;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, NULL::UUID, 0::DECIMAL, 'Invalid discount code'::TEXT;
        RETURN;
    END IF;
    
    -- Check if code is currently valid (date range)
    IF v_discount_code.valid_from > NOW() THEN
        RETURN QUERY SELECT false, NULL::UUID, 0::DECIMAL, 'Discount code is not yet active'::TEXT;
        RETURN;
    END IF;
    
    IF v_discount_code.valid_until IS NOT NULL AND v_discount_code.valid_until < NOW() THEN
        RETURN QUERY SELECT false, NULL::UUID, 0::DECIMAL, 'Discount code has expired'::TEXT;
        RETURN;
    END IF;
    
    -- Check minimum order amount
    IF p_order_total < v_discount_code.minimum_order_amount THEN
        RETURN QUERY SELECT false, NULL::UUID, 0::DECIMAL, 
            format('Minimum order amount of €%.2f required', v_discount_code.minimum_order_amount)::TEXT;
        RETURN;
    END IF;
    
    -- Check usage limits
    IF v_discount_code.usage_limit IS NOT NULL THEN
        SELECT used_count INTO v_usage_count FROM discount_codes WHERE id = v_discount_code.id;
        IF v_usage_count >= v_discount_code.usage_limit THEN
            RETURN QUERY SELECT false, NULL::UUID, 0::DECIMAL, 'Discount code usage limit reached'::TEXT;
            RETURN;
        END IF;
    END IF;
    
    -- Check single use per customer
    IF v_discount_code.single_use_per_customer AND p_user_id IS NOT NULL THEN
        SELECT COUNT(*) INTO v_user_usage_count
        FROM discount_code_usage
        WHERE discount_code_id = v_discount_code.id 
        AND (user_id = p_user_id OR customer_email = p_customer_email);
        
        IF v_user_usage_count > 0 THEN
            RETURN QUERY SELECT false, NULL::UUID, 0::DECIMAL, 'You have already used this discount code'::TEXT;
            RETURN;
        END IF;
    END IF;
    
    -- Calculate discount amount
    IF v_discount_code.discount_type = 'percentage' THEN
        v_calculated_discount := p_order_total * (v_discount_code.discount_value / 100);
        -- Apply maximum discount limit if set
        IF v_discount_code.maximum_discount_amount IS NOT NULL THEN
            v_calculated_discount := LEAST(v_calculated_discount, v_discount_code.maximum_discount_amount);
        END IF;
    ELSE -- fixed_amount
        v_calculated_discount := LEAST(v_discount_code.discount_value, p_order_total);
    END IF;
    
    -- Round to 2 decimal places
    v_calculated_discount := ROUND(v_calculated_discount, 2);
    
    RETURN QUERY SELECT true, v_discount_code.id, v_calculated_discount, NULL::TEXT;
END;
$$;

-- 8. Create function to record discount usage
CREATE OR REPLACE FUNCTION record_discount_usage(
    p_discount_code_id UUID,
    p_user_id UUID DEFAULT NULL,
    p_order_id UUID DEFAULT NULL,
    p_discount_amount DECIMAL,
    p_customer_email TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Insert usage record
    INSERT INTO discount_code_usage (
        discount_code_id,
        user_id,
        order_id,
        discount_amount,
        customer_email,
        used_at
    ) VALUES (
        p_discount_code_id,
        p_user_id,
        p_order_id,
        p_discount_amount,
        p_customer_email,
        NOW()
    );
    
    -- Update usage count
    UPDATE discount_codes 
    SET used_count = used_count + 1,
        updated_at = NOW()
    WHERE id = p_discount_code_id;
    
    RETURN TRUE;
EXCEPTION WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- 9. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_discount_codes_code ON discount_codes(code);
CREATE INDEX IF NOT EXISTS idx_discount_codes_active ON discount_codes(is_active, valid_from, valid_until);
CREATE INDEX IF NOT EXISTS idx_discount_usage_code_id ON discount_code_usage(discount_code_id);
CREATE INDEX IF NOT EXISTS idx_discount_usage_user_id ON discount_code_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_discount_code ON orders(discount_code_id);

-- 10. Grant permissions
GRANT EXECUTE ON FUNCTION validate_and_apply_discount TO authenticated, anon;
GRANT EXECUTE ON FUNCTION record_discount_usage TO authenticated;

-- 11. No pre-defined discount codes - let the admin create them via the admin panel
-- This gives the business owner full creative control over their promotional campaigns
-- She can create unique codes, set her own discount amounts, terms, and conditions

-- 12. Create a view for admin dashboard analytics
CREATE OR REPLACE VIEW discount_analytics AS
SELECT 
    dc.id,
    dc.code,
    dc.description,
    dc.discount_type,
    dc.discount_value,
    dc.used_count,
    dc.usage_limit,
    COALESCE(dc.usage_limit - dc.used_count, -1) as remaining_uses,
    dc.valid_from,
    dc.valid_until,
    dc.is_active,
    COUNT(dcu.id) as total_usage_records,
    COALESCE(SUM(dcu.discount_amount), 0) as total_discount_given,
    ROUND(
        CASE 
            WHEN dc.usage_limit IS NOT NULL 
            THEN (dc.used_count::DECIMAL / dc.usage_limit) * 100 
            ELSE 0 
        END, 2
    ) as usage_percentage,
    dc.created_at,
    dc.updated_at
FROM discount_codes dc
LEFT JOIN discount_code_usage dcu ON dc.id = dcu.discount_code_id
GROUP BY dc.id, dc.code, dc.description, dc.discount_type, dc.discount_value, 
         dc.used_count, dc.usage_limit, dc.valid_from, dc.valid_until, 
         dc.is_active, dc.created_at, dc.updated_at
ORDER BY dc.created_at DESC;

-- Grant access to the view for admins
GRANT SELECT ON discount_analytics TO authenticated;

-- Add comment for documentation
COMMENT ON TABLE discount_codes IS 'Store discount codes and promotional campaigns';
COMMENT ON TABLE discount_code_usage IS 'Track usage of discount codes by customers';
COMMENT ON FUNCTION validate_and_apply_discount IS 'Validates discount code and calculates discount amount';
COMMENT ON FUNCTION record_discount_usage IS 'Records when a discount code is used in an order';
COMMENT ON VIEW discount_analytics IS 'Analytics view for discount code performance in admin dashboard';

-- 13. Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_discount_codes_updated_at 
    BEFORE UPDATE ON discount_codes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Migration completed: Complete discount code system with validation functions and analytics