-- SECURITY PERFORMANCE: Add indexes for RLS policy performance
-- This migration adds indexes to improve performance of RLS policy checks

-- Index on profiles.is_admin for faster admin checks
-- This is queried in almost every admin policy
CREATE INDEX IF NOT EXISTS idx_profiles_is_admin 
ON profiles (is_admin) 
WHERE is_admin = true;

-- Composite index for admin user lookups (most common RLS pattern)
CREATE INDEX IF NOT EXISTS idx_profiles_id_is_admin 
ON profiles (id, is_admin);

-- Index on user_id columns for user-specific policies
CREATE INDEX IF NOT EXISTS idx_cart_items_user_id 
ON cart_items (user_id);

CREATE INDEX IF NOT EXISTS idx_wishlists_user_id 
ON wishlists (user_id);

CREATE INDEX IF NOT EXISTS idx_wardrobe_items_user_id 
ON wardrobe_items (user_id);

CREATE INDEX IF NOT EXISTS idx_orders_user_id 
ON orders (user_id);

CREATE INDEX IF NOT EXISTS idx_shipping_addresses_user_id 
ON shipping_addresses (user_id);

CREATE INDEX IF NOT EXISTS idx_product_views_user_id 
ON product_views (user_id);

-- Index on order_items.order_id for order-related policies
CREATE INDEX IF NOT EXISTS idx_order_items_order_id 
ON order_items (order_id);

-- Composite indexes for foreign key relationships used in RLS
CREATE INDEX IF NOT EXISTS idx_cart_items_user_product 
ON cart_items (user_id, product_id);

CREATE INDEX IF NOT EXISTS idx_wishlists_user_product 
ON wishlists (user_id, product_id);

-- Index for audit log queries (admins viewing logs)
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp 
ON audit_log (timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_audit_log_table_operation 
ON audit_log (table_name, operation);

CREATE INDEX IF NOT EXISTS idx_audit_log_user_id 
ON audit_log (user_id);

-- Index for order tracking lookups
CREATE INDEX IF NOT EXISTS idx_orders_order_number 
ON orders (order_number) 
WHERE order_number IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_orders_status 
ON orders (status);

CREATE INDEX IF NOT EXISTS idx_orders_payment_status 
ON orders (payment_status);

-- Index for product filtering and searching (public access)
CREATE INDEX IF NOT EXISTS idx_products_status 
ON products (status) 
WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_products_category 
ON products (category_id) 
WHERE category_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_products_collection 
ON products (collection_id) 
WHERE collection_id IS NOT NULL;

-- Index for product media ordering
CREATE INDEX IF NOT EXISTS idx_product_media_product_position 
ON product_media (product_id, position);

CREATE INDEX IF NOT EXISTS idx_product_media_is_main 
ON product_media (product_id, is_main) 
WHERE is_main = true;

-- Index for contact message management
CREATE INDEX IF NOT EXISTS idx_contact_messages_status 
ON contact_messages (status);

CREATE INDEX IF NOT EXISTS idx_contact_messages_created_at 
ON contact_messages (created_at DESC);

-- Performance optimization for auth.uid() lookups
-- Create a function to get current user profile efficiently
CREATE OR REPLACE FUNCTION get_current_user_profile()
RETURNS profiles
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT * FROM profiles WHERE id = auth.uid();
$$;

-- Grant usage to authenticated users
GRANT EXECUTE ON FUNCTION get_current_user_profile() TO authenticated;

-- Analyze tables to update query planner statistics
ANALYZE profiles;
ANALYZE orders;
ANALYZE order_items;
ANALYZE cart_items;
ANALYZE wishlists;
ANALYZE wardrobe_items;
ANALYZE shipping_addresses;
ANALYZE product_views;
ANALYZE audit_log;
ANALYZE products;
ANALYZE product_media;
ANALYZE contact_messages;

-- Add constraints for data integrity (helps with query optimization)
-- Foreign key constraints help the query planner
ALTER TABLE cart_items 
ADD CONSTRAINT fk_cart_items_user_id 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

ALTER TABLE wishlists 
ADD CONSTRAINT fk_wishlists_user_id 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

ALTER TABLE wardrobe_items 
ADD CONSTRAINT fk_wardrobe_items_user_id 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

ALTER TABLE orders 
ADD CONSTRAINT fk_orders_user_id 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

ALTER TABLE shipping_addresses 
ADD CONSTRAINT fk_shipping_addresses_user_id 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

ALTER TABLE product_views 
ADD CONSTRAINT fk_product_views_user_id 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- Add check constraints for enum-like fields to help query planner
ALTER TABLE orders 
ADD CONSTRAINT chk_orders_status 
CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'));

ALTER TABLE orders 
ADD CONSTRAINT chk_orders_payment_status 
CHECK (payment_status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'));

ALTER TABLE products 
ADD CONSTRAINT chk_products_status 
CHECK (status IN ('active', 'inactive', 'draft', 'archived'));

ALTER TABLE contact_messages 
ADD CONSTRAINT chk_contact_messages_status 
CHECK (status IN ('new', 'in_progress', 'resolved', 'closed'));

-- Create partial indexes for common filtered queries
CREATE INDEX IF NOT EXISTS idx_orders_active 
ON orders (user_id, created_at DESC) 
WHERE status NOT IN ('cancelled', 'refunded');

CREATE INDEX IF NOT EXISTS idx_products_active_price 
ON products (price) 
WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_shipping_addresses_default 
ON shipping_addresses (user_id) 
WHERE is_default = true;

-- Add comments for maintenance
COMMENT ON INDEX idx_profiles_is_admin IS 'SECURITY: Performance index for admin role checks - 2025-06-17';
COMMENT ON INDEX idx_profiles_id_is_admin IS 'SECURITY: Composite index for admin user lookups - 2025-06-17';
COMMENT ON FUNCTION get_current_user_profile IS 'SECURITY: Optimized function for current user profile lookup - 2025-06-17';