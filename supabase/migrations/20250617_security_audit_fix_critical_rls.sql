-- CRITICAL SECURITY FIX: Add missing RLS policies
-- This migration addresses critical security vulnerabilities identified in the security audit
-- Apply immediately to production

-- Enable RLS on all tables that are missing it
ALTER TABLE admin_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE store_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipping_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipping_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipping_regions ENABLE ROW LEVEL SECURITY;

-- Force RLS on critical tables (cannot be bypassed)
ALTER TABLE admin_tokens FORCE ROW LEVEL SECURITY;
ALTER TABLE profiles FORCE ROW LEVEL SECURITY;
ALTER TABLE store_settings FORCE ROW LEVEL SECURITY;
ALTER TABLE contact_messages FORCE ROW LEVEL SECURITY;
ALTER TABLE shipping_addresses FORCE ROW LEVEL SECURITY;

-- CRITICAL: admin_tokens table (highest security)
-- Only authenticated users with admin privileges can access admin tokens
CREATE POLICY "Only admins can view admin tokens"
ON admin_tokens FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Only admins can insert admin tokens"
ON admin_tokens FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Only admins can update admin tokens"
ON admin_tokens FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Only admins can delete admin tokens"
ON admin_tokens FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- HIGH PRIORITY: profiles table
-- Users can view and edit their own profile, admins can view all profiles
CREATE POLICY "Users can view their own profile"
ON profiles FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
ON profiles FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

CREATE POLICY "Admins can view all profiles"
ON profiles FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles admin_profile
    WHERE admin_profile.id = auth.uid()
    AND admin_profile.is_admin = true
  )
);

CREATE POLICY "Admins can update all profiles"
ON profiles FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles admin_profile
    WHERE admin_profile.id = auth.uid()
    AND admin_profile.is_admin = true
  )
);

-- HIGH PRIORITY: store_settings table
-- Public can read store settings, only admins can modify
CREATE POLICY "Anyone can view store settings"
ON store_settings FOR SELECT
TO public
USING (true);

CREATE POLICY "Only admins can update store settings"
ON store_settings FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Only admins can insert store settings"
ON store_settings FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- MEDIUM PRIORITY: contact_messages table
-- Only admins can view and manage contact messages
CREATE POLICY "Only admins can view contact messages"
ON contact_messages FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Only admins can update contact messages"
ON contact_messages FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Anyone can insert contact messages"
ON contact_messages FOR INSERT
TO public
WITH CHECK (true);

-- HIGH PRIORITY: shipping_addresses table
-- Users can manage their own addresses, admins can view all for order management
CREATE POLICY "Users can view their own shipping addresses"
ON shipping_addresses FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own shipping addresses"
ON shipping_addresses FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own shipping addresses"
ON shipping_addresses FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own shipping addresses"
ON shipping_addresses FOR DELETE
USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all shipping addresses"
ON shipping_addresses FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- LOW PRIORITY: products table (public catalog)
-- Everyone can view products, only admins can modify
CREATE POLICY "Anyone can view products"
ON products FOR SELECT
TO public
USING (true);

CREATE POLICY "Only admins can insert products"
ON products FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Only admins can update products"
ON products FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Only admins can delete products"
ON products FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- LOW PRIORITY: categories table
CREATE POLICY "Anyone can view categories"
ON categories FOR SELECT
TO public
USING (true);

CREATE POLICY "Only admins can manage categories"
ON categories FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- LOW PRIORITY: collections table
CREATE POLICY "Anyone can view collections"
ON collections FOR SELECT
TO public
USING (true);

CREATE POLICY "Only admins can manage collections"
ON collections FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- LOW PRIORITY: product_media table
CREATE POLICY "Anyone can view product media"
ON product_media FOR SELECT
TO public
USING (true);

CREATE POLICY "Only admins can manage product media"
ON product_media FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- MEDIUM PRIORITY: product_views table (analytics)
-- Users can view their own product views, admins can view all
CREATE POLICY "Users can view their own product views"
ON product_views FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own product views"
ON product_views FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all product views"
ON product_views FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- LOW PRIORITY: shipping tables
CREATE POLICY "Anyone can view shipping rates"
ON shipping_rates FOR SELECT
TO public
USING (true);

CREATE POLICY "Only admins can manage shipping rates"
ON shipping_rates FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Anyone can view shipping regions"
ON shipping_regions FOR SELECT
TO public
USING (true);

CREATE POLICY "Only admins can manage shipping regions"
ON shipping_regions FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- Add comment to track this security fix
COMMENT ON TABLE admin_tokens IS 'SECURITY: Critical RLS policies applied 2025-06-17';
COMMENT ON TABLE profiles IS 'SECURITY: Critical RLS policies applied 2025-06-17';
COMMENT ON TABLE store_settings IS 'SECURITY: Critical RLS policies applied 2025-06-17';
COMMENT ON TABLE contact_messages IS 'SECURITY: Critical RLS policies applied 2025-06-17';
COMMENT ON TABLE shipping_addresses IS 'SECURITY: Critical RLS policies applied 2025-06-17';