-- SECURITY FIX: Tighten overly permissive policies and standardize admin checking
-- This migration fixes overly broad permissions and inconsistent admin role checking

-- First, drop existing policies that are too permissive or inconsistent
DROP POLICY IF EXISTS "Users can update their orders" ON orders;
DROP POLICY IF EXISTS "Users can insert order items" ON order_items;
DROP POLICY IF EXISTS "Ad<PERSON> can view all order items" ON order_items;
DROP POLICY IF EXISTS "Ad<PERSON> can manage order items" ON order_items;
DROP POLICY IF EXISTS "Ad<PERSON> can view all wardrobe items" ON wardrobe_items;
DROP POLICY IF EXISTS "Admins can view all orders" ON orders;
DROP POLICY IF EXISTS "Admins can update all orders" ON orders;

-- ORDERS TABLE: Restrict user permissions to prevent tampering
-- Users should NOT be able to update orders after creation (prevents price/status manipulation)
-- Only admins should be able to update order status, tracking info, etc.

CREATE POLICY "Only admins can update orders"
ON orders FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- Users can insert orders (for checkout process)
CREATE POLICY "Users can create orders"
ON orders FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Standardize admin policies for orders (use is_admin consistently)
CREATE POLICY "Admins can view all orders"
ON orders FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Admins can delete orders"
ON orders FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- ORDER ITEMS TABLE: Tighten permissions to prevent price manipulation
-- Users should NOT be able to directly insert/update order items
-- This should only happen through controlled application logic

-- Remove the overly permissive user insert policy
-- Order items should only be created through secure checkout process

CREATE POLICY "Only admins can insert order items"
ON order_items FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Only admins can update order items"
ON order_items FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Only admins can delete order items"
ON order_items FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- Standardize admin policy for order items (use is_admin consistently)
CREATE POLICY "Admins can view all order items"
ON order_items FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- WARDROBE ITEMS: Standardize admin checking
-- Fix inconsistent admin role checking (was using profiles.role = 'admin')
CREATE POLICY "Admins can view all wardrobe items"
ON wardrobe_items FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

CREATE POLICY "Admins can manage all wardrobe items"
ON wardrobe_items FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- Add a secure function for order item creation during checkout
-- This ensures order items can only be created through controlled process
CREATE OR REPLACE FUNCTION create_order_with_items(
  p_total_amount DECIMAL,
  p_shipping_address_id UUID,
  p_payment_intent TEXT,
  p_payment_provider TEXT,
  p_items JSONB
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  v_order_id UUID;
  v_item JSONB;
  v_product_price DECIMAL;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- Check if user is authenticated
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;
  
  -- Create the order
  INSERT INTO orders (
    user_id,
    total_amount,
    shipping_address_id,
    payment_intent,
    payment_provider,
    status,
    payment_status
  )
  VALUES (
    v_user_id,
    p_total_amount,
    p_shipping_address_id,
    p_payment_intent,
    p_payment_provider,
    'pending',
    'pending'
  )
  RETURNING id INTO v_order_id;
  
  -- Create order items with price validation
  FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
  LOOP
    -- Get current product price to prevent manipulation
    SELECT price INTO v_product_price
    FROM products
    WHERE id = (v_item->>'product_id')::UUID;
    
    -- Validate that the price matches current product price
    IF v_product_price != (v_item->>'price')::DECIMAL THEN
      RAISE EXCEPTION 'Price mismatch for product %', v_item->>'product_id';
    END IF;
    
    -- Insert order item with validated price
    INSERT INTO order_items (
      order_id,
      product_id,
      quantity,
      price
    )
    VALUES (
      v_order_id,
      (v_item->>'product_id')::UUID,
      (v_item->>'quantity')::INTEGER,
      v_product_price
    );
  END LOOP;
  
  RETURN v_order_id;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error and re-raise
    RAISE NOTICE 'Error creating order: %', SQLERRM;
    RAISE;
END;
$$;

-- Grant execute permission to authenticated users for secure order creation
GRANT EXECUTE ON FUNCTION create_order_with_items(DECIMAL, UUID, TEXT, TEXT, JSONB) TO authenticated;

-- Add constraints to prevent common security issues
-- Ensure order amounts are positive
ALTER TABLE orders 
ADD CONSTRAINT orders_total_amount_positive 
CHECK (total_amount > 0);

-- Ensure order item quantities are positive
ALTER TABLE order_items 
ADD CONSTRAINT order_items_quantity_positive 
CHECK (quantity > 0);

-- Ensure order item prices are positive
ALTER TABLE order_items 
ADD CONSTRAINT order_items_price_positive 
CHECK (price > 0);

-- Add audit triggers for sensitive operations
CREATE OR REPLACE FUNCTION audit_order_changes()
RETURNS TRIGGER AS $$
BEGIN
  -- Log order status changes for audit trail
  IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
    INSERT INTO audit_log (
      table_name,
      operation,
      old_values,
      new_values,
      user_id,
      timestamp
    )
    VALUES (
      'orders',
      'status_change',
      jsonb_build_object('old_status', OLD.status),
      jsonb_build_object('new_status', NEW.status),
      auth.uid(),
      NOW()
    );
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create audit log table if it doesn't exist
CREATE TABLE IF NOT EXISTS audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name TEXT NOT NULL,
  operation TEXT NOT NULL,
  old_values JSONB,
  new_values JSONB,
  user_id UUID,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on audit log
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY "Only admins can view audit logs"
ON audit_log FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = true
  )
);

-- Create trigger for order audit
DROP TRIGGER IF EXISTS orders_audit_trigger ON orders;
CREATE TRIGGER orders_audit_trigger
AFTER UPDATE ON orders
FOR EACH ROW
EXECUTE FUNCTION audit_order_changes();

-- Add security comments
COMMENT ON FUNCTION create_order_with_items IS 'SECURITY: Secure order creation with price validation - 2025-06-17';
COMMENT ON TABLE audit_log IS 'SECURITY: Audit trail for sensitive operations - 2025-06-17';
COMMENT ON CONSTRAINT orders_total_amount_positive ON orders IS 'SECURITY: Prevent negative order amounts';
COMMENT ON CONSTRAINT order_items_quantity_positive ON order_items IS 'SECURITY: Prevent negative quantities';
COMMENT ON CONSTRAINT order_items_price_positive ON order_items IS 'SECURITY: Prevent negative prices';