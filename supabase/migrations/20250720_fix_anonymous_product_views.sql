-- Fix product_views RLS policy to allow anonymous tracking
-- This allows both authenticated and anonymous users to track product views

-- Drop the existing restrictive policy
DROP POLICY IF EXISTS "Users can insert their own product views" ON product_views;

-- Create new policies that allow anonymous views
CREATE POLICY "Anyone can insert product views for tracking"
ON product_views FOR INSERT
WITH CHECK (
  CASE 
    -- If user_id is provided, must match auth.uid()
    WHEN user_id IS NOT NULL THEN auth.uid() = user_id
    -- If user_id is NULL (anonymous), allow the insert
    WHEN user_id IS NULL THEN true
    ELSE false
  END
);

-- Add policy for authenticated users to view their own product views
CREATE POLICY "Users can view their own product views (authenticated)"
ON product_views FOR SELECT
USING (
  auth.uid() IS NOT NULL 
  AND auth.uid() = user_id
);

-- Allow anonymous viewing for analytics (but not personal data)
CREATE POLICY "Anonymous users can view product view counts"
ON product_views FOR SELECT
USING (user_id IS NULL);

-- Make user_id nullable in the table if it isn't already
ALTER TABLE product_views ALTER COLUMN user_id DROP NOT NULL;