-- Add 'awaiting_payment' status to orders table
-- This fixes the critical payment bug where orders showed as 'pending' even when payments failed

-- First, let's check if we have a status constraint and remove it temporarily
DO $$
BEGIN
    -- Try to drop the constraint if it exists
    BEGIN
        ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_status_check;
    EXCEPTION WHEN OTHERS THEN
        NULL; -- Ignore errors if constraint doesn't exist
    END;
END $$;

-- Update any existing 'pending' orders that haven't been paid to 'awaiting_payment'
-- This is safe because truly pending orders should have payment_status = 'completed'
UPDATE orders 
SET status = 'awaiting_payment'
WHERE status = 'pending' 
  AND (payment_status IS NULL OR payment_status != 'completed');

-- Add a comprehensive status constraint that includes all possible statuses
ALTER TABLE orders ADD CONSTRAINT orders_status_check 
CHECK (status IN (
    'awaiting_payment',  -- New status: Order created but payment not confirmed
    'pending',           -- Payment confirmed, order being processed
    'paid',              -- Payment completed successfully
    'processing',        -- Order being prepared
    'shipped',           -- Order has been shipped
    'delivered',         -- Order delivered to customer
    'cancelled',         -- Order cancelled by customer or admin
    'failed',            -- Payment failed
    'expired',           -- Payment session expired
    'abandoned',         -- Order abandoned (cleanup status)
    'refunded',          -- Order refunded
    'disputed'           -- Payment disputed
));

-- Add similar constraint for payment_status
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_payment_status_check;
ALTER TABLE orders ADD CONSTRAINT orders_payment_status_check 
CHECK (payment_status IN (
    'awaiting_payment',  -- Payment not yet confirmed
    'pending',           -- Payment processing
    'completed',         -- Payment successful
    'failed',            -- Payment failed
    'cancelled',         -- Payment cancelled
    'expired',           -- Payment session expired
    'abandoned',         -- Payment abandoned
    'refunded',          -- Payment refunded
    'disputed'           -- Payment disputed
));

-- Add index on status for better performance
CREATE INDEX IF NOT EXISTS orders_status_idx ON orders(status);
CREATE INDEX IF NOT EXISTS orders_payment_status_idx ON orders(payment_status);

-- Add comments to document the new status flow
COMMENT ON COLUMN orders.status IS 'Order status: awaiting_payment -> paid/failed/expired -> processing -> shipped -> delivered';
COMMENT ON COLUMN orders.payment_status IS 'Payment status tracking to prevent showing unpaid orders as pending';