/**
 * Test the actual admin API endpoint that was failing
 */

const { createClient } = require('@supabase/supabase-js');
const fetch = require('node-fetch');

// Load environment variables
require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const DEV_SERVER_URL = 'http://localhost:3001';

async function testAdminApiEndpoint() {
  try {
    console.log('🔍 Testing Admin API Endpoint Authentication...\n');

    // For this test, we'll simulate the API call directly without full cookie handling

    // Create supabase client for authentication
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    console.log('1. Attempting to authenticate with admin credentials...');
    
    // Try to sign in with admin credentials
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>', // The admin user from the logs
      password: 'Myhappy1' // Using the password provided earlier
    });

    if (authError) {
      console.error('❌ Authentication failed:', authError.message);
      console.log('📝 Note: You may need to provide the correct admin password');
      return;
    }

    console.log('✅ Authentication successful');
    console.log('   User:', authData.user.email);
    console.log('   Session expires:', new Date(authData.session.expires_at * 1000).toISOString());

    // Get session cookies
    const sessionCookies = [];
    if (authData.session.access_token) {
      sessionCookies.push(`sb-${SUPABASE_URL.split('//')[1].split('.')[0]}-auth-token=${authData.session.access_token}`);
    }
    if (authData.session.refresh_token) {
      sessionCookies.push(`sb-${SUPABASE_URL.split('//')[1].split('.')[0]}-auth-token.1=${authData.session.refresh_token}`);
    }

    console.log('\n2. Testing admin orders API with session...');

    // First, get a list of orders to find one to update
    const ordersResponse = await fetch(`${DEV_SERVER_URL}/api/admin/orders`, {
      method: 'GET',
      headers: {
        'Cookie': sessionCookies.join('; '),
        'Content-Type': 'application/json'
      }
    });

    console.log('GET /api/admin/orders status:', ordersResponse.status);

    if (ordersResponse.status === 200) {
      const ordersData = await ordersResponse.json();
      console.log('✅ Successfully fetched orders list');
      console.log('   Found', ordersData.orders?.length || 0, 'orders');

      if (ordersData.orders && ordersData.orders.length > 0) {
        const testOrder = ordersData.orders[0];
        const orderId = testOrder.id;
        console.log('   Testing with order ID:', orderId);
        console.log('   Current status:', testOrder.status);

        console.log('\n3. Testing order status update...');

        // Determine new status
        const newStatus = testOrder.status === 'pending' ? 'processing' : 'pending';

        // Test the PATCH endpoint that was failing
        const updateResponse = await fetch(`${DEV_SERVER_URL}/api/admin/orders/${orderId}`, {
          method: 'PATCH',
          headers: {
            'Cookie': sessionCookies.join('; '),
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ status: newStatus })
        });

        console.log('PATCH /api/admin/orders/[id] status:', updateResponse.status);

        if (updateResponse.status === 200) {
          const updateData = await updateResponse.json();
          console.log('✅ Successfully updated order status');
          console.log('   New status:', updateData.order?.status || newStatus);
        } else {
          const errorData = await updateResponse.text();
          console.error('❌ Failed to update order status');
          console.error('   Response:', errorData);
        }

        console.log('\n4. Testing order detail fetch...');

        // Test the GET endpoint for individual order
        const detailResponse = await fetch(`${DEV_SERVER_URL}/api/admin/orders/${orderId}`, {
          method: 'GET',
          headers: {
            'Cookie': sessionCookies.join('; '),
            'Content-Type': 'application/json'
          }
        });

        console.log('GET /api/admin/orders/[id] status:', detailResponse.status);

        if (detailResponse.status === 200) {
          const detailData = await detailResponse.json();
          console.log('✅ Successfully fetched order details');
          console.log('   Order status:', detailData.order?.status);
        } else {
          const errorData = await detailResponse.text();
          console.error('❌ Failed to fetch order details');
          console.error('   Response:', errorData);
        }

      } else {
        console.log('⚠️  No orders found to test with');
      }

    } else {
      const errorData = await ordersResponse.text();
      console.error('❌ Failed to fetch orders list');
      console.error('   Response:', errorData);
    }

    // Clean up
    await supabase.auth.signOut();
    console.log('\n✅ Test completed and session cleaned up');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testAdminApiEndpoint().catch(console.error);
}

module.exports = { testAdminApiEndpoint };