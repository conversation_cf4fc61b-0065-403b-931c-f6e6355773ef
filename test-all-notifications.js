// Test all notification channels with your Twilio setup
// Run with: node test-all-notifications.js

const testEmailNotification = async () => {
  try {
    console.log('📧 Testing Email Notification...');
    
    const response = await fetch('http://localhost:3000/api/notifications/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'system_alert',
        title: 'Test Email from Treasures of Maimi',
        message: 'This is a test email notification. If you received this, email notifications are working correctly!',
        priority: 'low',
        data: {
          test: true,
          timestamp: new Date().toISOString()
        }
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Email notification sent successfully!');
      console.log(`📝 Response: ${result.message}`);
    } else {
      console.log('❌ Email notification failed:');
      console.log(`🚨 Status: ${response.status}`);
      console.log(`📝 Error: ${result.error}`);
    }
  } catch (error) {
    console.error('❌ Email test error:', error.message);
  }
};

const testSMSNotification = async () => {
  try {
    console.log('📱 Testing SMS Notification...');
    
    const response = await fetch('http://localhost:3000/api/notifications/send-sms', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'system_alert',
        title: 'Test SMS from Treasures of Maimi',
        message: 'This is a test SMS notification. If you received this, SMS notifications are working correctly!',
        priority: 'low',
        data: {
          test: true,
          timestamp: new Date().toISOString()
        }
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ SMS notification sent successfully!');
      console.log(`📝 Response: ${result.message}`);
    } else {
      console.log('❌ SMS notification failed:');
      console.log(`🚨 Status: ${response.status}`);
      console.log(`📝 Error: ${result.error}`);
    }
  } catch (error) {
    console.error('❌ SMS test error:', error.message);
  }
};

const testWhatsAppNotification = async () => {
  try {
    console.log('💬 Testing WhatsApp Notification...');
    
    const response = await fetch('http://localhost:3000/api/notifications/send-whatsapp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'test',
        phoneNumber: '+1234567890' // Replace with your actual phone number
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ WhatsApp notification sent successfully!');
      console.log(`📝 Response: ${result.message}`);
    } else {
      console.log('❌ WhatsApp notification failed:');
      console.log(`🚨 Status: ${response.status}`);
      console.log(`📝 Error: ${result.error}`);
    }
  } catch (error) {
    console.error('❌ WhatsApp test error:', error.message);
  }
};

const testUnifiedNotification = async () => {
  try {
    console.log('🚀 Testing Unified Notification (All Channels)...');
    
    const response = await fetch('http://localhost:3000/api/notifications/send-all', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'new_order',
        data: {
          order_id: 'test-order-123',
          order_number: 'ORD-2025-001',
          total_amount: 299.99,
          customer_email: '<EMAIL>'
        }
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Unified notification sent successfully!');
      console.log(`📝 Response: ${result.message}`);
    } else {
      console.log('❌ Unified notification failed:');
      console.log(`🚨 Status: ${response.status}`);
      console.log(`📝 Error: ${result.error}`);
    }
  } catch (error) {
    console.error('❌ Unified test error:', error.message);
  }
};

const runAllTests = async () => {
  console.log('🎯 Testing Treasures of Maimi Notification System');
  console.log('=' * 60);
  console.log('📋 Configuration:');
  console.log('   📧 Email: Resend API (configured)');
  console.log('   📱 SMS: Twilio +12182191983 (configured)');
  console.log('   💬 WhatsApp: Twilio Sandbox (configured)');
  console.log('   🔐 Auth: Testing without authentication (expected to fail)');
  console.log('');
  
  console.log('⚠️  NOTE: These tests will fail with 401 Unauthorized because');
  console.log('   they don\'t include authentication. Use the admin panel');
  console.log('   at http://localhost:3000/admin/notification-settings');
  console.log('   for authenticated testing.');
  console.log('');
  console.log('🚀 Starting tests...\n');
  
  await testEmailNotification();
  console.log('\n' + '-'.repeat(50) + '\n');
  
  await testSMSNotification();
  console.log('\n' + '-'.repeat(50) + '\n');
  
  await testWhatsAppNotification();
  console.log('\n' + '-'.repeat(50) + '\n');
  
  await testUnifiedNotification();
  console.log('\n' + '='.repeat(60) + '\n');
  
  console.log('🏁 All tests completed!');
  console.log('');
  console.log('📝 Next Steps:');
  console.log('1. Go to http://localhost:3000/admin/notification-settings');
  console.log('2. Add your phone number for SMS/WhatsApp testing');
  console.log('3. Use the "Test" buttons for authenticated testing');
  console.log('4. For WhatsApp: Join Twilio Sandbox first');
  console.log('   - Send "join <keyword>" to ****** 523 8886');
  console.log('');
  console.log('🎉 Your notification system is ready!');
};

// Run all tests
runAllTests();
