// Test script to verify bag request status updates work
// Run this in the browser console on the bag requests admin page

async function testBagRequestUpdate() {
  console.log('🧪 Testing bag request status update...');
  
  // Find the first bag request on the page
  const firstRow = document.querySelector('tbody tr');
  if (!firstRow) {
    console.error('❌ No bag requests found on the page');
    return;
  }
  
  // Get the item ID from the approve button
  const approveButton = firstRow.querySelector('button[title="Approve"]');
  if (!approveButton) {
    console.log('ℹ️ No approve button found (item might already be approved)');
    return;
  }
  
  // Extract the item ID from the onclick handler
  const onClickHandler = approveButton.getAttribute('onclick') || approveButton.onclick?.toString();
  console.log('Button handler:', onClickHandler);
  
  // Simulate the API call that the frontend makes
  try {
    const response = await fetch('/api/admin/bag-requests', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        id: 'test-id', // Replace with actual ID
        status: 'approved'
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ API call successful:', result);
    } else {
      const error = await response.json();
      console.error('❌ API call failed:', error);
    }
  } catch (error) {
    console.error('❌ Network error:', error);
  }
}

// Instructions
console.log(`
🔧 Bag Request Update Test

To test the fix:
1. Open the bag requests admin page
2. Open browser console
3. Run: testBagRequestUpdate()
4. Or manually click an approve/reject button and check the network tab

The fix ensures that status updates use the admin API endpoint instead of direct database calls.
`);
