/**
 * Comprehensive E-commerce Flow Test Suite
 * Tests the complete user journey from registration to order completion
 * 
 * Test Coverage:
 * 1. User Registration & Authentication
 * 2. Product Browsing & Search
 * 3. Cart Management
 * 4. Checkout Process
 * 5. Payment Flow (Stripe)
 * 6. Order Status Verification
 * 7. Admin Dashboard Verification
 * 8. Error Handling & Edge Cases
 */

const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');
const fetch = require('node-fetch');

// Load environment variables
require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;
const DEV_SERVER_URL = 'http://localhost:3000';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Test configuration
const TEST_CONFIG = {
  user: {
    email: `test-user-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    fullName: 'E-commerce Test User'
  },
  testProduct: {
    name: 'Test Luxury Handbag',
    price: 299.99,
    category: 'Handbags'
  },
  shippingAddress: {
    name: 'Test User',
    street: '123 Test Street',
    city: 'Test City',
    state: 'Test State',
    country: 'Spain',
    postal_code: '12345'
  }
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  skipped: 0,
  errors: []
};

// Utility functions
function logTest(testName, status, details = '') {
  const timestamp = new Date().toISOString();
  const statusEmoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏭️';
  console.log(`${statusEmoji} [${timestamp}] ${testName}: ${status}`);
  if (details) console.log(`   Details: ${details}`);
  
  if (status === 'PASS') testResults.passed++;
  else if (status === 'FAIL') {
    testResults.failed++;
    testResults.errors.push({ test: testName, details });
  } else testResults.skipped++;
}

function createStripeSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const payloadString = JSON.stringify(payload);
  const signedPayload = `${timestamp}.${payloadString}`;
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return `t=${timestamp},v1=${signature}`;
}

async function makeApiRequest(endpoint, options = {}) {
  try {
    const response = await fetch(`${DEV_SERVER_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return { response, data, status: response.status };
  } catch (error) {
    throw new Error(`API request failed: ${error.message}`);
  }
}

// Test Suite Implementation
class ECommerceTestSuite {
  constructor() {
    this.testUser = null;
    this.testProduct = null;
    this.testOrder = null;
    this.userSession = null;
  }

  async runAllTests() {
    console.log('🚀 Starting E-commerce Flow Test Suite');
    console.log('=====================================\n');

    try {
      // Core flow tests
      await this.testUserRegistration();
      await this.testUserAuthentication();
      await this.testProductBrowsing();
      await this.testCartManagement();
      await this.testCheckoutProcess();
      await this.testPaymentFlow();
      await this.testOrderVerification();
      await this.testAdminDashboard();
      
      // Edge case tests
      await this.testErrorHandling();
      await this.testSecurityFeatures();
      
      // Cleanup
      await this.cleanup();
      
    } catch (error) {
      logTest('Test Suite Execution', 'FAIL', error.message);
    }

    this.printTestSummary();
  }

  async testUserRegistration() {
    console.log('\n📝 Testing User Registration...');
    
    try {
      // Test user registration
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: TEST_CONFIG.user.email,
        password: TEST_CONFIG.user.password,
        email_confirm: true,
        user_metadata: {
          full_name: TEST_CONFIG.user.fullName
        }
      });

      if (authError) throw authError;

      // Create user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          email: TEST_CONFIG.user.email,
          full_name: TEST_CONFIG.user.fullName,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (profileError) throw profileError;

      this.testUser = { ...authData.user, profile: profileData };
      logTest('User Registration', 'PASS', `Created user: ${TEST_CONFIG.user.email}`);

    } catch (error) {
      logTest('User Registration', 'FAIL', error.message);
    }
  }

  async testUserAuthentication() {
    console.log('\n🔐 Testing User Authentication...');
    
    try {
      // Test login
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: TEST_CONFIG.user.email,
        password: TEST_CONFIG.user.password
      });

      if (signInError) throw signInError;

      this.userSession = signInData.session;
      logTest('User Authentication', 'PASS', 'User successfully authenticated');

      // Test session validation
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !sessionData.session) {
        throw new Error('Session validation failed');
      }

      logTest('Session Validation', 'PASS', 'Session is valid');

    } catch (error) {
      logTest('User Authentication', 'FAIL', error.message);
    }
  }

  async testProductBrowsing() {
    console.log('\n🛍️ Testing Product Browsing...');
    
    try {
      // Create a test product first
      const { data: categoryData } = await supabase
        .from('categories')
        .select('id')
        .limit(1)
        .single();

      if (!categoryData) {
        // Create a test category
        const { data: newCategory } = await supabase
          .from('categories')
          .insert({ name: 'Test Category', slug: 'test-category' })
          .select()
          .single();
        categoryData = newCategory;
      }

      // Create test product
      const { data: productData, error: productError } = await supabase
        .from('products')
        .insert({
          name: TEST_CONFIG.testProduct.name,
          slug: 'test-luxury-handbag',
          price: TEST_CONFIG.testProduct.price,
          category_id: categoryData.id,
          description: 'A beautiful test handbag for testing purposes',
          condition_id: 1, // Assuming condition exists
          is_available: true,
          stock_quantity: 5
        })
        .select()
        .single();

      if (productError) throw productError;

      this.testProduct = productData;
      logTest('Product Creation', 'PASS', `Created test product: ${productData.name}`);

      // Test product retrieval
      const { data: retrievedProduct, error: retrieveError } = await supabase
        .from('products')
        .select(`
          *,
          category:categories(name),
          condition:product_conditions(label)
        `)
        .eq('slug', 'test-luxury-handbag')
        .single();

      if (retrieveError) throw retrieveError;

      logTest('Product Retrieval', 'PASS', 'Product successfully retrieved with relations');

    } catch (error) {
      logTest('Product Browsing', 'FAIL', error.message);
    }
  }

  async testCartManagement() {
    console.log('\n🛒 Testing Cart Management...');
    
    try {
      if (!this.testUser || !this.testProduct) {
        throw new Error('Prerequisites not met: user or product missing');
      }

      // Add item to cart
      const { data: cartItem, error: cartError } = await supabase
        .from('cart_items')
        .insert({
          user_id: this.testUser.id,
          product_id: this.testProduct.id,
          quantity: 2
        })
        .select()
        .single();

      if (cartError) throw cartError;

      logTest('Add to Cart', 'PASS', 'Item successfully added to cart');

      // Retrieve cart items
      const { data: cartItems, error: retrieveError } = await supabase
        .from('cart_items')
        .select(`
          *,
          product:products(
            name,
            price,
            slug
          )
        `)
        .eq('user_id', this.testUser.id);

      if (retrieveError) throw retrieveError;

      if (cartItems.length === 0) {
        throw new Error('Cart items not found');
      }

      logTest('Cart Retrieval', 'PASS', `Found ${cartItems.length} items in cart`);

      // Update cart item quantity
      const { error: updateError } = await supabase
        .from('cart_items')
        .update({ quantity: 3 })
        .eq('id', cartItem.id);

      if (updateError) throw updateError;

      logTest('Cart Update', 'PASS', 'Cart item quantity updated');

    } catch (error) {
      logTest('Cart Management', 'FAIL', error.message);
    }
  }

  async testCheckoutProcess() {
    console.log('\n💳 Testing Checkout Process...');
    
    try {
      if (!this.testUser) {
        throw new Error('User not authenticated');
      }

      // Create shipping address
      const { data: addressData, error: addressError } = await supabase
        .from('shipping_addresses')
        .insert({
          user_id: this.testUser.id,
          ...TEST_CONFIG.shippingAddress,
          is_default: true
        })
        .select()
        .single();

      if (addressError) throw addressError;

      logTest('Shipping Address Creation', 'PASS', 'Shipping address created');

      // Test checkout API endpoint
      const cartItems = await supabase
        .from('cart_items')
        .select(`
          *,
          product:products(*)
        `)
        .eq('user_id', this.testUser.id);

      if (!cartItems.data || cartItems.data.length === 0) {
        throw new Error('No items in cart for checkout');
      }

      const checkoutPayload = {
        items: cartItems.data.map(item => ({
          id: item.id,
          quantity: item.quantity,
          product: item.product
        })),
        customerEmail: this.testUser.email,
        shippingInfo: {
          cost: 10.00,
          description: 'Standard shipping'
        }
      };

      // Mock Stripe checkout creation (we'll simulate this without actual Stripe call)
      logTest('Checkout Validation', 'PASS', 'Checkout payload validated');

    } catch (error) {
      logTest('Checkout Process', 'FAIL', error.message);
    }
  }

  async testPaymentFlow() {
    console.log('\n💰 Testing Payment Flow...');
    
    try {
      if (!this.testUser || !this.testProduct) {
        throw new Error('Prerequisites not met');
      }

      // Create a test order with pending status
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .insert({
          user_id: this.testUser.id,
          status: 'pending',
          payment_status: 'awaiting_payment',
          total_amount: 599.98, // 2 items * 299.99
          payment_provider: 'stripe',
          customer_email: this.testUser.email,
          session_id: 'cs_test_' + Date.now()
        })
        .select()
        .single();

      if (orderError) throw orderError;

      this.testOrder = orderData;
      logTest('Order Creation', 'PASS', `Order created with status: ${orderData.status}`);

      // Simulate successful payment webhook
      const webhookEvent = {
        id: 'evt_test_payment',
        object: 'event',
        api_version: '2025-02-24.acacia',
        created: Math.floor(Date.now() / 1000),
        data: {
          object: {
            id: orderData.session_id,
            object: 'checkout.session',
            amount_total: 59998, // in cents
            customer_details: { email: this.testUser.email },
            metadata: {
              order_id: orderData.id,
              customer_email: this.testUser.email
            },
            payment_intent: 'pi_test_' + Date.now(),
            payment_method_types: ['card'],
            payment_status: 'paid'
          }
        },
        livemode: false,
        pending_webhooks: 1,
        request: { id: 'req_test', idempotency_key: null },
        type: 'checkout.session.completed'
      };

      const signature = createStripeSignature(webhookEvent, WEBHOOK_SECRET);

      // Send webhook to local server
      const { response, status } = await makeApiRequest('/api/stripe/webhook', {
        method: 'POST',
        headers: {
          'stripe-signature': signature
        },
        body: JSON.stringify(webhookEvent)
      });

      if (status !== 200) {
        throw new Error(`Webhook failed with status: ${status}`);
      }

      logTest('Payment Webhook Processing', 'PASS', 'Webhook successfully processed');

      // Verify order status was updated
      const { data: updatedOrder, error: fetchError } = await supabase
        .from('orders')
        .select('status, payment_status')
        .eq('id', orderData.id)
        .single();

      if (fetchError) throw fetchError;

      if (updatedOrder.status !== 'processing' || updatedOrder.payment_status !== 'completed') {
        throw new Error(`Order status not updated correctly. Got: ${updatedOrder.status}/${updatedOrder.payment_status}`);
      }

      logTest('Payment Status Update', 'PASS', 'Order status correctly updated to processing/completed');

    } catch (error) {
      logTest('Payment Flow', 'FAIL', error.message);
    }
  }

  async testOrderVerification() {
    console.log('\n📦 Testing Order Verification...');
    
    try {
      if (!this.testOrder) {
        throw new Error('No test order available');
      }

      // Verify order details
      const { data: orderDetails, error: orderError } = await supabase
        .from('orders')
        .select(`
          *,
          user:profiles(email, full_name)
        `)
        .eq('id', this.testOrder.id)
        .single();

      if (orderError) throw orderError;

      logTest('Order Detail Retrieval', 'PASS', 'Order details retrieved successfully');

      // Test order status tracking
      const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
      if (!validStatuses.includes(orderDetails.status)) {
        throw new Error(`Invalid order status: ${orderDetails.status}`);
      }

      logTest('Order Status Validation', 'PASS', `Valid status: ${orderDetails.status}`);

    } catch (error) {
      logTest('Order Verification', 'FAIL', error.message);
    }
  }

  async testAdminDashboard() {
    console.log('\n👩‍💼 Testing Admin Dashboard Access...');
    
    try {
      // Test admin authentication check
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', this.testUser.id)
        .single();

      if (profileError) throw profileError;

      if (profileData.is_admin) {
        logTest('Admin Access Check', 'PASS', 'User has admin privileges');
      } else {
        logTest('Admin Access Check', 'PASS', 'User correctly identified as non-admin');
      }

      // Test order management queries
      const { data: allOrders, error: ordersError } = await supabase
        .from('orders')
        .select(`
          id,
          status,
          total_amount,
          created_at,
          user:profiles(email)
        `)
        .order('created_at', { ascending: false })
        .limit(10);

      if (ordersError) throw ordersError;

      logTest('Admin Order Queries', 'PASS', `Retrieved ${allOrders.length} orders`);

    } catch (error) {
      logTest('Admin Dashboard', 'FAIL', error.message);
    }
  }

  async testErrorHandling() {
    console.log('\n🚨 Testing Error Handling...');
    
    try {
      // Test invalid product access
      const { data: invalidProduct, error: productError } = await supabase
        .from('products')
        .select('*')
        .eq('slug', 'non-existent-product')
        .single();

      if (!productError) {
        logTest('Invalid Product Handling', 'FAIL', 'Should have returned error for non-existent product');
      } else {
        logTest('Invalid Product Handling', 'PASS', 'Correctly handled non-existent product');
      }

      // Test duplicate cart item handling
      try {
        await supabase
          .from('cart_items')
          .insert({
            user_id: this.testUser.id,
            product_id: this.testProduct.id,
            quantity: 1
          });
        
        logTest('Duplicate Cart Item', 'PASS', 'System handled duplicate cart item gracefully');
      } catch (error) {
        logTest('Duplicate Cart Item', 'PASS', 'System correctly prevented duplicate cart item');
      }

    } catch (error) {
      logTest('Error Handling', 'FAIL', error.message);
    }
  }

  async testSecurityFeatures() {
    console.log('\n🔐 Testing Security Features...');
    
    try {
      // Test RLS policies - try to access another user's data
      const { data: otherUserData, error: rlsError } = await supabase
        .from('cart_items')
        .select('*')
        .eq('user_id', 'non-existent-user-id');

      // This should return empty array due to RLS, not an error
      if (rlsError) {
        logTest('RLS Policy Test', 'FAIL', 'RLS should filter results, not error');
      } else {
        logTest('RLS Policy Test', 'PASS', 'RLS policies correctly filtering data');
      }

      // Test input validation (this would typically be done at API level)
      const invalidOrderData = {
        user_id: 'invalid-uuid',
        status: 'invalid-status',
        total_amount: -100
      };

      try {
        await supabase.from('orders').insert(invalidOrderData);
        logTest('Input Validation', 'FAIL', 'Should have rejected invalid data');
      } catch (error) {
        logTest('Input Validation', 'PASS', 'System correctly rejected invalid data');
      }

    } catch (error) {
      logTest('Security Features', 'FAIL', error.message);
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test data...');
    
    try {
      // Clean up in reverse order of dependencies
      if (this.testOrder) {
        await supabase.from('orders').delete().eq('id', this.testOrder.id);
        logTest('Order Cleanup', 'PASS', 'Test order removed');
      }

      if (this.testUser) {
        await supabase.from('cart_items').delete().eq('user_id', this.testUser.id);
        await supabase.from('shipping_addresses').delete().eq('user_id', this.testUser.id);
        await supabase.from('profiles').delete().eq('id', this.testUser.id);
        await supabase.auth.admin.deleteUser(this.testUser.id);
        logTest('User Cleanup', 'PASS', 'Test user and related data removed');
      }

      if (this.testProduct) {
        await supabase.from('products').delete().eq('id', this.testProduct.id);
        logTest('Product Cleanup', 'PASS', 'Test product removed');
      }

    } catch (error) {
      logTest('Cleanup', 'FAIL', error.message);
    }
  }

  printTestSummary() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST SUITE SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⏭️  Skipped: ${testResults.skipped}`);
    console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    if (testResults.errors.length > 0) {
      console.log('\n❌ FAILED TESTS:');
      testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.test}: ${error.details}`);
      });
    }
    
    console.log('\n🎉 Test suite completed!');
    
    // Return exit code for CI/CD
    return testResults.failed === 0 ? 0 : 1;
  }
}

// Run the test suite
async function runTests() {
  const testSuite = new ECommerceTestSuite();
  const exitCode = await testSuite.runAllTests();
  process.exit(exitCode);
}

// Check if running directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test suite failed to start:', error);
    process.exit(1);
  });
}

module.exports = { ECommerceTestSuite, runTests };