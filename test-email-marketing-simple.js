#!/usr/bin/env node

/**
 * Simple Email Marketing Service Test
 * Tests the database functionality and basic operations
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testEmailMarketingDatabase() {
  console.log('🧪 Testing Email Marketing Database Operations...\n');

  try {
    // Test 1: Test email_campaigns table
    console.log('1️⃣ Testing email_campaigns table operations...');
    
    // Create a test campaign
    const testCampaign = {
      name: 'Test Campaign ' + Date.now(),
      subject: 'Test Email Subject',
      content: '<p>This is a test email campaign content.</p>',
      template_type: 'newsletter',
      target_audience: 'all_users',
      status: 'draft'
    };

    const { data: campaign, error: createError } = await supabase
      .from('email_campaigns')
      .insert(testCampaign)
      .select()
      .single();

    if (createError) {
      console.log('❌ Failed to create test campaign:', createError.message);
      return false;
    }

    console.log('✅ Successfully created test campaign:', campaign.id);

    // Test 2: Read the campaign back
    const { data: readCampaign, error: readError } = await supabase
      .from('email_campaigns')
      .select('*')
      .eq('id', campaign.id)
      .single();

    if (readError) {
      console.log('❌ Failed to read campaign:', readError.message);
      return false;
    }

    console.log('✅ Successfully read campaign:', readCampaign.name);

    // Test 3: Update the campaign
    const { data: updatedCampaign, error: updateError } = await supabase
      .from('email_campaigns')
      .update({ status: 'scheduled' })
      .eq('id', campaign.id)
      .select()
      .single();

    if (updateError) {
      console.log('❌ Failed to update campaign:', updateError.message);
      return false;
    }

    console.log('✅ Successfully updated campaign status:', updatedCampaign.status);

    // Test 4: Test campaign_recipients table
    console.log('\n2️⃣ Testing campaign_recipients table operations...');

    const testRecipient = {
      campaign_id: campaign.id,
      email: '<EMAIL>',
      status: 'pending'
    };

    const { data: recipient, error: recipientError } = await supabase
      .from('campaign_recipients')
      .insert(testRecipient)
      .select()
      .single();

    if (recipientError) {
      console.log('❌ Failed to create test recipient:', recipientError.message);
      return false;
    }

    console.log('✅ Successfully created test recipient:', recipient.email);

    // Test 5: Query recipients for campaign
    const { data: recipients, error: recipientsError } = await supabase
      .from('campaign_recipients')
      .select('*')
      .eq('campaign_id', campaign.id);

    if (recipientsError) {
      console.log('❌ Failed to query recipients:', recipientsError.message);
      return false;
    }

    console.log('✅ Successfully queried recipients:', recipients.length, 'found');

    // Test 6: Test profiles marketing columns
    console.log('\n3️⃣ Testing profiles marketing columns...');

    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, marketing_emails_enabled, newsletter_subscribed')
      .limit(3);

    if (profilesError) {
      console.log('❌ Marketing columns error:', profilesError.message);
      if (profilesError.message.includes('does not exist')) {
        console.log('⚠️  Marketing columns not added to profiles table yet');
        console.log('💡 This is optional - the service can work without these columns');
      }
    } else {
      console.log('✅ Marketing columns accessible, found', profiles.length, 'profiles');
    }

    // Cleanup: Delete test data
    console.log('\n4️⃣ Cleaning up test data...');

    await supabase
      .from('campaign_recipients')
      .delete()
      .eq('campaign_id', campaign.id);

    await supabase
      .from('email_campaigns')
      .delete()
      .eq('id', campaign.id);

    console.log('✅ Test data cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    return false;
  }
}

async function testTypeScriptCompilation() {
  console.log('\n🧪 Testing TypeScript Compilation...\n');

  try {
    const { spawn } = require('child_process');
    
    return new Promise((resolve) => {
      // Test compilation with Next.js build (which handles path aliases)
      const buildCheck = spawn('npm', ['run', 'build'], {
        stdio: 'pipe',
        cwd: process.cwd()
      });

      let output = '';
      let errorOutput = '';

      buildCheck.stdout.on('data', (data) => {
        output += data.toString();
      });

      buildCheck.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      buildCheck.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Next.js build successful - TypeScript compilation passed');
          console.log('✅ Email marketing service compiles without errors');
        } else {
          console.log('❌ Next.js build failed');
          if (errorOutput.includes('email-marketing')) {
            console.log('❌ Email marketing service has compilation errors');
          } else {
            console.log('⚠️  Build failed but may not be related to email marketing service');
          }
          
          // Show relevant error lines
          const lines = errorOutput.split('\n');
          const relevantLines = lines.filter(line => 
            line.includes('email-marketing') || 
            line.includes('error TS')
          );
          
          if (relevantLines.length > 0) {
            console.log('Relevant errors:');
            relevantLines.slice(0, 5).forEach(line => console.log('  ', line));
          }
        }
        resolve({ code, output, errorOutput });
      });

      // Kill the build after 60 seconds if it's still running
      setTimeout(() => {
        buildCheck.kill();
        console.log('⚠️  Build timeout - killed after 60 seconds');
        resolve({ code: -1, timeout: true });
      }, 60000);
    });

  } catch (error) {
    console.error('❌ TypeScript test failed:', error.message);
    return { error };
  }
}

async function runTests() {
  console.log('🚀 Email Marketing Service - Comprehensive Test\n');
  console.log('=' .repeat(60));

  // Test 1: Database operations
  const dbSuccess = await testEmailMarketingDatabase();
  
  // Test 2: TypeScript compilation (optional - can be slow)
  let tsSuccess = true;
  if (process.argv.includes('--include-build')) {
    const tsResults = await testTypeScriptCompilation();
    tsSuccess = tsResults.code === 0;
  } else {
    console.log('\n⏭️  Skipping TypeScript build test (use --include-build to enable)');
  }

  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST RESULTS');
  console.log('=' .repeat(60));

  if (dbSuccess && tsSuccess) {
    console.log('🎉 ALL TESTS PASSED!');
    console.log('✅ Email marketing database operations work correctly');
    console.log('✅ TypeScript fixes are successful');
    console.log('✅ Service is ready for use');
  } else {
    console.log('⚠️  SOME TESTS FAILED');
    
    if (!dbSuccess) {
      console.log('❌ Database operations failed');
      console.log('💡 Check database connection and table structure');
    }
    
    if (!tsSuccess) {
      console.log('❌ TypeScript compilation failed');
      console.log('💡 Check for import errors and type issues');
    }
  }

  console.log('\n🏁 Testing completed');
  return dbSuccess && tsSuccess;
}

// Run the tests
runTests()
  .then(success => process.exit(success ? 0 : 1))
  .catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
