#!/usr/bin/env node

/**
 * Test script for Email Marketing Service
 * Tests the TypeScript fixes and basic functionality
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testEmailMarketingTables() {
  console.log('🧪 Testing Email Marketing Database Tables...\n');

  try {
    // Test 1: Check if email_campaigns table exists
    console.log('1️⃣ Testing email_campaigns table...');
    const { data: campaigns, error: campaignsError } = await supabase
      .from('email_campaigns')
      .select('id, name, status')
      .limit(5);

    if (campaignsError) {
      console.log('❌ email_campaigns table error:', campaignsError.message);
      if (campaignsError.message.includes('does not exist')) {
        console.log('💡 Run the migration: supabase/migrations/20250720_create_email_marketing.sql');
      }
    } else {
      console.log('✅ email_campaigns table accessible');
      console.log(`   Found ${campaigns?.length || 0} campaigns`);
    }

    // Test 2: Check if campaign_recipients table exists
    console.log('\n2️⃣ Testing campaign_recipients table...');
    const { data: recipients, error: recipientsError } = await supabase
      .from('campaign_recipients')
      .select('id, email, status')
      .limit(5);

    if (recipientsError) {
      console.log('❌ campaign_recipients table error:', recipientsError.message);
      if (recipientsError.message.includes('does not exist')) {
        console.log('💡 Run the migration: supabase/migrations/20250720_create_email_marketing.sql');
      }
    } else {
      console.log('✅ campaign_recipients table accessible');
      console.log(`   Found ${recipients?.length || 0} recipients`);
    }

    // Test 3: Check profiles table for marketing columns
    console.log('\n3️⃣ Testing profiles table marketing columns...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, marketing_emails_enabled, newsletter_subscribed')
      .limit(3);

    if (profilesError) {
      console.log('❌ Profiles marketing columns error:', profilesError.message);
      if (profilesError.message.includes('does not exist')) {
        console.log('💡 Marketing columns not added to profiles table yet');
      }
    } else {
      console.log('✅ Profiles marketing columns accessible');
      console.log(`   Found ${profiles?.length || 0} profiles with marketing preferences`);
    }

    return { campaignsError, recipientsError, profilesError };

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    return { error };
  }
}

async function testEmailMarketingService() {
  console.log('\n🧪 Testing Email Marketing Service Class...\n');

  try {
    // Import the service (this will test if TypeScript compilation works)
    console.log('1️⃣ Importing EmailMarketingService...');
    
    // We can't directly import the TS file, so let's test the compilation
    const { spawn } = require('child_process');
    
    return new Promise((resolve) => {
      const tscCheck = spawn('npx', ['tsc', '--noEmit', 'src/lib/email-marketing.ts'], {
        stdio: 'pipe',
        cwd: process.cwd()
      });

      let output = '';
      let errorOutput = '';

      tscCheck.stdout.on('data', (data) => {
        output += data.toString();
      });

      tscCheck.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      tscCheck.on('close', (code) => {
        if (code === 0) {
          console.log('✅ TypeScript compilation successful - no errors found');
          console.log('✅ Email marketing service TypeScript fixes are working');
        } else {
          console.log('❌ TypeScript compilation failed');
          console.log('Error output:', errorOutput);
          console.log('Standard output:', output);
        }
        resolve({ code, output, errorOutput });
      });
    });

  } catch (error) {
    console.error('❌ Service test failed:', error.message);
    return { error };
  }
}

async function testEmailMarketingMigration() {
  console.log('\n🧪 Testing Email Marketing Migration Status...\n');

  try {
    // Check if the migration has been run
    console.log('1️⃣ Checking migration status...');
    
    const { data: migrations, error } = await supabase
      .from('supabase_migrations')
      .select('version, name')
      .like('name', '%email_marketing%');

    if (error) {
      console.log('❌ Could not check migration status:', error.message);
    } else if (migrations && migrations.length > 0) {
      console.log('✅ Email marketing migration found:');
      migrations.forEach(migration => {
        console.log(`   - ${migration.version}: ${migration.name}`);
      });
    } else {
      console.log('⚠️  Email marketing migration not found');
      console.log('💡 You may need to run: supabase/migrations/20250720_create_email_marketing.sql');
    }

    return { migrations, error };

  } catch (error) {
    console.error('❌ Migration check failed:', error.message);
    return { error };
  }
}

async function runAllTests() {
  console.log('🚀 Starting Email Marketing Service Tests\n');
  console.log('=' .repeat(60));

  // Test 1: Database tables
  const dbResults = await testEmailMarketingTables();
  
  // Test 2: TypeScript compilation
  const serviceResults = await testEmailMarketingService();
  
  // Test 3: Migration status
  const migrationResults = await testEmailMarketingMigration();

  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(60));

  const hasDbErrors = dbResults.campaignsError || dbResults.recipientsError;
  const hasServiceErrors = serviceResults.code !== 0;
  const hasMigrationIssues = migrationResults.error || !migrationResults.migrations?.length;

  if (!hasDbErrors && !hasServiceErrors) {
    console.log('✅ ALL TESTS PASSED');
    console.log('✅ Email marketing service is working correctly');
    console.log('✅ TypeScript fixes are successful');
  } else {
    console.log('⚠️  SOME TESTS FAILED');
    
    if (hasDbErrors) {
      console.log('❌ Database tables need to be created');
      console.log('💡 Run: supabase/migrations/20250720_create_email_marketing.sql');
    }
    
    if (hasServiceErrors) {
      console.log('❌ TypeScript compilation issues found');
    }
    
    if (hasMigrationIssues) {
      console.log('⚠️  Migration status unclear');
    }
  }

  console.log('\n🏁 Test completed');
}

// Run the tests
runAllTests().catch(console.error);
