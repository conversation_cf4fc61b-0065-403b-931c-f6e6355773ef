/**
 * Live end-to-end payment flow test
 * Tests the actual payment flow with a running server
 * 
 * Run with: node test-live-payment-flow.js
 */

const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Load all environment files
require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '.env.production' });

// Test configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;
const APP_URL = 'http://localhost:3002';

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY || !WEBHOOK_SECRET) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Test data
const TEST_USER_EMAIL = '<EMAIL>';

function logTest(testName, status, message = '') {
  const statusEmoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '🔄';
  console.log(`${statusEmoji} ${testName}: ${message}`);
}

function logSection(sectionName) {
  console.log(`\n=== ${sectionName} ===`);
}

// Create a valid Stripe signature
function createStripeSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const payloadString = JSON.stringify(payload);
  const signedPayload = `${timestamp}.${payloadString}`;
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return `t=${timestamp},v1=${signature}`;
}

async function createTestUser() {
  // Try to find existing user profile first
  const { data: existingProfile } = await supabase
    .from('profiles')
    .select('id, email')
    .eq('email', TEST_USER_EMAIL)
    .single();

  if (existingProfile) {
    return { id: existingProfile.id, email: existingProfile.email };
  }

  // If no profile exists, try to create a new user
  const { data: user, error } = await supabase.auth.admin.createUser({
    email: TEST_USER_EMAIL,
    password: 'test-password-123',
    email_confirm: true
  });

  if (error && !error.message.includes('already been registered')) {
    throw error;
  }

  const createdUser = user?.user || (await supabase.auth.admin.listUsers()).data.users.find(u => u.email === TEST_USER_EMAIL);
  
  if (!createdUser) {
    throw new Error('Failed to create or find test user');
  }

  // Ensure profile exists
  const { error: profileError } = await supabase
    .from('profiles')
    .upsert({
      id: createdUser.id,
      email: createdUser.email,
      full_name: 'Live Test User'
    });

  if (profileError) {
    console.warn('Profile creation warning:', profileError.message);
  }

  return createdUser;
}

async function cleanupTestData(userId) {
  // Clean up test orders
  await supabase
    .from('orders')
    .delete()
    .eq('user_id', userId);

  // Clean up test profile
  await supabase
    .from('profiles')
    .delete()
    .eq('id', userId);

  // Clean up test user
  try {
    await supabase.auth.admin.deleteUser(userId);
  } catch (error) {
    console.warn('User deletion warning:', error.message);
  }
}

async function testOrderCreationAPI() {
  logSection('TEST 1: Order Creation API');
  
  try {
    const testUser = await createTestUser();
    
    // Test the actual create-checkout API
    const response = await fetch(`${APP_URL}/api/stripe/create-checkout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `supabase-auth-token=${testUser.id}` // Simulate auth
      },
      body: JSON.stringify({
        items: [
          {
            quantity: 1,
            product: {
              id: 'test-product',
              name: 'Test Payment Bug Fix',
              price: 10.00,
              condition: 'excellent',
              main_image_url: 'test-image.jpg'
            }
          }
        ],
        customerEmail: TEST_USER_EMAIL,
        shippingInfo: {
          cost: 5.00,
          description: 'Test shipping'
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      logTest('Order Creation API', 'FAIL', `API Error: ${response.status} - ${errorText}`);
      await cleanupTestData(testUser.id);
      return null;
    }

    const result = await response.json();
    
    if (result.sessionId && result.orderId) {
      // Verify order was created with correct status
      const { data: order } = await supabase
        .from('orders')
        .select('*')
        .eq('id', result.orderId)
        .single();

      if (order && order.status === 'processing' && order.payment_status === 'processing') {
        logTest('Order Creation API', 'PASS', `Order created with correct "processing" status. Order ID: ${result.orderId}`);
        return { orderId: result.orderId, sessionId: result.sessionId, userId: testUser.id };
      } else {
        logTest('Order Creation API', 'FAIL', `Wrong status: ${order?.status}/${order?.payment_status}`);
      }
    } else {
      logTest('Order Creation API', 'FAIL', 'No session ID or order ID returned');
    }

    await cleanupTestData(testUser.id);
    return null;
    
  } catch (error) {
    logTest('Order Creation API', 'FAIL', error.message);
    return null;
  }
}

async function testWebhookWithRealOrder(orderId, sessionId) {
  logSection('TEST 2: Webhook with Real Order');
  
  try {
    // Create a realistic webhook event using the real order ID
    const webhookEvent = {
      id: 'evt_test_webhook_live',
      object: 'event',
      api_version: '2025-02-24.acacia',
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: sessionId,
          object: 'checkout.session',
          amount_total: 1500, // $15.00 in cents
          customer_details: {
            email: TEST_USER_EMAIL
          },
          metadata: {
            order_id: orderId,
            customer_email: TEST_USER_EMAIL
          },
          payment_intent: 'pi_test_live_payment',
          payment_method_types: ['card'],
          payment_status: 'paid',
          shipping_details: {
            name: 'Live Test Customer',
            address: {
              line1: '123 Test St',
              city: 'Test City',
              state: 'TC',
              postal_code: '12345',
              country: 'US'
            }
          }
        }
      },
      livemode: false,
      pending_webhooks: 1,
      request: {
        id: 'req_test_live',
        idempotency_key: null
      },
      type: 'checkout.session.completed'
    };

    const signature = createStripeSignature(webhookEvent, WEBHOOK_SECRET);
    
    const response = await fetch(`${APP_URL}/api/stripe/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': signature
      },
      body: JSON.stringify(webhookEvent)
    });
    
    const responseText = await response.text();
    
    if (response.ok) {
      // Check if order status was updated
      const { data: updatedOrder } = await supabase
        .from('orders')
        .select('status, payment_status')
        .eq('id', orderId)
        .single();

      if (updatedOrder && updatedOrder.status === 'pending' && updatedOrder.payment_status === 'completed') {
        logTest('Webhook with Real Order', 'PASS', 'Order updated from "processing" to "pending" (payment confirmed)');
        return true;
      } else {
        logTest('Webhook with Real Order', 'FAIL', `Status: ${updatedOrder?.status}, Payment: ${updatedOrder?.payment_status}`);
        return false;
      }
    } else {
      logTest('Webhook with Real Order', 'FAIL', `Webhook rejected: ${response.status} - ${responseText}`);
      return false;
    }
    
  } catch (error) {
    logTest('Webhook with Real Order', 'FAIL', error.message);
    return false;
  }
}

async function testFailedPaymentWebhook(orderId, sessionId) {
  logSection('TEST 3: Failed Payment Webhook');
  
  try {
    // Reset order to processing status
    await supabase
      .from('orders')
      .update({
        status: 'processing',
        payment_status: 'processing'
      })
      .eq('id', orderId);

    // Create expired session webhook
    const webhookEvent = {
      id: 'evt_test_expired_live',
      object: 'event',
      api_version: '2025-02-24.acacia',
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: sessionId,
          object: 'checkout.session',
          metadata: {
            order_id: orderId
          }
        }
      },
      livemode: false,
      pending_webhooks: 1,
      request: {
        id: 'req_test_expired_live',
        idempotency_key: null
      },
      type: 'checkout.session.expired'
    };

    const signature = createStripeSignature(webhookEvent, WEBHOOK_SECRET);
    
    const response = await fetch(`${APP_URL}/api/stripe/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': signature
      },
      body: JSON.stringify(webhookEvent)
    });
    
    if (response.ok) {
      // Check if order status was updated to cancelled
      const { data: updatedOrder } = await supabase
        .from('orders')
        .select('status, payment_status')
        .eq('id', orderId)
        .single();

      if (updatedOrder && updatedOrder.status === 'cancelled' && updatedOrder.payment_status === 'failed') {
        logTest('Failed Payment Webhook', 'PASS', 'Order updated from "processing" to "cancelled" (session expired)');
        return true;
      } else {
        logTest('Failed Payment Webhook', 'FAIL', `Status: ${updatedOrder?.status}, Payment: ${updatedOrder?.payment_status}`);
        return false;
      }
    } else {
      const responseText = await response.text();
      logTest('Failed Payment Webhook', 'FAIL', `Webhook rejected: ${response.status} - ${responseText}`);
      return false;
    }
    
  } catch (error) {
    logTest('Failed Payment Webhook', 'FAIL', error.message);
    return false;
  }
}

async function runLiveTests() {
  console.log('🌐 Live Payment Flow Testing');
  console.log('=============================');
  console.log('Testing with actual server running on http://localhost:3002\n');
  
  const testResults = [];
  let testData = null;
  
  // Test 1: Order Creation API
  testData = await testOrderCreationAPI();
  testResults.push(!!testData);
  
  if (testData) {
    // Test 2: Webhook with Real Order
    const test2Result = await testWebhookWithRealOrder(testData.orderId, testData.sessionId);
    testResults.push(test2Result);
    
    // Test 3: Failed Payment Webhook
    const test3Result = await testFailedPaymentWebhook(testData.orderId, testData.sessionId);
    testResults.push(test3Result);
    
    // Cleanup
    try {
      await cleanupTestData(testData.userId);
    } catch (error) {
      console.log('⚠️  Cleanup warning:', error.message);
    }
  } else {
    logTest('Webhook Tests', 'SKIP', 'No order created to test webhooks');
    testResults.push(false);
    testResults.push(false);
  }
  
  // Summary
  logSection('LIVE TEST SUMMARY');
  const passed = testResults.filter(Boolean).length;
  const total = testResults.length;
  
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 ALL LIVE TESTS PASSED! Payment flow is working correctly online.');
    console.log('\n🛡️  Critical Bug Status: FIXED');
    console.log('   • Orders no longer show as "pending" when payments fail');
    console.log('   • Only successful payments are marked for fulfillment');
    console.log('   • Admin dashboard now shows accurate order statuses');
  } else {
    console.log('\n⚠️  Some live tests failed. The payment bug may still exist.');
  }
  
  process.exit(passed === total ? 0 : 1);
}

// Run the live tests
runLiveTests().catch(error => {
  console.error('❌ Live test suite failed:', error);
  process.exit(1);
});