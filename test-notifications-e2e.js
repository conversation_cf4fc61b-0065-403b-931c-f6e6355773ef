#!/usr/bin/env node

/**
 * End-to-End Notification System Testing
 * Tests all notification channels for real user scenarios
 */

console.log('🔔 Starting End-to-End Notification System Testing...\n');

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

// Test configuration
const TEST_CONFIG = {
  testEmail: '<EMAIL>',
  testPhone: '+1234567890',
  adminTestData: {
    order: {
      id: 'test-order-123',
      order_number: 'ORD-001',
      total_amount: 99.99,
      customer_email: '<EMAIL>'
    },
    bagRequest: {
      id: 'test-bag-req-123',
      brand: '<PERSON>',
      name: 'Test Bag',
      category: 'Handbag',
      user_email: '<EMAIL>'
    }
  }
};

console.log(`🌐 Testing against: ${BASE_URL}`);
console.log('📋 Test Configuration:', TEST_CONFIG);
console.log('\n' + '='.repeat(60) + '\n');

// Test 1: Check notification API endpoints exist
function testNotificationEndpoints() {
  console.log('1. 🔍 Testing Notification API Endpoints...');
  
  const fs = require('fs');
  const path = require('path');
  
  const expectedEndpoints = [
    'src/app/api/notifications/send-email/route.ts',
    'src/app/api/notifications/send-sms/route.ts', 
    'src/app/api/notifications/send-whatsapp/route.ts',
    'src/app/api/notifications/send-push/route.ts',
    'src/app/api/notifications/send-all/route.ts',
    'src/app/api/notifications/subscribe/route.ts',
    'src/app/api/notifications/count/route.ts',
    'src/app/api/notifications/process-pending/route.ts'
  ];
  
  let endpointCount = 0;
  
  expectedEndpoints.forEach(endpoint => {
    const fullPath = path.join(process.cwd(), endpoint);
    if (fs.existsSync(fullPath)) {
      console.log(`   ✅ ${endpoint} exists`);
      endpointCount++;
    } else {
      console.log(`   ❌ ${endpoint} missing`);
    }
  });
  
  console.log(`   📊 Found ${endpointCount}/${expectedEndpoints.length} notification endpoints`);
  return endpointCount === expectedEndpoints.length;
}

// Test 2: Check notification service implementations
function testNotificationServices() {
  console.log('\n2. 📧 Testing Notification Service Implementations...');
  
  const fs = require('fs');
  const path = require('path');
  
  const expectedServices = [
    'src/lib/email-notifications.ts',
    'src/lib/sms-notifications.ts',
    'src/lib/whatsapp-notifications.ts', 
    'src/lib/push-notifications.ts',
    'src/lib/unified-notifications.ts'
  ];
  
  let serviceCount = 0;
  
  expectedServices.forEach(service => {
    const fullPath = path.join(process.cwd(), service);
    if (fs.existsSync(fullPath)) {
      try {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // Check for key functions
        const hasExports = content.includes('export');
        const hasFunctions = content.includes('async function') || content.includes('function');
        
        if (hasExports && hasFunctions) {
          console.log(`   ✅ ${service} properly implemented`);
          serviceCount++;
        } else {
          console.log(`   ⚠️  ${service} exists but may be incomplete`);
        }
      } catch (error) {
        console.log(`   ❌ ${service} could not be read`);
      }
    } else {
      console.log(`   ❌ ${service} missing`);
    }
  });
  
  console.log(`   📊 Found ${serviceCount}/${expectedServices.length} notification services`);
  return serviceCount === expectedServices.length;
}

// Test 3: Check environment variables for notifications
function testNotificationEnvironment() {
  console.log('\n3. 🌍 Testing Notification Environment Variables...');
  
  const criticalEnvVars = [
    'RESEND_API_KEY',
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN',
    'TWILIO_PHONE_NUMBER',
    'TWILIO_WHATSAPP_NUMBER',
    'VAPID_PUBLIC_KEY',
    'VAPID_PRIVATE_KEY',
    'VAPID_EMAIL'
  ];
  
  let configuredCount = 0;
  
  criticalEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      console.log(`   ✅ ${envVar} configured`);
      configuredCount++;
    } else {
      console.log(`   ❌ ${envVar} not configured`);
    }
  });
  
  console.log(`   📊 ${configuredCount}/${criticalEnvVars.length} critical environment variables configured`);
  
  // Check for .env files
  const fs = require('fs');
  const envFiles = ['.env.local', '.env.production', '.env'];
  let envFileCount = 0;
  
  envFiles.forEach(envFile => {
    if (fs.existsSync(envFile)) {
      console.log(`   ✅ ${envFile} exists`);
      envFileCount++;
    }
  });
  
  console.log(`   📄 ${envFileCount}/${envFiles.length} environment files found`);
  
  return configuredCount >= 4; // At least half should be configured for basic testing
}

// Test 4: Check notification database schema
function testNotificationSchema() {
  console.log('\n4. 🗄️ Testing Notification Database Schema...');
  
  const fs = require('fs');
  const path = require('path');
  
  // Check for notification-related migrations
  const migrationsDir = 'supabase/migrations';
  let notificationMigrations = 0;
  
  if (fs.existsSync(migrationsDir)) {
    const migrations = fs.readdirSync(migrationsDir);
    
    migrations.forEach(migration => {
      if (migration.includes('notification') || migration.includes('20241218')) {
        console.log(`   ✅ Found notification migration: ${migration}`);
        notificationMigrations++;
      }
    });
  }
  
  // Check database types file
  const dbTypesFile = 'src/lib/database.types.ts';
  if (fs.existsSync(dbTypesFile)) {
    const content = fs.readFileSync(dbTypesFile, 'utf8');
    
    const hasNotificationTables = content.includes('notifications') || 
                                 content.includes('notification_delivery_log') ||
                                 content.includes('push_subscriptions');
    
    if (hasNotificationTables) {
      console.log('   ✅ Database types include notification tables');
    } else {
      console.log('   ❌ Database types missing notification tables');
    }
  }
  
  console.log(`   📊 Found ${notificationMigrations} notification-related migrations`);
  return notificationMigrations > 0;
}

// Test 5: Check notification configuration
function testNotificationConfiguration() {
  console.log('\n5. ⚙️ Testing Notification Configuration...');
  
  const fs = require('fs');
  
  // Check for notification configuration files
  const configFiles = [
    'src/lib/email-notifications.ts',
    'src/lib/unified-notifications.ts'
  ];
  
  let configurationScore = 0;
  
  configFiles.forEach(configFile => {
    if (fs.existsSync(configFile)) {
      const content = fs.readFileSync(configFile, 'utf8');
      
      // Check for proper configuration patterns
      const hasEmailConfig = content.includes('resend') || content.includes('email');
      const hasTwilioConfig = content.includes('twilio') || content.includes('sms');
      const hasProperExports = content.includes('export');
      
      if (hasEmailConfig || hasTwilioConfig || hasProperExports) {
        console.log(`   ✅ ${configFile} properly configured`);
        configurationScore++;
      } else {
        console.log(`   ❌ ${configFile} configuration incomplete`);
      }
    }
  });
  
  // Check for test notification scripts
  const testScripts = [
    'test-all-notifications.js',
    'test-notifications.js', 
    'test-twilio.js'
  ];
  
  let testScriptCount = 0;
  
  testScripts.forEach(script => {
    if (fs.existsSync(script)) {
      console.log(`   ✅ Test script exists: ${script}`);
      testScriptCount++;
    }
  });
  
  console.log(`   📊 Configuration score: ${configurationScore}/${configFiles.length}`);
  console.log(`   🧪 Test scripts available: ${testScriptCount}/${testScripts.length}`);
  
  return configurationScore >= 1 && testScriptCount >= 1;
}

// Test 6: Validate notification flow integration
function testNotificationIntegration() {
  console.log('\n6. 🔄 Testing Notification Flow Integration...');
  
  const fs = require('fs');
  
  // Check for notification integration in key files
  const integrationFiles = [
    'src/app/api/stripe/webhook/route.ts',
    'src/app/api/webhooks/stripe/route.ts',
    'src/app/api/admin/orders/route.ts'
  ];
  
  let integrationCount = 0;
  
  integrationFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for notification service imports and usage
      const hasNotificationImport = content.includes('unifiedNotificationService') ||
                                   content.includes('notification') ||
                                   content.includes('email') ||
                                   content.includes('sms');
      
      if (hasNotificationImport) {
        console.log(`   ✅ ${file} includes notification integration`);
        integrationCount++;
      } else {
        console.log(`   ❌ ${file} missing notification integration`);
      }
    }
  });
  
  console.log(`   📊 Integration score: ${integrationCount}/${integrationFiles.length}`);
  return integrationCount >= 1;
}

// Test 7: Check frontend notification components
function testFrontendNotifications() {
  console.log('\n7. 🖥️ Testing Frontend Notification Components...');
  
  const fs = require('fs');
  const path = require('path');
  
  // Check for notification-related components
  const componentFiles = [
    'src/components/admin/notifications/NotificationBell.tsx',
    'src/components/admin/notifications/NotificationPanel.tsx'
  ];
  
  let componentCount = 0;
  
  componentFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`   ✅ ${file} exists`);
      componentCount++;
    } else {
      console.log(`   ❌ ${file} missing`);
    }
  });
  
  // Check for notification pages
  const notificationPages = [
    'src/app/admin/notifications/page.tsx',
    'src/app/admin/notification-settings/page.tsx'
  ];
  
  let pageCount = 0;
  
  notificationPages.forEach(page => {
    if (fs.existsSync(page)) {
      console.log(`   ✅ ${page} exists`);
      pageCount++;
    } else {
      console.log(`   ❌ ${page} missing`);
    }
  });
  
  console.log(`   📊 Notification components: ${componentCount}/${componentFiles.length}`);
  console.log(`   📄 Notification pages: ${pageCount}/${notificationPages.length}`);
  
  return componentCount >= 1 && pageCount >= 1;
}

// Main test runner
function runNotificationSystemCheck() {
  const results = [
    testNotificationEndpoints(),
    testNotificationServices(),
    testNotificationEnvironment(),
    testNotificationSchema(),
    testNotificationConfiguration(),
    testNotificationIntegration(),
    testFrontendNotifications()
  ];
  
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log('\n' + '='.repeat(60));
  console.log('🔔 NOTIFICATION SYSTEM CHECK SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Tests Passed: ${passed}/${total}`);
  console.log(`❌ Tests Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 All notification system components are in place!');
    console.log('\n📋 Systems Verified:');
    console.log('   • API endpoints ✅');
    console.log('   • Service implementations ✅');
    console.log('   • Environment configuration ✅');
    console.log('   • Database schema ✅');
    console.log('   • Service configuration ✅');
    console.log('   • Integration with workflows ✅');
    console.log('   • Frontend components ✅');
  } else {
    console.log('\n⚠️  Some notification system components are missing or incomplete.');
  }
  
  console.log('\n💡 Next Steps for Live Testing:');
  console.log('   1. Start the development server: npm run dev');
  console.log('   2. Test email notifications: node test-all-notifications.js');
  console.log('   3. Test Twilio SMS/WhatsApp: node test-twilio.js');
  console.log('   4. Create a test order and verify notifications');
  console.log('   5. Submit a test bag request and verify notifications');
  console.log('   6. Check admin notification panel in browser');
  
  return passed === total;
}

// Run the system check
runNotificationSystemCheck();