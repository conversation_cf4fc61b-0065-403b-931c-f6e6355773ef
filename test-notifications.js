// Simple test script to verify notification functionality
// Run with: node test-notifications.js

const testEmailNotification = async () => {
  try {
    console.log('🧪 Testing email notification...');
    
    const response = await fetch('http://localhost:3000/api/notifications/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'system_alert',
        title: 'Test Email Notification',
        message: 'This is a test email notification from your Treasures of Maimi admin panel.',
        priority: 'low'
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Email notification test successful:', result);
    } else {
      console.log('❌ Email notification test failed:', result);
    }
  } catch (error) {
    console.error('❌ Email notification test error:', error.message);
  }
};

const testSMSNotification = async () => {
  try {
    console.log('🧪 Testing SMS notification...');
    
    const response = await fetch('http://localhost:3000/api/notifications/send-sms', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'system_alert',
        title: 'Test SMS Notification',
        message: 'This is a test SMS notification from your Treasures of Maimi admin panel.',
        priority: 'low'
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ SMS notification test successful:', result);
    } else {
      console.log('❌ SMS notification test failed:', result);
    }
  } catch (error) {
    console.error('❌ SMS notification test error:', error.message);
  }
};

const testWhatsAppNotification = async () => {
  try {
    console.log('🧪 Testing WhatsApp notification...');
    
    const response = await fetch('http://localhost:3000/api/notifications/send-whatsapp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'test',
        phoneNumber: '+1234567890' // Replace with actual phone number for testing
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ WhatsApp notification test successful:', result);
    } else {
      console.log('❌ WhatsApp notification test failed:', result);
    }
  } catch (error) {
    console.error('❌ WhatsApp notification test error:', error.message);
  }
};

const runTests = async () => {
  console.log('🚀 Starting notification system tests...\n');
  
  await testEmailNotification();
  console.log('');
  
  await testSMSNotification();
  console.log('');
  
  await testWhatsAppNotification();
  console.log('');
  
  console.log('🏁 All tests completed!');
  console.log('\n📝 Notes:');
  console.log('- Email notifications should work if Resend API key is configured');
  console.log('- SMS/WhatsApp notifications require Twilio configuration');
  console.log('- Authentication errors are expected when testing without proper session');
  console.log('- Use the admin panel notification settings page for authenticated testing');
};

// Run the tests
runTests();
