/**
 * Integration test for Stripe webhook handling
 * Tests the actual webhook endpoint with simulated Stripe events
 * 
 * Run with: node test-stripe-webhook.js
 */

const crypto = require('crypto');
// Load all environment files
require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '.env.production' });

const WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';

if (!WEBHOOK_SECRET) {
  console.error('❌ STRIPE_WEBHOOK_SECRET not found in environment');
  process.exit(1);
}

// Create a valid Stripe signature
function createStripeSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const payloadString = JSON.stringify(payload);
  const signedPayload = `${timestamp}.${payloadString}`;
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return `t=${timestamp},v1=${signature}`;
}

// Test webhook events
const testEvents = {
  checkoutCompleted: {
    id: 'evt_test_webhook',
    object: 'event',
    api_version: '2025-02-24.acacia',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'cs_test_session_123',
        object: 'checkout.session',
        amount_total: 6000, // $60.00 in cents
        customer_details: {
          email: '<EMAIL>'
        },
        metadata: {
          user_id: 'test-user-123',
          order_id: 'test-order-456',
          customer_email: '<EMAIL>'
        },
        payment_intent: 'pi_test_payment_intent',
        payment_method_types: ['card'],
        payment_status: 'paid',
        shipping_details: {
          name: 'Test Customer',
          address: {
            line1: '123 Test St',
            city: 'Test City',
            state: 'TC',
            postal_code: '12345',
            country: 'US'
          }
        }
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request',
      idempotency_key: null
    },
    type: 'checkout.session.completed'
  },
  
  checkoutExpired: {
    id: 'evt_test_webhook_expired',
    object: 'event',
    api_version: '2025-02-24.acacia',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'cs_test_session_expired',
        object: 'checkout.session',
        metadata: {
          order_id: 'test-order-expired'
        }
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request_expired',
      idempotency_key: null
    },
    type: 'checkout.session.expired'
  },
  
  paymentFailed: {
    id: 'evt_test_webhook_failed',
    object: 'event',
    api_version: '2025-02-24.acacia',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'pi_test_failed_payment',
        object: 'payment_intent',
        status: 'failed'
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request_failed',
      idempotency_key: null
    },
    type: 'payment_intent.payment_failed'
  }
};

async function testWebhookEndpoint(eventName, eventData) {
  console.log(`\n🧪 Testing webhook: ${eventName}`);
  
  try {
    const signature = createStripeSignature(eventData, WEBHOOK_SECRET);
    
    const response = await fetch(`${APP_URL}/api/stripe/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': signature
      },
      body: JSON.stringify(eventData)
    });
    
    const responseText = await response.text();
    
    if (response.ok) {
      console.log(`✅ ${eventName}: Webhook accepted (${response.status})`);
      try {
        const responseData = JSON.parse(responseText);
        console.log(`   Response:`, responseData);
      } catch {
        console.log(`   Response: ${responseText}`);
      }
      return true;
    } else {
      console.log(`❌ ${eventName}: Webhook rejected (${response.status})`);
      console.log(`   Error: ${responseText}`);
      return false;
    }
    
  } catch (error) {
    console.log(`❌ ${eventName}: Request failed - ${error.message}`);
    return false;
  }
}

async function runWebhookTests() {
  console.log('🚀 Testing Stripe Webhook Integration');
  console.log('====================================');
  
  const results = [];
  
  // Test each webhook event
  for (const [eventName, eventData] of Object.entries(testEvents)) {
    const result = await testWebhookEndpoint(eventName, eventData);
    results.push(result);
  }
  
  // Test invalid signature
  console.log(`\n🧪 Testing webhook: invalidSignature`);
  try {
    const response = await fetch(`${APP_URL}/api/stripe/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': 'invalid-signature'
      },
      body: JSON.stringify(testEvents.checkoutCompleted)
    });
    
    if (response.status === 400) {
      console.log(`✅ invalidSignature: Correctly rejected invalid signature`);
      results.push(true);
    } else {
      console.log(`❌ invalidSignature: Should have rejected invalid signature (got ${response.status})`);
      results.push(false);
    }
  } catch (error) {
    console.log(`❌ invalidSignature: Request failed - ${error.message}`);
    results.push(false);
  }
  
  // Summary
  console.log('\n📊 WEBHOOK TEST SUMMARY');
  console.log('========================');
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 ALL WEBHOOK TESTS PASSED!');
  } else {
    console.log('\n⚠️  Some webhook tests failed. Check the implementation.');
  }
  
  return passed === total;
}

// Run the tests
if (require.main === module) {
  runWebhookTests()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('❌ Webhook test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { runWebhookTests, testEvents, createStripeSignature };