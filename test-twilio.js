// Test Twilio SMS functionality
// Run with: node test-twilio.js

// Load environment variables
require('dotenv').config();

const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN;

// Validate required environment variables
if (!TWILIO_ACCOUNT_SID) {
  console.error('❌ TWILIO_ACCOUNT_SID environment variable is required');
  process.exit(1);
}

if (!TWILIO_AUTH_TOKEN) {
  console.error('❌ TWILIO_AUTH_TOKEN environment variable is required');
  process.exit(1);
}

const testTwilioSMS = async (toPhoneNumber, fromPhoneNumber) => {
  try {
    console.log('🧪 Testing Twilio SMS...');
    console.log(`📱 From: ${fromPhoneNumber}`);
    console.log(`📱 To: ${toPhoneNumber}`);
    
    const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/Messages.json`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`).toString('base64')}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        From: fromPhoneNumber,
        To: toPhoneNumber,
        Body: '🛍️ Test SMS from Treasures of Maimi admin panel! If you received this, SMS notifications are working correctly! ✅',
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ SMS sent successfully!');
      console.log(`📧 Message SID: ${result.sid}`);
      console.log(`📊 Status: ${result.status}`);
      console.log(`💰 Price: ${result.price || 'N/A'} ${result.price_unit || ''}`);
    } else {
      console.log('❌ SMS failed:', result);
      if (result.code) {
        console.log(`🚨 Error Code: ${result.code}`);
        console.log(`📝 Error Message: ${result.message}`);
      }
    }
  } catch (error) {
    console.error('❌ SMS test error:', error.message);
  }
};

const testTwilioWhatsApp = async (toPhoneNumber) => {
  try {
    console.log('🧪 Testing Twilio WhatsApp...');
    console.log(`📱 From: whatsapp:+*********** (Sandbox)`);
    console.log(`📱 To: whatsapp:${toPhoneNumber}`);
    
    const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/Messages.json`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`).toString('base64')}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        From: 'whatsapp:+***********',
        To: `whatsapp:${toPhoneNumber}`,
        Body: '🛍️ *Test WhatsApp from Treasures of Maimi!*\n\nHi! This is a test WhatsApp notification from your admin panel.\n\nIf you received this message, WhatsApp notifications are working correctly! ✅\n\n---\n💼 Treasures of Maimi Admin Panel',
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ WhatsApp message sent successfully!');
      console.log(`📧 Message SID: ${result.sid}`);
      console.log(`📊 Status: ${result.status}`);
      console.log(`💰 Price: ${result.price || 'N/A'} ${result.price_unit || ''}`);
    } else {
      console.log('❌ WhatsApp failed:', result);
      if (result.code) {
        console.log(`🚨 Error Code: ${result.code}`);
        console.log(`📝 Error Message: ${result.message}`);
      }
    }
  } catch (error) {
    console.error('❌ WhatsApp test error:', error.message);
  }
};

const runTwilioTests = async () => {
  console.log('🚀 Starting Twilio notification tests...\n');
  
  // You need to replace these with actual phone numbers
  const YOUR_PHONE_NUMBER = '+34631789277'; // The problem phone number
  const TWILIO_PHONE_NUMBER = '+12182191983'; // Our verified Twilio phone number
  
  console.log('⚠️  IMPORTANT: Replace the phone numbers below with real numbers before testing!\n');
  
  await testTwilioSMS(YOUR_PHONE_NUMBER, TWILIO_PHONE_NUMBER);
  console.log('\n' + '='.repeat(50) + '\n');
  
  await testTwilioWhatsApp(YOUR_PHONE_NUMBER);
  console.log('\n🏁 Twilio tests completed!\n');
  
  console.log('📝 Notes:');
  console.log('- For SMS: You need a verified Twilio phone number');
  console.log('- For WhatsApp: You need to join the Twilio Sandbox first');
  console.log('- WhatsApp Sandbox: Send "join <sandbox-keyword>" to ****** 523 8886');
  console.log('- Replace phone numbers in this script with real numbers');
  console.log('- Phone numbers must include country code (e.g., +1 for US)');
};

// Run the tests
runTwilioTests();
