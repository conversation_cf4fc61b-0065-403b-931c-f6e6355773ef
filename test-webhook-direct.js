/**
 * Direct webhook test with detailed logging
 */

const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testWebhookDirect() {
  console.log('🔍 Direct Webhook Test');
  console.log('======================\n');

  try {
    // Create a test order
    const { data: user } = await supabase
      .from('profiles')
      .select('id, email')
      .limit(1)
      .single();

    const { data: order } = await supabase
      .from('orders')
      .insert([{
        user_id: user.id,
        status: 'processing',
        total_amount: 99.99,
        payment_provider: 'stripe',
        payment_status: 'processing',
        customer_email: user.email,
        session_id: 'cs_direct_test_' + Date.now()
      }])
      .select()
      .single();

    console.log('✅ Created test order:', order.id);
    console.log('   Session ID:', order.session_id);

    // Create webhook payload with exact order ID
    const webhookEvent = {
      id: 'evt_direct_test',
      object: 'event',
      api_version: '2025-02-24.acacia',
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: order.session_id,
          object: 'checkout.session',
          amount_total: 9999,
          customer_details: { email: user.email },
          metadata: {
            order_id: order.id,
            customer_email: user.email
          },
          payment_intent: 'pi_direct_test',
          payment_method_types: ['card'],
          payment_status: 'paid'
        }
      },
      livemode: false,
      pending_webhooks: 1,
      request: { id: 'req_direct_test', idempotency_key: null },
      type: 'checkout.session.completed'
    };

    console.log('\n📤 Sending webhook with metadata:');
    console.log('   order_id:', webhookEvent.data.object.metadata.order_id);
    console.log('   customer_email:', webhookEvent.data.object.metadata.customer_email);

    // Create signature
    const timestamp = Math.floor(Date.now() / 1000);
    const payloadString = JSON.stringify(webhookEvent);
    const signedPayload = `${timestamp}.${payloadString}`;
    const signature = crypto
      .createHmac('sha256', WEBHOOK_SECRET)
      .update(signedPayload, 'utf8')
      .digest('hex');
    const stripeSignature = `t=${timestamp},v1=${signature}`;

    // Send to webhook
    const response = await fetch('http://localhost:3000/api/stripe/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': stripeSignature
      },
      body: payloadString
    });

    console.log('\n📥 Webhook response:', response.status, response.statusText);
    
    if (response.ok) {
      const result = await response.json();
      console.log('   Response body:', result);
    } else {
      const errorText = await response.text();
      console.log('   Error:', errorText);
    }

    // Check order status
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait a bit
    
    const { data: finalOrder } = await supabase
      .from('orders')
      .select('*')
      .eq('id', order.id)
      .single();

    console.log('\n📊 Final order status:');
    console.log('   ID:', finalOrder.id);
    console.log('   Status:', finalOrder.status);
    console.log('   Payment Status:', finalOrder.payment_status);
    console.log('   Payment Intent:', finalOrder.payment_intent);
    console.log('   Updated At:', finalOrder.updated_at);

    if (finalOrder.status === 'pending' && finalOrder.payment_status === 'completed') {
      console.log('\n✅ SUCCESS: Webhook updated order correctly!');
    } else {
      console.log('\n❌ FAILED: Order not updated by webhook');
    }

    // Cleanup
    await supabase.from('orders').delete().eq('id', order.id);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testWebhookDirect();