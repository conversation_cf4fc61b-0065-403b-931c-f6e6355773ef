/**
 * Live webhook endpoint test
 * Tests webhook endpoints with the actual running server
 * 
 * Run with: node test-webhook-endpoints-live.js
 */

const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Load all environment files
require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '.env.production' });

// Test configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;
const APP_URL = 'http://localhost:3002';

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY || !WEBHOOK_SECRET) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Test data
const TEST_USER_EMAIL = '<EMAIL>';

function logTest(testName, status, message = '') {
  const statusEmoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '🔄';
  console.log(`${statusEmoji} ${testName}: ${message}`);
}

function logSection(sectionName) {
  console.log(`\n=== ${sectionName} ===`);
}

// Create a valid Stripe signature
function createStripeSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const payloadString = JSON.stringify(payload);
  const signedPayload = `${timestamp}.${payloadString}`;
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return `t=${timestamp},v1=${signature}`;
}

async function createTestUserAndOrder() {
  try {
    // Create test user
    const { data: user, error: userError } = await supabase.auth.admin.createUser({
      email: TEST_USER_EMAIL,
      password: 'test-password-123',
      email_confirm: true
    });

    let testUser = user?.user;
    if (userError && userError.message.includes('already been registered')) {
      // Find existing user
      const users = await supabase.auth.admin.listUsers();
      testUser = users.data.users.find(u => u.email === TEST_USER_EMAIL);
    } else if (userError) {
      throw userError;
    }

    if (!testUser) {
      throw new Error('Failed to create or find test user');
    }

    // Ensure profile exists
    await supabase
      .from('profiles')
      .upsert({
        id: testUser.id,
        email: testUser.email,
        full_name: 'Webhook Test User'
      });

    // Create test order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert([
        {
          user_id: testUser.id,
          status: 'processing',
          total_amount: 25.00,
          payment_provider: 'stripe',
          payment_status: 'processing',
          customer_email: TEST_USER_EMAIL,
          session_id: 'cs_test_webhook_session_' + Date.now()
        }
      ])
      .select()
      .single();

    if (orderError) throw orderError;

    return { user: testUser, order };
    
  } catch (error) {
    console.error('Error creating test data:', error);
    return null;
  }
}

async function cleanupTestData(userId) {
  try {
    // Clean up test orders
    await supabase
      .from('orders')
      .delete()
      .eq('user_id', userId);

    // Clean up test profile
    await supabase
      .from('profiles')
      .delete()
      .eq('id', userId);

    // Clean up test user
    await supabase.auth.admin.deleteUser(userId);
  } catch (error) {
    console.warn('Cleanup warning:', error.message);
  }
}

async function testSuccessfulPaymentWebhook(order) {
  logSection('TEST 1: Successful Payment Webhook');
  
  try {
    const webhookEvent = {
      id: 'evt_test_success_' + Date.now(),
      object: 'event',
      api_version: '2025-02-24.acacia',
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: order.session_id,
          object: 'checkout.session',
          amount_total: 2500, // $25.00 in cents
          customer_details: {
            email: TEST_USER_EMAIL
          },
          metadata: {
            order_id: order.id,
            customer_email: TEST_USER_EMAIL
          },
          payment_intent: 'pi_test_webhook_' + Date.now(),
          payment_method_types: ['card'],
          payment_status: 'paid',
          shipping_details: {
            name: 'Webhook Test Customer',
            address: {
              line1: '123 Webhook St',
              city: 'Test City',
              state: 'TC',
              postal_code: '12345',
              country: 'US'
            }
          }
        }
      },
      livemode: false,
      pending_webhooks: 1,
      request: {
        id: 'req_test_webhook_' + Date.now(),
        idempotency_key: null
      },
      type: 'checkout.session.completed'
    };

    const signature = createStripeSignature(webhookEvent, WEBHOOK_SECRET);
    
    const response = await fetch(`${APP_URL}/api/stripe/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': signature
      },
      body: JSON.stringify(webhookEvent)
    });
    
    const responseText = await response.text();
    
    if (response.ok) {
      // Check if order status was updated
      const { data: updatedOrder } = await supabase
        .from('orders')
        .select('status, payment_status, payment_intent')
        .eq('id', order.id)
        .single();

      if (updatedOrder && updatedOrder.status === 'pending' && updatedOrder.payment_status === 'completed') {
        logTest('Successful Payment Webhook', 'PASS', 
          `Order updated: ${order.status} → ${updatedOrder.status}, ` +
          `Payment: ${order.payment_status} → ${updatedOrder.payment_status}`);
        return true;
      } else {
        logTest('Successful Payment Webhook', 'FAIL', 
          `Expected: pending/completed, Got: ${updatedOrder?.status}/${updatedOrder?.payment_status}`);
        return false;
      }
    } else {
      logTest('Successful Payment Webhook', 'FAIL', `HTTP ${response.status}: ${responseText}`);
      return false;
    }
    
  } catch (error) {
    logTest('Successful Payment Webhook', 'FAIL', error.message);
    return false;
  }
}

async function testFailedPaymentWebhook(order) {
  logSection('TEST 2: Failed Payment Webhook');
  
  try {
    // Reset order to processing status
    await supabase
      .from('orders')
      .update({
        status: 'processing',
        payment_status: 'processing'
      })
      .eq('id', order.id);

    const webhookEvent = {
      id: 'evt_test_expired_' + Date.now(),
      object: 'event',
      api_version: '2025-02-24.acacia',
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: order.session_id,
          object: 'checkout.session',
          metadata: {
            order_id: order.id
          }
        }
      },
      livemode: false,
      pending_webhooks: 1,
      request: {
        id: 'req_test_expired_' + Date.now(),
        idempotency_key: null
      },
      type: 'checkout.session.expired'
    };

    const signature = createStripeSignature(webhookEvent, WEBHOOK_SECRET);
    
    const response = await fetch(`${APP_URL}/api/stripe/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': signature
      },
      body: JSON.stringify(webhookEvent)
    });
    
    const responseText = await response.text();
    
    if (response.ok) {
      // Check if order status was updated
      const { data: updatedOrder } = await supabase
        .from('orders')
        .select('status, payment_status')
        .eq('id', order.id)
        .single();

      if (updatedOrder && updatedOrder.status === 'cancelled' && updatedOrder.payment_status === 'failed') {
        logTest('Failed Payment Webhook', 'PASS', 
          `Order updated: processing → ${updatedOrder.status}, ` +
          `Payment: processing → ${updatedOrder.payment_status}`);
        return true;
      } else {
        logTest('Failed Payment Webhook', 'FAIL', 
          `Expected: cancelled/failed, Got: ${updatedOrder?.status}/${updatedOrder?.payment_status}`);
        return false;
      }
    } else {
      logTest('Failed Payment Webhook', 'FAIL', `HTTP ${response.status}: ${responseText}`);
      return false;
    }
    
  } catch (error) {
    logTest('Failed Payment Webhook', 'FAIL', error.message);
    return false;
  }
}

async function testInvalidSignature() {
  logSection('TEST 3: Invalid Signature Rejection');
  
  try {
    const fakeEvent = { id: 'fake_event', type: 'fake_type' };
    
    const response = await fetch(`${APP_URL}/api/stripe/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': 'invalid-signature'
      },
      body: JSON.stringify(fakeEvent)
    });
    
    if (response.status === 400) {
      logTest('Invalid Signature Rejection', 'PASS', 'Correctly rejected invalid signature');
      return true;
    } else {
      logTest('Invalid Signature Rejection', 'FAIL', `Expected 400, got ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logTest('Invalid Signature Rejection', 'FAIL', error.message);
    return false;
  }
}

async function runWebhookTests() {
  console.log('🌐 Live Webhook Endpoint Testing');
  console.log('=================================');
  console.log('Testing Stripe webhooks with actual server on http://localhost:3002\n');
  
  const testResults = [];
  let testData = null;
  
  // Create test data
  logSection('SETUP: Creating Test Data');
  testData = await createTestUserAndOrder();
  
  if (!testData) {
    console.log('❌ Failed to create test data. Cannot proceed with webhook tests.');
    process.exit(1);
  }
  
  console.log(`✅ Created test order: ${testData.order.id} (status: ${testData.order.status})`);
  
  // Test 1: Successful Payment Webhook
  const test1Result = await testSuccessfulPaymentWebhook(testData.order);
  testResults.push(test1Result);
  
  // Test 2: Failed Payment Webhook
  const test2Result = await testFailedPaymentWebhook(testData.order);
  testResults.push(test2Result);
  
  // Test 3: Invalid Signature
  const test3Result = await testInvalidSignature();
  testResults.push(test3Result);
  
  // Cleanup
  try {
    await cleanupTestData(testData.user.id);
    console.log('\n🧹 Test data cleaned up');
  } catch (error) {
    console.log('⚠️  Cleanup warning:', error.message);
  }
  
  // Summary
  logSection('WEBHOOK TEST SUMMARY');
  const passed = testResults.filter(Boolean).length;
  const total = testResults.length;
  
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 ALL WEBHOOK TESTS PASSED!');
    console.log('\n✅ Payment Flow Bug Status: FIXED');
    console.log('   • Successful payments update orders to "pending" (safe to ship)');
    console.log('   • Failed payments update orders to "cancelled" (not "pending")');
    console.log('   • Webhook security is working correctly');
    console.log('   • No risk of shipping unpaid orders!');
  } else {
    console.log('\n⚠️  Some webhook tests failed. Review the issues above.');
  }
  
  process.exit(passed === total ? 0 : 1);
}

// Run the webhook tests
runWebhookTests().catch(error => {
  console.error('❌ Webhook test suite failed:', error);
  process.exit(1);
});